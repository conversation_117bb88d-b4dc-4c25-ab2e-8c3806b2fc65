-- YalaOffice Database Update Script
-- Run this script in your Supabase SQL Editor to add the status column

-- 1. Add status column to product_reviews table
ALTER TABLE product_reviews 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'approved' 
CHECK (status IN ('pending', 'approved', 'rejected'));

-- 2. Update existing reviews to have 'approved' status (since they were visible before)
UPDATE product_reviews 
SET status = 'approved' 
WHERE status IS NULL;

-- 3. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_product_reviews_status ON product_reviews(status);
CREATE INDEX IF NOT EXISTS idx_product_reviews_product_status ON product_reviews(product_id, status);

-- 4. Add comment to document the column
COMMENT ON COLUMN product_reviews.status IS 'Review approval status: pending (awaiting approval), approved (visible to public), rejected (hidden from public)';

-- 5. Create review_replies table for admin responses
CREATE TABLE IF NOT EXISTS review_replies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    review_id UUID NOT NULL REFERENCES product_reviews(id) ON DELETE CASCADE,
    admin_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reply_text TEXT NOT NULL CHECK (length(reply_text) >= 10 AND length(reply_text) <= 2000),
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Create indexes for review_replies
CREATE INDEX IF NOT EXISTS idx_review_replies_review_id ON review_replies(review_id);
CREATE INDEX IF NOT EXISTS idx_review_replies_admin_id ON review_replies(admin_id);
CREATE INDEX IF NOT EXISTS idx_review_replies_created_at ON review_replies(created_at DESC);

-- 7. Verify the changes
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'product_reviews'
ORDER BY ordinal_position;
