-- YalaOffice Promo Codes Management Database Schema
-- This script creates the promo_codes table and related structures

-- Create promo_codes table
CREATE TABLE IF NOT EXISTS promo_codes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Basic Information
    code VARCHAR(50) UNIQUE NOT NULL,
    name VA<PERSON>HAR(255) NOT NULL,
    description TEXT,
    
    -- Discount Configuration
    discount_type VARCHAR(20) NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount')),
    discount_value DECIMAL(10,2) NOT NULL CHECK (discount_value > 0),
    
    -- Usage Limits
    usage_limit_total INTEGER DEFAULT NULL, -- NULL = unlimited
    usage_limit_per_customer INTEGER DEFAULT 1,
    current_usage_count INTEGER DEFAULT 0,
    
    -- Validity Period
    valid_from TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    valid_until TIMESTAMP WITH TIME ZONE DEFAULT NULL, -- NULL = no expiry
    
    -- Order Requirements
    minimum_order_amount DECIMAL(10,2) DEFAULT 0,
    maximum_discount_amount DECIMAL(10,2) DEFAULT NULL, -- NULL = no maximum
    
    -- Restrictions
    applicable_to VARCHAR(20) DEFAULT 'all' CHECK (applicable_to IN ('all', 'categories', 'products')),
    restricted_categories JSONB DEFAULT '[]'::jsonb, -- Array of category IDs
    restricted_products JSONB DEFAULT '[]'::jsonb,   -- Array of product IDs
    
    -- Status and Metadata
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false, -- For highlighting special promos
    
    -- Audit Fields
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_promo_codes_code ON promo_codes(code);
CREATE INDEX IF NOT EXISTS idx_promo_codes_active ON promo_codes(is_active);
CREATE INDEX IF NOT EXISTS idx_promo_codes_valid_period ON promo_codes(valid_from, valid_until);
CREATE INDEX IF NOT EXISTS idx_promo_codes_discount_type ON promo_codes(discount_type);
CREATE INDEX IF NOT EXISTS idx_promo_codes_created_at ON promo_codes(created_at);
CREATE INDEX IF NOT EXISTS idx_promo_codes_usage ON promo_codes(current_usage_count, usage_limit_total);

-- Create promo_code_usage table to track individual usage
CREATE TABLE IF NOT EXISTS promo_code_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    promo_code_id UUID NOT NULL REFERENCES promo_codes(id) ON DELETE CASCADE,
    order_id UUID NOT NULL, -- References orders table
    user_id UUID REFERENCES auth.users(id),
    
    -- Usage Details
    original_amount DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) NOT NULL,
    final_amount DECIMAL(10,2) NOT NULL,
    
    -- Metadata
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT
);

-- Create indexes for promo_code_usage
CREATE INDEX IF NOT EXISTS idx_promo_code_usage_promo_code ON promo_code_usage(promo_code_id);
CREATE INDEX IF NOT EXISTS idx_promo_code_usage_order ON promo_code_usage(order_id);
CREATE INDEX IF NOT EXISTS idx_promo_code_usage_user ON promo_code_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_promo_code_usage_date ON promo_code_usage(used_at);

-- Create function to update usage count
CREATE OR REPLACE FUNCTION update_promo_code_usage_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE promo_codes 
        SET current_usage_count = current_usage_count + 1,
            updated_at = NOW()
        WHERE id = NEW.promo_code_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE promo_codes 
        SET current_usage_count = GREATEST(current_usage_count - 1, 0),
            updated_at = NOW()
        WHERE id = OLD.promo_code_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update usage count
DROP TRIGGER IF EXISTS trigger_update_promo_code_usage_count ON promo_code_usage;
CREATE TRIGGER trigger_update_promo_code_usage_count
    AFTER INSERT OR DELETE ON promo_code_usage
    FOR EACH ROW
    EXECUTE FUNCTION update_promo_code_usage_count();

-- Create function to check if promo code is valid
CREATE OR REPLACE FUNCTION is_promo_code_valid(
    p_code VARCHAR(50),
    p_user_id UUID DEFAULT NULL,
    p_order_amount DECIMAL(10,2) DEFAULT 0
)
RETURNS TABLE(
    is_valid BOOLEAN,
    error_message TEXT,
    promo_data JSONB
) AS $$
DECLARE
    promo_record promo_codes%ROWTYPE;
    user_usage_count INTEGER;
BEGIN
    -- Get promo code record
    SELECT * INTO promo_record FROM promo_codes WHERE code = p_code;
    
    -- Check if promo code exists
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'Promo code not found', NULL::JSONB;
        RETURN;
    END IF;
    
    -- Check if active
    IF NOT promo_record.is_active THEN
        RETURN QUERY SELECT false, 'Promo code is inactive', NULL::JSONB;
        RETURN;
    END IF;
    
    -- Check validity period
    IF promo_record.valid_from > NOW() THEN
        RETURN QUERY SELECT false, 'Promo code is not yet valid', NULL::JSONB;
        RETURN;
    END IF;
    
    IF promo_record.valid_until IS NOT NULL AND promo_record.valid_until < NOW() THEN
        RETURN QUERY SELECT false, 'Promo code has expired', NULL::JSONB;
        RETURN;
    END IF;
    
    -- Check total usage limit
    IF promo_record.usage_limit_total IS NOT NULL AND 
       promo_record.current_usage_count >= promo_record.usage_limit_total THEN
        RETURN QUERY SELECT false, 'Promo code usage limit reached', NULL::JSONB;
        RETURN;
    END IF;
    
    -- Check per-customer usage limit
    IF p_user_id IS NOT NULL THEN
        SELECT COUNT(*) INTO user_usage_count 
        FROM promo_code_usage 
        WHERE promo_code_id = promo_record.id AND user_id = p_user_id;
        
        IF user_usage_count >= promo_record.usage_limit_per_customer THEN
            RETURN QUERY SELECT false, 'You have reached the usage limit for this promo code', NULL::JSONB;
            RETURN;
        END IF;
    END IF;
    
    -- Check minimum order amount
    IF p_order_amount < promo_record.minimum_order_amount THEN
        RETURN QUERY SELECT false, 
            'Order amount must be at least ' || promo_record.minimum_order_amount::TEXT, 
            NULL::JSONB;
        RETURN;
    END IF;
    
    -- If all checks pass, return valid with promo data
    RETURN QUERY SELECT true, 'Valid'::TEXT, row_to_json(promo_record)::JSONB;
END;
$$ LANGUAGE plpgsql;

-- Create function to calculate discount amount
CREATE OR REPLACE FUNCTION calculate_promo_discount(
    p_code VARCHAR(50),
    p_order_amount DECIMAL(10,2)
)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    promo_record promo_codes%ROWTYPE;
    discount_amount DECIMAL(10,2);
BEGIN
    SELECT * INTO promo_record FROM promo_codes WHERE code = p_code AND is_active = true;
    
    IF NOT FOUND THEN
        RETURN 0;
    END IF;
    
    -- Calculate discount based on type
    IF promo_record.discount_type = 'percentage' THEN
        discount_amount := p_order_amount * (promo_record.discount_value / 100);
    ELSE -- fixed_amount
        discount_amount := promo_record.discount_value;
    END IF;
    
    -- Apply maximum discount limit if set
    IF promo_record.maximum_discount_amount IS NOT NULL THEN
        discount_amount := LEAST(discount_amount, promo_record.maximum_discount_amount);
    END IF;
    
    -- Ensure discount doesn't exceed order amount
    discount_amount := LEAST(discount_amount, p_order_amount);
    
    RETURN GREATEST(discount_amount, 0);
END;
$$ LANGUAGE plpgsql;

-- Insert sample promo codes for testing
INSERT INTO promo_codes (code, name, description, discount_type, discount_value, usage_limit_total, valid_until, minimum_order_amount, is_active, is_featured) VALUES
('WELCOME10', 'Welcome Discount', 'Get 10% off your first order', 'percentage', 10.00, 100, NOW() + INTERVAL '30 days', 50.00, true, true),
('SAVE50', 'Save 50 Dh', 'Get 50 Dh off orders over 200 Dh', 'fixed_amount', 50.00, NULL, NOW() + INTERVAL '60 days', 200.00, true, false),
('STUDENT15', 'Student Discount', '15% off for students', 'percentage', 15.00, 500, NOW() + INTERVAL '90 days', 30.00, true, true),
('BULK20', 'Bulk Order Discount', '20% off orders over 500 Dh', 'percentage', 20.00, 50, NOW() + INTERVAL '45 days', 500.00, true, false),
('EXPIRED', 'Expired Code', 'This code has expired', 'percentage', 25.00, 10, NOW() - INTERVAL '1 day', 0.00, true, false);

-- Enable Row Level Security (RLS) if needed
-- ALTER TABLE promo_codes ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE promo_code_usage ENABLE ROW LEVEL SECURITY;

-- Create policies for RLS (uncomment if needed)
-- CREATE POLICY "Allow all operations for authenticated users" ON promo_codes FOR ALL TO authenticated USING (true);
-- CREATE POLICY "Allow all operations for authenticated users" ON promo_code_usage FOR ALL TO authenticated USING (true);

COMMENT ON TABLE promo_codes IS 'Stores promo codes with discount rules and usage limits';
COMMENT ON TABLE promo_code_usage IS 'Tracks individual usage of promo codes';
COMMENT ON FUNCTION is_promo_code_valid IS 'Validates if a promo code can be used';
COMMENT ON FUNCTION calculate_promo_discount IS 'Calculates the discount amount for a promo code';
