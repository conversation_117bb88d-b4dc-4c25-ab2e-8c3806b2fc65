-- YalaOffice Reply System - Simple Fix
-- Run this script in your Supabase SQL Editor to fix the reply system

-- 1. Create the review_replies table if it doesn't exist
CREATE TABLE IF NOT EXISTS review_replies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    review_id UUID NOT NULL REFERENCES product_reviews(id) ON DELETE CASCADE,
    admin_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reply_text TEXT NOT NULL CHECK (length(reply_text) >= 10 AND length(reply_text) <= 2000),
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_review_replies_review_id ON review_replies(review_id);
CREATE INDEX IF NOT EXISTS idx_review_replies_admin_id ON review_replies(admin_id);

-- 3. Drop any existing policies that might be causing issues
DROP POLICY IF EXISTS "Anyone can read public replies" ON review_replies;
DROP POLICY IF EXISTS "Admins can insert replies" ON review_replies;
DROP POLICY IF EXISTS "Authenticated users can insert replies" ON review_replies;
DROP POLICY IF EXISTS "Users can update own replies" ON review_replies;
DROP POLICY IF EXISTS "Users can delete own replies" ON review_replies;
DROP POLICY IF EXISTS "Admins can update own replies" ON review_replies;
DROP POLICY IF EXISTS "Admins can delete own replies" ON review_replies;
DROP POLICY IF EXISTS "Allow all reads" ON review_replies;
DROP POLICY IF EXISTS "Allow all inserts" ON review_replies;
DROP POLICY IF EXISTS "Allow all updates" ON review_replies;
DROP POLICY IF EXISTS "Allow all deletes" ON review_replies;
DROP POLICY IF EXISTS "Allow all operations" ON review_replies;

-- 4. Disable RLS completely for development
ALTER TABLE review_replies DISABLE ROW LEVEL SECURITY;

-- 5. Test that the table is ready
SELECT 'review_replies table is ready for use!' as status;
