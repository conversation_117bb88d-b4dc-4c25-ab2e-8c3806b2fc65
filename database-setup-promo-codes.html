<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YalaOffice Promo Codes Database Setup</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #0d9488;
            border-bottom: 3px solid #0d9488;
            padding-bottom: 10px;
        }
        h2 {
            color: #374151;
            margin-top: 30px;
        }
        .step {
            background: #f0fdfa;
            border: 1px solid #5eead4;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .step h3 {
            color: #0d9488;
            margin-top: 0;
        }
        .sql-code {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            margin: 15px 0;
        }
        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background: #d1fae5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .copy-btn {
            background: #0d9488;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 10px;
        }
        .copy-btn:hover {
            background: #0f766e;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
        }
        .feature-card h4 {
            color: #0d9488;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏷️ YalaOffice Promo Codes Database Setup</h1>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> This setup creates the complete promo codes management system for YalaOffice. Make sure you have admin access to your Supabase project before proceeding.
        </div>

        <h2>📋 What This Setup Includes</h2>
        <div class="feature-list">
            <div class="feature-card">
                <h4>🏷️ Promo Codes Table</h4>
                <p>Complete promo code management with discount types, usage limits, and validity periods.</p>
            </div>
            <div class="feature-card">
                <h4>📊 Usage Tracking</h4>
                <p>Track individual promo code usage with detailed analytics and reporting.</p>
            </div>
            <div class="feature-card">
                <h4>🔧 Database Functions</h4>
                <p>Validation functions, discount calculation, and automatic usage counting.</p>
            </div>
            <div class="feature-card">
                <h4>🎯 Sample Data</h4>
                <p>Pre-loaded sample promo codes for testing and demonstration.</p>
            </div>
        </div>

        <div class="step">
            <h3>Step 1: Access Supabase SQL Editor</h3>
            <ol>
                <li>Go to your <strong>Supabase Dashboard</strong></li>
                <li>Select your YalaOffice project</li>
                <li>Navigate to <strong>SQL Editor</strong> in the left sidebar</li>
                <li>Click <strong>"New Query"</strong></li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 2: Execute the Complete Schema</h3>
            <p>Copy and paste the following SQL script into the Supabase SQL Editor:</p>
            
            <div class="sql-code" id="schema-sql">-- YalaOffice Promo Codes Management Database Schema
-- Execute this script in Supabase SQL Editor

-- Create promo_codes table
CREATE TABLE IF NOT EXISTS promo_codes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Basic Information
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Discount Configuration
    discount_type VARCHAR(20) NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount')),
    discount_value DECIMAL(10,2) NOT NULL CHECK (discount_value > 0),
    
    -- Usage Limits
    usage_limit_total INTEGER DEFAULT NULL, -- NULL = unlimited
    usage_limit_per_customer INTEGER DEFAULT 1,
    current_usage_count INTEGER DEFAULT 0,
    
    -- Validity Period
    valid_from TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    valid_until TIMESTAMP WITH TIME ZONE DEFAULT NULL, -- NULL = no expiry
    
    -- Order Requirements
    minimum_order_amount DECIMAL(10,2) DEFAULT 0,
    maximum_discount_amount DECIMAL(10,2) DEFAULT NULL, -- NULL = no maximum
    
    -- Restrictions
    applicable_to VARCHAR(20) DEFAULT 'all' CHECK (applicable_to IN ('all', 'categories', 'products')),
    restricted_categories JSONB DEFAULT '[]'::jsonb,
    restricted_products JSONB DEFAULT '[]'::jsonb,
    
    -- Status and Metadata
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    
    -- Audit Fields
    created_by UUID,
    updated_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_promo_codes_code ON promo_codes(code);
CREATE INDEX IF NOT EXISTS idx_promo_codes_active ON promo_codes(is_active);
CREATE INDEX IF NOT EXISTS idx_promo_codes_valid_period ON promo_codes(valid_from, valid_until);
CREATE INDEX IF NOT EXISTS idx_promo_codes_discount_type ON promo_codes(discount_type);
CREATE INDEX IF NOT EXISTS idx_promo_codes_created_at ON promo_codes(created_at);
CREATE INDEX IF NOT EXISTS idx_promo_codes_usage ON promo_codes(current_usage_count, usage_limit_total);

-- Create promo_code_usage table
CREATE TABLE IF NOT EXISTS promo_code_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    promo_code_id UUID NOT NULL REFERENCES promo_codes(id) ON DELETE CASCADE,
    order_id UUID NOT NULL,
    user_id UUID,
    
    -- Usage Details
    original_amount DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) NOT NULL,
    final_amount DECIMAL(10,2) NOT NULL,
    
    -- Metadata
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT
);

-- Create indexes for promo_code_usage
CREATE INDEX IF NOT EXISTS idx_promo_code_usage_promo_code ON promo_code_usage(promo_code_id);
CREATE INDEX IF NOT EXISTS idx_promo_code_usage_order ON promo_code_usage(order_id);
CREATE INDEX IF NOT EXISTS idx_promo_code_usage_user ON promo_code_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_promo_code_usage_date ON promo_code_usage(used_at);

-- Insert sample promo codes for testing
INSERT INTO promo_codes (code, name, description, discount_type, discount_value, usage_limit_total, valid_until, minimum_order_amount, is_active, is_featured) VALUES
('WELCOME10', 'Welcome Discount', 'Get 10% off your first order', 'percentage', 10.00, 100, NOW() + INTERVAL '30 days', 50.00, true, true),
('SAVE50', 'Save 50 MAD', 'Get 50 MAD off orders over 200 MAD', 'fixed_amount', 50.00, NULL, NOW() + INTERVAL '60 days', 200.00, true, false),
('STUDENT15', 'Student Discount', '15% off for students', 'percentage', 15.00, 500, NOW() + INTERVAL '90 days', 30.00, true, true),
('BULK20', 'Bulk Order Discount', '20% off orders over 500 MAD', 'percentage', 20.00, 50, NOW() + INTERVAL '45 days', 500.00, true, false),
('EXPIRED', 'Expired Code', 'This code has expired', 'percentage', 25.00, 10, NOW() - INTERVAL '1 day', 0.00, true, false)
ON CONFLICT (code) DO NOTHING;</div>
            
            <button class="copy-btn" onclick="copyToClipboard('schema-sql')">📋 Copy SQL Script</button>
        </div>

        <div class="step">
            <h3>Step 3: Execute the Script</h3>
            <ol>
                <li>Paste the SQL script into the Supabase SQL Editor</li>
                <li>Click <strong>"Run"</strong> to execute the script</li>
                <li>Wait for the success message</li>
                <li>Verify the tables were created in the <strong>Table Editor</strong></li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 4: Verify the Setup</h3>
            <p>After running the script, you should see:</p>
            <ul>
                <li>✅ <strong>promo_codes</strong> table with sample data</li>
                <li>✅ <strong>promo_code_usage</strong> table (empty initially)</li>
                <li>✅ All indexes created successfully</li>
                <li>✅ 5 sample promo codes inserted</li>
            </ul>
        </div>

        <div class="success">
            <strong>🎉 Setup Complete!</strong> Your YalaOffice Promo Codes system is now ready. You can access it through the Admin Dashboard → Promo Codes Management.
        </div>

        <h2>🔧 Database Schema Details</h2>
        
        <h3>Promo Codes Table Structure</h3>
        <div class="sql-code">
-- Key columns in promo_codes table:
- id: UUID primary key
- code: Unique promo code (e.g., "WELCOME10")
- name: Display name
- discount_type: 'percentage' or 'fixed_amount'
- discount_value: Discount amount/percentage
- usage_limit_total: Maximum total uses (NULL = unlimited)
- usage_limit_per_customer: Max uses per customer
- current_usage_count: Current usage count (auto-updated)
- valid_from/valid_until: Validity period
- minimum_order_amount: Minimum order requirement
- is_active: Enable/disable status
- is_featured: Featured promo flag
        </div>

        <h3>Sample Promo Codes Created</h3>
        <div class="feature-list">
            <div class="feature-card">
                <h4>WELCOME10</h4>
                <p><strong>10% off</strong> first orders over 50 MAD<br>
                <em>Featured • 100 uses limit • 30 days</em></p>
            </div>
            <div class="feature-card">
                <h4>SAVE50</h4>
                <p><strong>50 MAD off</strong> orders over 200 MAD<br>
                <em>Unlimited uses • 60 days</em></p>
            </div>
            <div class="feature-card">
                <h4>STUDENT15</h4>
                <p><strong>15% off</strong> for students over 30 MAD<br>
                <em>Featured • 500 uses limit • 90 days</em></p>
            </div>
            <div class="feature-card">
                <h4>BULK20</h4>
                <p><strong>20% off</strong> bulk orders over 500 MAD<br>
                <em>50 uses limit • 45 days</em></p>
            </div>
        </div>

        <div class="warning">
            <strong>💡 Next Steps:</strong>
            <ul>
                <li>Navigate to Admin Dashboard → Promo Codes Management</li>
                <li>Test the promo code functionality</li>
                <li>Create your own custom promo codes</li>
                <li>Monitor usage statistics and analytics</li>
            </ul>
        </div>
    </div>

    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                const btn = element.nextElementSibling;
                const originalText = btn.textContent;
                btn.textContent = '✅ Copied!';
                btn.style.background = '#10b981';
                
                setTimeout(function() {
                    btn.textContent = originalText;
                    btn.style.background = '#0d9488';
                }, 2000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                alert('Please manually copy the SQL script');
            });
        }
    </script>
</body>
</html>
