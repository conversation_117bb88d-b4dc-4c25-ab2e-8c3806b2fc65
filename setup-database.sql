-- YalaOffice Database Setup Script
-- This script creates the missing tables required for System Settings functionality

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create company_settings table
CREATE TABLE IF NOT EXISTS company_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(50),
    email VARCHAR(255),
    tax_id VARCHAR(100),
    ice_number VARCHAR(100),
    logo_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create system_configs table
CREATE TABLE IF NOT EXISTS system_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category VARCHAR(100) NOT NULL,
    key VARCHAR(100) NOT NULL,
    value TEXT,
    description TEXT,
    data_type VARCHAR(50) DEFAULT 'string',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    updated_by UUID,
    UNIQUE(category, key)
);

-- Create payment_methods_config table
CREATE TABLE IF NOT EXISTS payment_methods_config (
    id VARCHAR(50) PRIMARY KEY,
    method_type VARCHAR(50) NOT NULL,
    is_enabled BOOLEAN DEFAULT true,
    configuration JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default company settings
INSERT INTO company_settings (id, name, address, phone, email, tax_id, ice_number, created_at) 
VALUES ('550e8400-e29b-41d4-a716-************', 'YalaOffice Morocco', 'Casablanca, Morocco', '+212 5XX-XXX-XXX', '<EMAIL>', 'TAX-********', 'ICE-********', NOW())
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    address = EXCLUDED.address,
    phone = EXCLUDED.phone,
    email = EXCLUDED.email,
    tax_id = EXCLUDED.tax_id,
    ice_number = EXCLUDED.ice_number,
    updated_at = NOW();

-- Insert default payment methods configuration
INSERT INTO payment_methods_config (id, method_type, is_enabled, configuration, created_at) VALUES
('bank-transfer', 'bank_transfer', true, '{"bankName": "Banque Populaire", "accountNumber": "**********", "iban": "MA64 0123 4567 8901 2345 6789 01", "swiftCode": "BMCEMAMC", "accountHolder": "YalaOffice SARL"}', NOW()),
('check', 'check', true, '{"payableTo": "YalaOffice SARL", "mailingAddress": "Casablanca, Morocco", "processingTime": "3-5 business days"}', NOW()),
('cash', 'cash', true, '{"enabled": true}', NOW())
ON CONFLICT (id) DO UPDATE SET
    method_type = EXCLUDED.method_type,
    is_enabled = EXCLUDED.is_enabled,
    configuration = EXCLUDED.configuration,
    updated_at = NOW();

-- Insert default system configurations
INSERT INTO system_configs (category, key, value, description, data_type) VALUES
-- General settings
('general', 'site_name', '"YalaOffice Morocco"', 'Site name', 'string'),
('general', 'site_description', '"Professional Office & School Supplies"', 'Site description', 'string'),
('general', 'timezone', '"Africa/Casablanca"', 'System timezone', 'string'),
('general', 'session_timeout', '30', 'Session timeout in minutes', 'number'),
('general', 'file_upload_size_limit', '10', 'File upload size limit in MB', 'number'),
('general', 'maintenance_mode', 'false', 'Enable maintenance mode', 'boolean'),
('general', 'backup_frequency', '"daily"', 'Backup frequency', 'string'),

-- Security settings
('security', 'password_min_length', '8', 'Minimum password length', 'number'),
('security', 'max_login_attempts', '5', 'Maximum login attempts', 'number'),
('security', 'session_security_strict', 'false', 'Enable strict session security', 'boolean'),
('security', 'two_factor_auth_enabled', 'false', 'Enable two-factor authentication', 'boolean'),
('security', 'password_expiry_days', '90', 'Password expiry in days', 'number'),

-- Notification settings
('notifications', 'email_notifications', 'true', 'Enable email notifications', 'boolean'),
('notifications', 'sms_notifications', 'false', 'Enable SMS notifications', 'boolean'),
('notifications', 'push_notifications', 'true', 'Enable push notifications', 'boolean'),
('notifications', 'order_notifications', 'true', 'Enable order notifications', 'boolean'),
('notifications', 'inventory_notifications', 'true', 'Enable inventory notifications', 'boolean'),
('notifications', 'user_notifications', 'true', 'Enable user notifications', 'boolean'),

-- Email settings
('email', 'smtp_host', '""', 'SMTP host', 'string'),
('email', 'smtp_port', '587', 'SMTP port', 'number'),
('email', 'smtp_username', '""', 'SMTP username', 'string'),
('email', 'smtp_password', '""', 'SMTP password', 'string'),
('email', 'smtp_secure', 'true', 'Use secure SMTP', 'boolean'),
('email', 'from_email', '"<EMAIL>"', 'From email address', 'string'),
('email', 'from_name', '"YalaOffice Morocco"', 'From name', 'string'),
('email', 'test_email', '""', 'Test email address', 'string')

ON CONFLICT (category, key) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    data_type = EXCLUDED.data_type,
    updated_at = NOW();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_system_configs_category ON system_configs(category);
CREATE INDEX IF NOT EXISTS idx_system_configs_key ON system_configs(key);
CREATE INDEX IF NOT EXISTS idx_payment_methods_type ON payment_methods_config(method_type);

-- Grant necessary permissions (adjust as needed for your setup)
-- These might need to be adjusted based on your Supabase RLS policies
-- ALTER TABLE company_settings ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE system_configs ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE payment_methods_config ENABLE ROW LEVEL SECURITY;

-- Success message
SELECT 'YalaOffice database setup completed successfully!' as status;
