-- YalaOffice Reply System Database Script
-- Run this script in your Supabase SQL Editor to add the reply functionality
-- Note: Status column already exists, so we only need to create the reply table

-- 1. Create review_replies table for admin responses
CREATE TABLE IF NOT EXISTS review_replies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    review_id UUID NOT NULL REFERENCES product_reviews(id) ON DELETE CASCADE,
    admin_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reply_text TEXT NOT NULL CHECK (length(reply_text) >= 10 AND length(reply_text) <= 2000),
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create indexes for review_replies
CREATE INDEX IF NOT EXISTS idx_review_replies_review_id ON review_replies(review_id);
CREATE INDEX IF NOT EXISTS idx_review_replies_admin_id ON review_replies(admin_id);
CREATE INDEX IF NOT EXISTS idx_review_replies_created_at ON review_replies(created_at DESC);

-- 3. Add RLS (Row Level Security) policies
ALTER TABLE review_replies ENABLE ROW LEVEL SECURITY;

-- Policy: Anyone can read public replies
CREATE POLICY "Anyone can read public replies" ON review_replies
    FOR SELECT USING (is_public = true);

-- Policy: Authenticated users can insert replies (simplified for development)
CREATE POLICY "Authenticated users can insert replies" ON review_replies
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- Policy: Users can update their own replies
CREATE POLICY "Users can update own replies" ON review_replies
    FOR UPDATE USING (admin_id = auth.uid());

-- Policy: Users can delete their own replies
CREATE POLICY "Users can delete own replies" ON review_replies
    FOR DELETE USING (admin_id = auth.uid());

-- 4. Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_review_replies_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 5. Create trigger for updated_at
CREATE TRIGGER update_review_replies_updated_at_trigger
    BEFORE UPDATE ON review_replies
    FOR EACH ROW
    EXECUTE FUNCTION update_review_replies_updated_at();

-- 6. Add comments for documentation
COMMENT ON TABLE review_replies IS 'Admin replies to customer product reviews';
COMMENT ON COLUMN review_replies.review_id IS 'Reference to the original product review';
COMMENT ON COLUMN review_replies.admin_id IS 'Admin user who wrote the reply';
COMMENT ON COLUMN review_replies.reply_text IS 'The reply content (10-2000 characters)';
COMMENT ON COLUMN review_replies.is_public IS 'Whether the reply is visible to customers';

-- 7. Verify the changes
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'review_replies' 
ORDER BY ordinal_position;
