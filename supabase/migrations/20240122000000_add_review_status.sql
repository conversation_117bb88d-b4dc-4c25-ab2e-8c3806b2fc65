-- Add status column to product_reviews table
ALTER TABLE product_reviews 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'pending' 
CHECK (status IN ('pending', 'approved', 'rejected'));

-- Update existing reviews to have 'approved' status (since they were visible before)
UPDATE product_reviews 
SET status = 'approved' 
WHERE status IS NULL;

-- Create index for better performance on status queries
CREATE INDEX IF NOT EXISTS idx_product_reviews_status ON product_reviews(status);

-- Create index for product_id + status combination
CREATE INDEX IF NOT EXISTS idx_product_reviews_product_status ON product_reviews(product_id, status);

-- Add comment to document the column
COMMENT ON COLUMN product_reviews.status IS 'Review approval status: pending (awaiting approval), approved (visible to public), rejected (hidden from public)';
