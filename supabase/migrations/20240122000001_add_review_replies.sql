-- Create review_replies table for admin responses to customer reviews
CREATE TABLE IF NOT EXISTS review_replies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    review_id UUID NOT NULL REFERENCES product_reviews(id) ON DELETE CASCADE,
    admin_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reply_text TEXT NOT NULL CHECK (length(reply_text) >= 10 AND length(reply_text) <= 2000),
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_review_replies_review_id ON review_replies(review_id);
CREATE INDEX IF NOT EXISTS idx_review_replies_admin_id ON review_replies(admin_id);
CREATE INDEX IF NOT EXISTS idx_review_replies_created_at ON review_replies(created_at DESC);

-- Add RLS (Row Level Security) policies
ALTER TABLE review_replies ENABLE ROW LEVEL SECURITY;

-- Policy: Anyone can read public replies
CREATE POLICY "Anyone can read public replies" ON review_replies
    FOR SELECT USING (is_public = true);

-- Policy: Admins and store managers can insert replies
CREATE POLICY "Admins can insert replies" ON review_replies
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'store_manager')
        )
    );

-- Policy: Admins can update their own replies
CREATE POLICY "Admins can update own replies" ON review_replies
    FOR UPDATE USING (
        admin_id = auth.uid() 
        AND EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'store_manager')
        )
    );

-- Policy: Admins can delete their own replies
CREATE POLICY "Admins can delete own replies" ON review_replies
    FOR DELETE USING (
        admin_id = auth.uid() 
        AND EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'store_manager')
        )
    );

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_review_replies_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER update_review_replies_updated_at_trigger
    BEFORE UPDATE ON review_replies
    FOR EACH ROW
    EXECUTE FUNCTION update_review_replies_updated_at();

-- Add comments for documentation
COMMENT ON TABLE review_replies IS 'Admin replies to customer product reviews';
COMMENT ON COLUMN review_replies.review_id IS 'Reference to the original product review';
COMMENT ON COLUMN review_replies.admin_id IS 'Admin user who wrote the reply';
COMMENT ON COLUMN review_replies.reply_text IS 'The reply content (10-2000 characters)';
COMMENT ON COLUMN review_replies.is_public IS 'Whether the reply is visible to customers';
COMMENT ON COLUMN review_replies.created_at IS 'When the reply was created';
COMMENT ON COLUMN review_replies.updated_at IS 'When the reply was last updated';
