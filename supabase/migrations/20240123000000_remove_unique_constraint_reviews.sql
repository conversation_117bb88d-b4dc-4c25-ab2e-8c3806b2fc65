-- Remove unique constraint to allow multiple reviews per user per product
-- This migration enables customers to submit multiple reviews for the same product

-- First, check if the constraint exists and drop it
DO $$ 
BEGIN
    -- Drop the unique constraint if it exists
    IF EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints 
        WHERE constraint_name = 'product_reviews_product_id_customer_id_key' 
        AND table_name = 'product_reviews'
    ) THEN
        ALTER TABLE product_reviews DROP CONSTRAINT product_reviews_product_id_customer_id_key;
        RAISE NOTICE 'Unique constraint product_reviews_product_id_customer_id_key dropped successfully';
    ELSE
        RAISE NOTICE 'Unique constraint product_reviews_product_id_customer_id_key does not exist';
    END IF;
END $$;

-- Add comment to document the change
COMMENT ON TABLE product_reviews IS 'Product reviews table - allows multiple reviews per user per product to enable comprehensive feedback collection';

-- Create indexes for performance (since we removed the unique constraint)
CREATE INDEX IF NOT EXISTS idx_product_reviews_product_customer ON product_reviews(product_id, customer_id);
CREATE INDEX IF NOT EXISTS idx_product_reviews_customer_created ON product_reviews(customer_id, created_at DESC);

-- Add comment to document the indexes
COMMENT ON INDEX idx_product_reviews_product_customer IS 'Index for efficient querying of reviews by product and customer';
COMMENT ON INDEX idx_product_reviews_customer_created IS 'Index for efficient querying of customer reviews by date';
