<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YalaOffice Database Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #0d9488;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .step {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #0d9488;
        }
        .step h3 {
            margin-top: 0;
            color: #0d9488;
        }
        button {
            background: #0d9488;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #0f766e;
        }
        button:disabled {
            background: #6b7280;
            cursor: not-allowed;
        }
        .copy-btn {
            background: #f59e0b;
            font-size: 12px;
            padding: 5px 10px;
        }
        .copy-btn:hover {
            background: #d97706;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ YalaOffice Database Setup</h1>
        
        <div class="alert alert-error">
            <strong>⚠️ Database Tables Missing!</strong><br>
            The error <code>relation "public.company_settings" does not exist</code> indicates that the required database tables haven't been created yet.
        </div>

        <div class="alert alert-info">
            <strong>📋 What This Setup Does:</strong><br>
            This setup will create the missing database tables required for System Settings functionality:
            <ul>
                <li><strong>company_settings</strong> - Stores company information</li>
                <li><strong>system_configs</strong> - Stores all system configuration settings</li>
                <li><strong>payment_methods_config</strong> - Stores payment method configurations</li>
            </ul>
        </div>

        <div class="step">
            <h3>📝 Step 1: Copy the SQL Script</h3>
            <p>Copy the SQL script below to your clipboard:</p>
            <button class="copy-btn" onclick="copyToClipboard('sql-script')">📋 Copy SQL Script</button>
            <div class="code-block" id="sql-script">-- YalaOffice Database Setup Script
-- This script creates the missing tables required for System Settings functionality

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create company_settings table
CREATE TABLE IF NOT EXISTS company_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(50),
    email VARCHAR(255),
    tax_id VARCHAR(100),
    ice_number VARCHAR(100),
    logo_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create system_configs table
CREATE TABLE IF NOT EXISTS system_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category VARCHAR(100) NOT NULL,
    key VARCHAR(100) NOT NULL,
    value TEXT,
    description TEXT,
    data_type VARCHAR(50) DEFAULT 'string',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    updated_by UUID,
    UNIQUE(category, key)
);

-- Create payment_methods_config table
CREATE TABLE IF NOT EXISTS payment_methods_config (
    id VARCHAR(50) PRIMARY KEY,
    method_type VARCHAR(50) NOT NULL,
    is_enabled BOOLEAN DEFAULT true,
    configuration JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default company settings
INSERT INTO company_settings (id, name, address, phone, email, tax_id, ice_number, created_at) 
VALUES ('550e8400-e29b-41d4-a716-************', 'YalaOffice Morocco', 'Casablanca, Morocco', '+212 5XX-XXX-XXX', '<EMAIL>', 'TAX-********', 'ICE-********', NOW())
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    address = EXCLUDED.address,
    phone = EXCLUDED.phone,
    email = EXCLUDED.email,
    tax_id = EXCLUDED.tax_id,
    ice_number = EXCLUDED.ice_number,
    updated_at = NOW();

-- Insert default payment methods configuration
INSERT INTO payment_methods_config (id, method_type, is_enabled, configuration, created_at) VALUES
('bank-transfer', 'bank_transfer', true, '{"bankName": "Banque Populaire", "accountNumber": "********90", "iban": "MA64 0123 4567 8901 2345 6789 01", "swiftCode": "BMCEMAMC", "accountHolder": "YalaOffice SARL"}', NOW()),
('check', 'check', true, '{"payableTo": "YalaOffice SARL", "mailingAddress": "Casablanca, Morocco", "processingTime": "3-5 business days"}', NOW()),
('cash', 'cash', true, '{"enabled": true}', NOW())
ON CONFLICT (id) DO UPDATE SET
    method_type = EXCLUDED.method_type,
    is_enabled = EXCLUDED.is_enabled,
    configuration = EXCLUDED.configuration,
    updated_at = NOW();

-- Insert default system configurations
INSERT INTO system_configs (category, key, value, description, data_type) VALUES
-- General settings
('general', 'site_name', '"YalaOffice Morocco"', 'Site name', 'string'),
('general', 'site_description', '"Professional Office & School Supplies"', 'Site description', 'string'),
('general', 'timezone', '"Africa/Casablanca"', 'System timezone', 'string'),
('general', 'session_timeout', '30', 'Session timeout in minutes', 'number'),
('general', 'file_upload_size_limit', '10', 'File upload size limit in MB', 'number'),
('general', 'maintenance_mode', 'false', 'Enable maintenance mode', 'boolean'),
('general', 'backup_frequency', '"daily"', 'Backup frequency', 'string'),

-- Security settings
('security', 'password_min_length', '8', 'Minimum password length', 'number'),
('security', 'max_login_attempts', '5', 'Maximum login attempts', 'number'),
('security', 'session_security_strict', 'false', 'Enable strict session security', 'boolean'),
('security', 'two_factor_auth_enabled', 'false', 'Enable two-factor authentication', 'boolean'),
('security', 'password_expiry_days', '90', 'Password expiry in days', 'number'),

-- Notification settings
('notifications', 'email_notifications', 'true', 'Enable email notifications', 'boolean'),
('notifications', 'sms_notifications', 'false', 'Enable SMS notifications', 'boolean'),
('notifications', 'push_notifications', 'true', 'Enable push notifications', 'boolean'),
('notifications', 'order_notifications', 'true', 'Enable order notifications', 'boolean'),
('notifications', 'inventory_notifications', 'true', 'Enable inventory notifications', 'boolean'),
('notifications', 'user_notifications', 'true', 'Enable user notifications', 'boolean'),

-- Email settings
('email', 'smtp_host', '""', 'SMTP host', 'string'),
('email', 'smtp_port', '587', 'SMTP port', 'number'),
('email', 'smtp_username', '""', 'SMTP username', 'string'),
('email', 'smtp_password', '""', 'SMTP password', 'string'),
('email', 'smtp_secure', 'true', 'Use secure SMTP', 'boolean'),
('email', 'from_email', '"<EMAIL>"', 'From email address', 'string'),
('email', 'from_name', '"YalaOffice Morocco"', 'From name', 'string'),
('email', 'test_email', '""', 'Test email address', 'string')

ON CONFLICT (category, key) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    data_type = EXCLUDED.data_type,
    updated_at = NOW();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_system_configs_category ON system_configs(category);
CREATE INDEX IF NOT EXISTS idx_system_configs_key ON system_configs(key);
CREATE INDEX IF NOT EXISTS idx_payment_methods_type ON payment_methods_config(method_type);

-- Success message
SELECT 'YalaOffice database setup completed successfully!' as status;</div>
        </div>

        <div class="step">
            <h3>🌐 Step 2: Open Supabase Dashboard</h3>
            <p>Go to your Supabase project dashboard:</p>
            <ol>
                <li>Open <a href="https://supabase.com/dashboard" target="_blank">https://supabase.com/dashboard</a></li>
                <li>Select your YalaOffice project</li>
                <li>Navigate to <strong>SQL Editor</strong> in the left sidebar</li>
            </ol>
        </div>

        <div class="step">
            <h3>⚡ Step 3: Execute the SQL Script</h3>
            <ol>
                <li>In the SQL Editor, paste the copied SQL script</li>
                <li>Click the <strong>"Run"</strong> button to execute the script</li>
                <li>Wait for the script to complete (should take a few seconds)</li>
                <li>You should see a success message: <code>"YalaOffice database setup completed successfully!"</code></li>
            </ol>
        </div>

        <div class="step">
            <h3>✅ Step 4: Verify the Setup</h3>
            <p>After running the script, verify the tables were created:</p>
            <button class="copy-btn" onclick="copyToClipboard('verify-script')">📋 Copy Verification Script</button>
            <div class="code-block" id="verify-script">-- Verify tables were created successfully
SELECT 'company_settings' as table_name, COUNT(*) as record_count FROM company_settings
UNION ALL
SELECT 'system_configs' as table_name, COUNT(*) as record_count FROM system_configs
UNION ALL
SELECT 'payment_methods_config' as table_name, COUNT(*) as record_count FROM payment_methods_config;</div>
            <p>Expected results:</p>
            <ul>
                <li><strong>company_settings:</strong> 1 record</li>
                <li><strong>system_configs:</strong> ~20 records</li>
                <li><strong>payment_methods_config:</strong> 3 records</li>
            </ul>
        </div>

        <div class="step">
            <h3>🎉 Step 5: Test System Settings</h3>
            <p>After the database setup is complete:</p>
            <ol>
                <li>Go back to YalaOffice application</li>
                <li>Navigate to <strong>Admin Dashboard → System Settings</strong></li>
                <li>Modify some settings in different tabs</li>
                <li>Click <strong>"Save All Settings"</strong></li>
                <li>You should see a success message!</li>
            </ol>
        </div>

        <div class="alert alert-success">
            <strong>🎯 After Setup:</strong><br>
            Once you've run the SQL script, the System Settings functionality will work perfectly, and you'll be able to save all settings across all tabs!
        </div>

        <div class="alert alert-warning">
            <strong>💡 Note:</strong><br>
            This script uses <code>CREATE TABLE IF NOT EXISTS</code> and <code>ON CONFLICT</code> clauses, so it's safe to run multiple times without causing errors.
        </div>
    </div>

    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                // Show success feedback
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '✅ Copied!';
                button.style.background = '#10b981';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#f59e0b';
                }, 2000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                alert('Could not copy to clipboard. Please select and copy manually.');
            });
        }
    </script>
</body>
</html>
