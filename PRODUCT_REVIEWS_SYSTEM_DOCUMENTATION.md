# YalaOffice Product Reviews and Comments System Documentation

## Overview

The YalaOffice Product Reviews and Comments System provides a comprehensive platform for customers to share feedback about products and for administrators to manage and moderate these reviews. The system includes real-time synchronization, approval workflows, and detailed analytics.

## System Architecture

### Core Components

1. **Customer Review Interface** (`ProductReviews.tsx`, `ProductDetailsModal.tsx`)
2. **Admin Management Interface** (`ReviewsManagementPage.tsx`)
3. **Review Service** (`reviewService.ts`)
4. **Real-time Synchronization** (`realTimeService.ts`)
5. **Database Integration** (Supabase `product_reviews` table)

### Database Schema

```sql
CREATE TABLE product_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id),
    customer_id UUID REFERENCES users(id),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255),
    comment TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    is_verified BOOLEAN DEFAULT false,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id, customer_id)
);
```

## Customer Review Submission Process

### Step 1: Accessing the Review System

**Location**: Product Details Modal or Product Page
**Requirements**: Customer must be logged in

```typescript
// Customer can access reviews through:
1. Product Details Modal → Reviews Tab
2. Product Page → Reviews Section
3. Order History → Review Product Button
```

### Step 2: Writing a Review

**Components**: Star rating (1-5), optional title, comment text

```typescript
interface ReviewForm {
  rating: number;        // Required: 1-5 stars
  title?: string;        // Optional: Review headline
  comment: string;       // Required: Review text
}
```

**Validation Rules**:
- Rating: Must be between 1-5 stars
- Comment: Minimum 10 characters, maximum 1000 characters
- Title: Optional, maximum 100 characters
- One review per customer per product

### Step 3: Review Submission

**Process Flow**:
1. Customer fills out review form
2. System validates input data
3. Review is saved with `status: 'pending'`
4. Real-time event is emitted to admin dashboard
5. Customer receives confirmation message
6. Review appears in admin moderation queue

```typescript
// Submission code example
const handleSubmitReview = async () => {
  try {
    await reviewService.createReview({
      product_id: productId,
      customer_id: customerId,
      customer_name: customerName,
      rating: newReview.rating,
      title: newReview.title.trim(),
      comment: newReview.comment.trim()
    });
    
    alert('Review submitted successfully!');
    setShowReviewForm(false);
    await loadReviews();
  } catch (error) {
    alert('Error submitting review. Please try again.');
  }
};
```

## Admin Review Management Process

### Step 1: Accessing Review Management

**Navigation**: Admin Dashboard → Reviews & Comments Management
**Alternative**: System Settings → Reviews Management

### Step 2: Review Moderation Queue

**Interface Features**:
- **Filter Options**: All, Pending, Approved, Rejected
- **Search**: By product name, customer name, or review content
- **Sorting**: By date, rating, status
- **Pagination**: 20 reviews per page

### Step 3: Review Actions

**Available Actions**:

1. **Approve Review** ✅
   - Makes review visible to public
   - Updates status to 'approved'
   - Sends real-time update to product pages

2. **Reject Review** ❌
   - Hides review from public view
   - Updates status to 'rejected'
   - Review remains in admin interface for reference

3. **Delete Review** 🗑️
   - Permanently removes review from system
   - Requires confirmation dialog
   - Cannot be undone

```typescript
// Admin action example
const handleStatusUpdate = async (reviewId: string, newStatus: 'approved' | 'rejected') => {
  try {
    await supabase
      .from('product_reviews')
      .update({ 
        status: newStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', reviewId);

    // Emit real-time event
    realTimeService.emit('review-updated', {
      reviewId,
      status: newStatus,
      timestamp: new Date().toISOString()
    });

    alert(`Review ${newStatus} successfully!`);
    loadReviews(); // Refresh the list
  } catch (error) {
    alert('Error updating review status. Please try again.');
  }
};
```

## Review Display and Public Visibility

### Customer-Facing Display

**Location**: Product pages, product details modals
**Visibility**: Only approved reviews are shown to customers

**Display Features**:
- Star rating visualization
- Review title and comment
- Customer name (can be anonymized)
- Verified purchase badge (if applicable)
- Review date
- Helpful votes counter
- Average rating calculation

### Review Statistics

**Calculated Metrics**:
- Average rating (1-5 stars)
- Total number of reviews
- Rating distribution (5-star: X, 4-star: Y, etc.)
- Helpful votes per review

```typescript
// Statistics calculation
interface ReviewStats {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}
```

## Real-Time Synchronization

### Events and Updates

**Real-time Events**:
1. `review-created`: New review submitted
2. `review-updated`: Review status changed
3. `review-deleted`: Review removed
4. `review-helpful`: Helpful vote added

**Synchronized Components**:
- Admin review management interface
- Product detail pages
- Review statistics
- Dashboard analytics

```typescript
// Real-time subscription example
realTimeService.subscribe('review-created', (data) => {
  console.log('New review submitted:', data);
  // Update admin notification counter
  // Refresh pending reviews list
  // Update product statistics
});
```

## Configuration and Setup

### Database Setup

1. **Create Tables**: Run the database schema SQL
2. **Set Permissions**: Configure row-level security
3. **Add Indexes**: For performance optimization

```sql
-- Performance indexes
CREATE INDEX idx_product_reviews_product_id ON product_reviews(product_id);
CREATE INDEX idx_product_reviews_status ON product_reviews(status);
CREATE INDEX idx_product_reviews_created_at ON product_reviews(created_at);
```

### System Configuration

**Required Settings**:
- Review moderation: Enabled/Disabled
- Auto-approval: For verified customers
- Minimum review length: Default 10 characters
- Maximum review length: Default 1000 characters
- Anonymous reviews: Allowed/Not allowed

### User Permissions

**Customer Permissions**:
- Submit reviews for purchased products
- Edit own reviews (within time limit)
- Mark reviews as helpful

**Admin Permissions**:
- View all reviews (pending, approved, rejected)
- Approve/reject reviews
- Delete inappropriate reviews
- View review analytics
- Export review data

## Troubleshooting

### Common Issues

1. **Reviews Not Appearing**
   - Check review status (must be 'approved')
   - Verify database connection
   - Check real-time synchronization

2. **Duplicate Review Error**
   - Customer already reviewed this product
   - Check unique constraint in database

3. **Permission Errors**
   - Verify user authentication
   - Check database row-level security
   - Confirm user roles and permissions

### Error Handling

**Customer-Side Errors**:
- Network connectivity issues
- Authentication failures
- Validation errors
- Duplicate submission attempts

**Admin-Side Errors**:
- Database connection failures
- Permission denied errors
- Real-time synchronization issues

## Best Practices

### For Customers
1. Write detailed, helpful reviews
2. Be honest and constructive
3. Focus on product features and quality
4. Avoid personal attacks or inappropriate content

### For Administrators
1. Review submissions promptly
2. Apply consistent moderation standards
3. Respond to customer concerns
4. Monitor review quality and authenticity
5. Use analytics to improve products and services

### For Developers
1. Implement proper error handling
2. Use real-time updates for better UX
3. Optimize database queries for performance
4. Implement proper validation and sanitization
5. Monitor system performance and usage

## Analytics and Reporting

### Available Metrics
- Total reviews per product
- Average ratings over time
- Review approval rates
- Customer engagement metrics
- Most helpful reviews
- Review response times

### Export Options
- CSV export of all reviews
- Product-specific review reports
- Customer review history
- Admin activity logs

This documentation provides a complete guide to the YalaOffice Product Reviews and Comments System, covering all aspects from customer submission to admin management and system configuration.
