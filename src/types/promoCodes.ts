// YalaOffice Promo Codes Type Definitions

export type DiscountType = 'percentage' | 'fixed_amount';
export type ApplicableTo = 'all' | 'categories' | 'products';
export type PromoCodeStatus = 'active' | 'inactive' | 'expired' | 'used_up';

// Main promo code interface
export interface PromoCode {
  id: string;
  
  // Basic Information
  code: string;
  name: string;
  description?: string;
  
  // Discount Configuration
  discount_type: DiscountType;
  discount_value: number;
  
  // Usage Limits
  usage_limit_total?: number; // null = unlimited
  usage_limit_per_customer: number;
  current_usage_count: number;
  
  // Validity Period
  valid_from: string; // ISO date string
  valid_until?: string; // ISO date string, null = no expiry
  
  // Order Requirements
  minimum_order_amount: number;
  maximum_discount_amount?: number; // null = no maximum
  
  // Restrictions
  applicable_to: ApplicableTo;
  restricted_categories: string[]; // Array of category IDs
  restricted_products: string[]; // Array of product IDs
  
  // Status and Metadata
  is_active: boolean;
  is_featured: boolean;
  
  // Audit Fields
  created_by?: string;
  updated_by?: string;
  created_at: string;
  updated_at: string;
}

// Promo code usage tracking
export interface PromoCodeUsage {
  id: string;
  promo_code_id: string;
  order_id: string;
  user_id?: string;
  
  // Usage Details
  original_amount: number;
  discount_amount: number;
  final_amount: number;
  
  // Metadata
  used_at: string;
  ip_address?: string;
  user_agent?: string;
}

// Promo code validation result
export interface PromoCodeValidation {
  is_valid: boolean;
  error_message?: string;
  promo_data?: PromoCode;
  discount_amount?: number;
}

// Form data for creating/editing promo codes
export interface PromoCodeFormData {
  code: string;
  name: string;
  description: string;
  discount_type: DiscountType;
  discount_value: number;
  usage_limit_total?: number;
  usage_limit_per_customer: number;
  valid_from: string;
  valid_until?: string;
  minimum_order_amount: number;
  maximum_discount_amount?: number;
  applicable_to: ApplicableTo;
  restricted_categories: string[];
  restricted_products: string[];
  is_active: boolean;
  is_featured: boolean;
}

// Filter options for promo codes list
export interface PromoCodeFilters {
  status?: PromoCodeStatus[];
  discount_type?: DiscountType[];
  date_range?: {
    start: string;
    end: string;
    type: 'created' | 'valid_from' | 'valid_until';
  };
  usage_status?: ('unused' | 'partially_used' | 'fully_used')[];
  search?: string;
  is_featured?: boolean;
  minimum_discount?: number;
  maximum_discount?: number;
}

// Sort options for promo codes list
export interface PromoCodeSort {
  field: 'code' | 'name' | 'discount_value' | 'created_at' | 'valid_until' | 'current_usage_count';
  direction: 'asc' | 'desc';
}

// Pagination for promo codes list
export interface PromoCodePagination {
  page: number;
  limit: number;
  total: number;
  total_pages: number;
}

// Complete promo codes list response
export interface PromoCodesListResponse {
  data: PromoCode[];
  pagination: PromoCodePagination;
  filters: PromoCodeFilters;
  sort: PromoCodeSort;
}

// Promo code statistics
export interface PromoCodeStats {
  total_codes: number;
  active_codes: number;
  expired_codes: number;
  featured_codes: number;
  total_usage: number;
  total_discount_given: number;
  most_used_code?: {
    code: string;
    name: string;
    usage_count: number;
  };
  expiring_soon: PromoCode[]; // Codes expiring in next 7 days
}

// Bulk operations
export interface BulkPromoCodeOperation {
  action: 'activate' | 'deactivate' | 'delete' | 'feature' | 'unfeature';
  promo_code_ids: string[];
}

export interface BulkOperationResult {
  success: boolean;
  affected_count: number;
  errors?: string[];
}

// Real-time events
export interface PromoCodeEvent {
  type: 'promo-code-created' | 'promo-code-updated' | 'promo-code-deleted' | 'promo-code-used';
  data: PromoCode | PromoCodeUsage;
  timestamp: string;
  userId?: string;
  userType?: string;
}

// Validation rules
export interface PromoCodeValidationRules {
  code: {
    required: boolean;
    minLength: number;
    maxLength: number;
    pattern: RegExp;
    unique: boolean;
  };
  name: {
    required: boolean;
    minLength: number;
    maxLength: number;
  };
  discount_value: {
    required: boolean;
    min: number;
    max: number;
  };
  usage_limit_per_customer: {
    required: boolean;
    min: number;
    max: number;
  };
  minimum_order_amount: {
    required: boolean;
    min: number;
  };
  valid_from: {
    required: boolean;
  };
  valid_until: {
    required: boolean;
    mustBeAfterValidFrom: boolean;
  };
}

// Default validation rules
export const DEFAULT_PROMO_CODE_VALIDATION_RULES: PromoCodeValidationRules = {
  code: {
    required: true,
    minLength: 3,
    maxLength: 50,
    pattern: /^[A-Z0-9_-]+$/,
    unique: true
  },
  name: {
    required: true,
    minLength: 3,
    maxLength: 255
  },
  discount_value: {
    required: true,
    min: 0.01,
    max: 100 // For percentage, this would be 100%
  },
  usage_limit_per_customer: {
    required: true,
    min: 1,
    max: 100
  },
  minimum_order_amount: {
    required: true,
    min: 0
  },
  valid_from: {
    required: true
  },
  valid_until: {
    required: false,
    mustBeAfterValidFrom: true
  }
};

// Helper type for form field errors
export interface PromoCodeFormErrors {
  code?: string;
  name?: string;
  description?: string;
  discount_type?: string;
  discount_value?: string;
  usage_limit_total?: string;
  usage_limit_per_customer?: string;
  valid_from?: string;
  valid_until?: string;
  minimum_order_amount?: string;
  maximum_discount_amount?: string;
  applicable_to?: string;
  restricted_categories?: string;
  restricted_products?: string;
  general?: string;
}

// Export utility types
export type PromoCodeField = keyof PromoCode;
export type PromoCodeFormField = keyof PromoCodeFormData;
export type PromoCodeFilterField = keyof PromoCodeFilters;
