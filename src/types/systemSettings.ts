/**
 * TypeScript interfaces that exactly match the database schema for System Settings
 */

// ===== DATABASE TABLE INTERFACES =====

/**
 * company_settings table interface
 * Matches: id, name, address, phone, email, tax_id, ice_number, logo_url, created_at, updated_at
 */
export interface CompanySettings {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  tax_id?: string;
  ice_number?: string;
  logo_url?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * system_configs table interface
 * Matches: id, category, key, value, description, data_type, is_active, created_at, updated_at, created_by, updated_by
 */
export interface SystemConfig {
  id?: string;
  category: string;
  key: string;
  value: string;
  description?: string;
  data_type: 'string' | 'number' | 'boolean' | 'json';
  is_active?: boolean; // Optional - may not exist in database
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
}

/**
 * payment_methods_config table interface
 * Matches: id, method_type, is_enabled, configuration, created_at, updated_at
 */
export interface PaymentMethodConfig {
  id: string;
  method_type: 'bank_transfer' | 'check' | 'cash';
  is_enabled: boolean;
  configuration: PaymentMethodConfiguration;
  created_at?: string;
  updated_at?: string;
}

// ===== CONFIGURATION INTERFACES =====

/**
 * Bank Transfer Configuration (stored in JSONB)
 */
export interface BankTransferConfig {
  bankName: string;
  accountNumber: string;
  iban: string;
  swiftCode: string;
  accountHolder: string;
}

/**
 * Check Payment Configuration (stored in JSONB)
 */
export interface CheckPaymentConfig {
  payableTo: string;
  mailingAddress: string;
  processingTime: string;
}

/**
 * Cash Payment Configuration (stored in JSONB)
 */
export interface CashPaymentConfig {
  enabled: boolean;
}

/**
 * Union type for all payment method configurations
 */
export type PaymentMethodConfiguration = BankTransferConfig | CheckPaymentConfig | CashPaymentConfig;

// ===== ORGANIZED SYSTEM CONFIGS BY CATEGORY =====

/**
 * General Settings (category: 'general')
 * Based on actual database entries
 */
export interface GeneralSettings {
  site_name: string;
  site_description: string;
  timezone: string;
  session_timeout: number;
  file_upload_size_limit: number;
  maintenance_mode: boolean;
  backup_frequency: string;
}

/**
 * Security Settings (category: 'security')
 * Based on actual database entries
 */
export interface SecuritySettings {
  password_min_length: number;
  max_login_attempts: number;
  session_security_strict: boolean;
  two_factor_auth_enabled: boolean;
  password_expiry_days: number;
}

/**
 * Notification Settings (category: 'notifications')
 * Based on actual database entries
 */
export interface NotificationSettings {
  email_notifications: boolean;
  sms_notifications: boolean;
  push_notifications: boolean;
  order_notifications: boolean;
  inventory_notifications: boolean;
  user_notifications: boolean;
}

/**
 * Email Settings (category: 'email')
 * Based on actual database entries
 */
export interface EmailSettings {
  smtp_host: string;
  smtp_port: number;
  smtp_username: string;
  smtp_password: string;
  smtp_secure: boolean;
  from_email: string;
  from_name: string;
  test_email: string;
}

// ===== FORM STATE INTERFACES =====

/**
 * Complete System Settings state for the form
 */
export interface SystemSettingsState {
  companySettings: CompanySettings;
  generalSettings: GeneralSettings;
  securitySettings: SecuritySettings;
  notificationSettings: NotificationSettings;
  emailSettings: EmailSettings;
  paymentMethods: PaymentMethodConfig[];
}

/**
 * System Settings save payload
 */
export interface SystemSettingsSavePayload {
  companySettings: Partial<CompanySettings>;
  systemConfigs: SystemConfig[];
  paymentMethods: PaymentMethodConfig[];
}

// ===== VALIDATION INTERFACES =====

/**
 * Field validation rules based on database constraints
 */
export interface FieldValidation {
  required?: boolean;
  maxLength?: number;
  minLength?: number;
  pattern?: RegExp;
  type?: 'string' | 'number' | 'boolean' | 'email' | 'url';
}

/**
 * Validation rules for company settings
 */
export const COMPANY_SETTINGS_VALIDATION: Record<keyof CompanySettings, FieldValidation> = {
  id: { required: true, maxLength: 36 },
  name: { required: true, maxLength: 255 },
  address: { maxLength: 1000 },
  phone: { maxLength: 50 },
  email: { maxLength: 255, type: 'email' },
  tax_id: { maxLength: 100 },
  ice_number: { maxLength: 100 },
  logo_url: { type: 'url' },
  created_at: {},
  updated_at: {}
};

/**
 * Validation rules for system configs
 */
export const SYSTEM_CONFIG_VALIDATION: Record<keyof SystemConfig, FieldValidation> = {
  id: { maxLength: 36 },
  category: { required: true, maxLength: 100 },
  key: { required: true, maxLength: 100 },
  value: { required: true },
  description: {},
  data_type: { required: true, maxLength: 50 },
  is_active: { type: 'boolean' },
  created_at: {},
  updated_at: {},
  created_by: { maxLength: 36 },
  updated_by: { maxLength: 36 }
};

// ===== UTILITY TYPES =====

/**
 * System config categories
 */
export type SystemConfigCategory = 'general' | 'security' | 'notifications' | 'email';

/**
 * System config data types
 */
export type SystemConfigDataType = 'string' | 'number' | 'boolean' | 'json';

/**
 * Payment method types
 */
export type PaymentMethodType = 'bank_transfer' | 'check' | 'cash';

// ===== REAL-TIME EVENT INTERFACES =====

/**
 * Real-time event payloads for system settings changes
 */
export interface SystemSettingsUpdateEvent {
  type: 'company_settings' | 'system_configs' | 'payment_methods';
  data: CompanySettings | SystemConfig[] | PaymentMethodConfig[];
  timestamp: string;
  userId?: string;
}

/**
 * Specific event types for different settings
 */
export interface CompanySettingsUpdateEvent extends SystemSettingsUpdateEvent {
  type: 'company_settings';
  data: CompanySettings;
}

export interface SystemConfigsUpdateEvent extends SystemSettingsUpdateEvent {
  type: 'system_configs';
  data: SystemConfig[];
}

export interface PaymentMethodsUpdateEvent extends SystemSettingsUpdateEvent {
  type: 'payment_methods';
  data: PaymentMethodConfig[];
}
