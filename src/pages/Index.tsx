
import { Building2, Eye, EyeOff, Mail, Lock, User, Phone, MapPin, ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react';
import { useState, useRef } from 'react';
import Dashboard from '../components/Dashboard';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { MOROCCAN_CITIES } from '../constants/cities';
import { useMobileUtils } from '../hooks/use-mobile';
import { useResponsiveSpacing } from '../hooks/useResponsive';

const Index = () => {
  const { user, login, logout, isAuthenticated, loading } = useAuth();

  // Enhanced responsive utilities
  const { isMobile, isSmallMobile, isTouch, shouldUseTouchUI } = useMobileUtils();
  const spacing = useResponsiveSpacing();

  // Form states
  const [currentForm, setCurrentForm] = useState<'login' | 'signup' | 'forgot'>('login');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [forgotSuccess, setForgotSuccess] = useState(false);

  // Form data
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    phone: '',
    city: '',
    address: ''
  });

  const cities = MOROCCAN_CITIES;

  // Create a ref to store the dashboard navigation function (must be at top level)
  const dashboardNavigateRef = useRef<((page: string) => void) | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: formData.email,
        password: formData.password
      });

      if (error) throw error;

      if (data.user) {
        // Get user profile from database with profile information
        const { data: userProfile, error: profileError } = await supabase
          .from('users')
          .select(`
            *,
            user_profiles (
              bio,
              job_title,
              avatar_url
            )
          `)
          .eq('id', data.user.id)
          .single();

        const userData = {
          id: data.user.id,
          fullName: userProfile?.full_name || data.user.email?.split('@')[0] || 'User',
          full_name: userProfile?.full_name || data.user.email?.split('@')[0] || 'User', // Include snake_case version
          email: data.user.email || '',
          phone: userProfile?.phone || '',
          city: userProfile?.city || 'Tetouan',
          address: userProfile?.company_address || '',
          company: userProfile?.company || '',
          userType: (userProfile?.user_type || 'client') as any,
          // Company information - include all fields that can be updated
          isCompany: userProfile?.is_company || false,
          is_company: userProfile?.is_company || false, // Include snake_case version
          companyName: userProfile?.company_name || '',
          company_name: userProfile?.company_name || '', // Include snake_case version
          iceNumber: userProfile?.ice_number || '',
          ice_number: userProfile?.ice_number || '', // Include snake_case version
          companyAddress: userProfile?.company_address || '',
          company_address: userProfile?.company_address || '', // Include snake_case version
          companyPhone: userProfile?.company_phone || '',
          company_phone: userProfile?.company_phone || '', // Include snake_case version
          companyCity: userProfile?.company_city || '',
          company_city: userProfile?.company_city || '', // Include snake_case version
          companyEmail: userProfile?.company_email || '',
          company_email: userProfile?.company_email || '', // Include snake_case version
          taxId: userProfile?.tax_id || '',
          tax_id: userProfile?.tax_id || '', // Include snake_case version
          legalForm: userProfile?.legal_form || '',
          legal_form: userProfile?.legal_form || '', // Include snake_case version
          // Profile fields from user_profiles table
          jobTitle: userProfile?.user_profiles?.[0]?.job_title || '',
          job_title: userProfile?.user_profiles?.[0]?.job_title || '',
          bio: userProfile?.user_profiles?.[0]?.bio || '',
          isAuthenticated: true,
          isActive: userProfile?.is_active || true,
          createdAt: userProfile?.created_at || new Date().toISOString()
        };

        // Update last login timestamp
        try {
          await supabase
            .from('users')
            .update({ last_login: new Date().toISOString() })
            .eq('id', userData.id);
        } catch (error) {
          console.error('Error updating last login:', error);
        }

        login(userData);
      }
    } catch (error: any) {
      setError(error.message || 'Login failed');
    } finally {
      setFormLoading(false);
    }
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormLoading(true);
    setError(null);

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setFormLoading(false);
      return;
    }

    try {
      const { data, error } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            full_name: formData.fullName,
            phone: formData.phone,
            city: formData.city,
            user_type: 'client'
          }
        }
      });

      if (error) throw error;

      if (data.user) {
        // Insert user profile
        const { error: insertError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            full_name: formData.fullName,
            email: formData.email,
            phone: formData.phone,
            city: formData.city,
            company_address: formData.address,
            user_type: 'client',
            is_active: true
          });

        if (insertError) {
          console.warn('Profile insert failed:', insertError);
        }

        const userData = {
          id: data.user.id,
          fullName: formData.fullName,
          email: formData.email,
          phone: formData.phone,
          city: formData.city,
          address: formData.address,
          userType: 'client' as const,
          isAuthenticated: true,
          isActive: true,
          createdAt: new Date().toISOString()
        };

        login(userData);
      }
    } catch (error: any) {
      setError(error.message || 'Registration failed');
    } finally {
      setFormLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormLoading(true);
    setError(null);

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(formData.email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        throw error;
      }

      setForgotSuccess(true);
    } catch (error: any) {
      console.error('Password recovery error:', error);

      if (error.message.includes('Email not confirmed')) {
        setError('Please confirm your email address first before resetting your password.');
      } else if (error.message.includes('Invalid email')) {
        setError('Please enter a valid email address.');
      } else if (error.message.includes('User not found')) {
        setError('No account found with this email address.');
      } else {
        setError(error.message || 'Failed to send password reset email. Please try again.');
      }
    } finally {
      setFormLoading(false);
    }
  };

  const handleLogout = () => {
    logout();
  };

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading YalaOffice...</p>
        </div>
      </div>
    );
  }

  if (isAuthenticated && user) {
    return (
      <Dashboard
        user={user}
        onLogout={handleLogout}
        onNavigateToProfile={() => {
          console.log('Index.tsx: onNavigateToProfile called - attempting profile navigation');

          // Try to use the dashboard navigation function if available
          if (dashboardNavigateRef.current) {
            console.log('Index.tsx: Using dashboard navigation function');
            dashboardNavigateRef.current('profile');
          } else {
            console.log('Index.tsx: Dashboard navigation not available, using URL navigation');
            // Fallback: Update URL and localStorage
            const url = new URL(window.location.href);
            url.searchParams.set('page', 'profile');
            window.history.replaceState({}, '', url.toString());
            localStorage.setItem('admin_current_page', 'profile');

            // Force page reload as last resort
            window.location.reload();
          }
        }}
        dashboardNavigateRef={dashboardNavigateRef}
      />
    );
  }



  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1 flex flex-col lg:flex-row">
        {/* Left Section - White Background with Authentication - Enhanced for mobile */}
        <div className="w-full lg:w-1/2 bg-white flex flex-col">
        {/* Logo Section - Responsive */}
        <div className={`${isSmallMobile ? 'p-4' : isMobile ? 'p-6' : 'p-8'}`}>
          <div className={`flex items-center ${isSmallMobile ? 'space-x-2' : 'space-x-3'}`}>
            <div className={`bg-gradient-to-r from-teal-600 to-amber-500 rounded-xl shadow-lg ${
              isSmallMobile ? 'p-1.5' : 'p-2'
            }`}>
              <Building2 className={`text-white ${isSmallMobile ? 'h-6 w-6' : 'h-8 w-8'}`} />
            </div>
            <div>
              <h1 className={`font-bold bg-gradient-to-r from-teal-600 to-amber-500 bg-clip-text text-transparent ${
                isSmallMobile ? 'text-xl' : 'text-2xl'
              }`}>
                YalaOffice
              </h1>
              <p className={`text-gray-600 ${isSmallMobile ? 'text-xs' : 'text-sm'}`}>
                {isSmallMobile ? 'Supply Management' : '(Modern Supply Chain Management)'}
              </p>
            </div>
          </div>
        </div>



        {/* Authentication Form Area - Enhanced for mobile */}
        <div className={`flex-1 flex items-center justify-center ${
          isSmallMobile ? 'p-4' : isMobile ? 'p-6' : 'p-8'
        }`}>
          <div className="w-full max-w-md">
            {/* Login Form */}
            {currentForm === 'login' && (
              <div>
                <h2 className={`font-bold text-gray-900 ${
                  isSmallMobile ? 'text-2xl mb-6' : 'text-3xl mb-8'
                }`}>Login to your account</h2>

                <form onSubmit={handleLogin} className={`${isSmallMobile ? 'space-y-4' : 'space-y-6'}`}>
                  {/* Error Display */}
                  {error && (
                    <div className={`bg-red-50 border border-red-200 rounded-lg ${
                      isSmallMobile ? 'p-2' : 'p-3'
                    }`}>
                      <p className={`text-red-600 ${isSmallMobile ? 'text-xs' : 'text-sm'}`}>{error}</p>
                    </div>
                  )}

                  {/* Email Field - Enhanced for mobile */}
                  <div>
                    <label className={`block font-medium text-gray-700 mb-2 ${
                      isSmallMobile ? 'text-xs' : 'text-sm'
                    }`}>
                      Email Address
                    </label>
                    <div className="relative">
                      <Mail className={`absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 ${
                        isSmallMobile ? 'h-4 w-4' : 'h-5 w-5'
                      }`} />
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className={`w-full pl-10 pr-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-colors ${
                          shouldUseTouchUI ? 'py-4 text-base' : 'py-3 text-sm'
                        }`}
                        placeholder="Enter your email"
                        required
                        autoComplete="email"
                      />
                    </div>
                  </div>

                  {/* Password Field - Enhanced for mobile */}
                  <div>
                    <label className={`block font-medium text-gray-700 mb-2 ${
                      isSmallMobile ? 'text-xs' : 'text-sm'
                    }`}>
                      Password
                    </label>
                    <div className="relative">
                      <Lock className={`absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 ${
                        isSmallMobile ? 'h-4 w-4' : 'h-5 w-5'
                      }`} />
                      <input
                        type={showPassword ? 'text' : 'password'}
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        className={`w-full pl-10 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-colors ${
                          shouldUseTouchUI ? 'py-4 text-base' : 'py-3 text-sm'
                        }`}
                        placeholder="Enter your password"
                        required
                        autoComplete="current-password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors ${
                          shouldUseTouchUI ? 'p-1' : ''
                        }`}
                        aria-label={showPassword ? "Hide password" : "Show password"}
                      >
                        {showPassword ?
                          <EyeOff className={`${isSmallMobile ? 'h-4 w-4' : 'h-5 w-5'}`} /> :
                          <Eye className={`${isSmallMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
                        }
                      </button>
                    </div>
                  </div>

                  {/* Remember Me & Forgot Password - Enhanced for mobile */}
                  <div className={`flex items-center justify-between ${isSmallMobile ? 'flex-col space-y-3' : ''}`}>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={rememberMe}
                        onChange={(e) => setRememberMe(e.target.checked)}
                        className={`text-teal-600 focus:ring-teal-500 border-gray-300 rounded ${
                          shouldUseTouchUI ? 'h-5 w-5' : 'h-4 w-4'
                        }`}
                      />
                      <span className={`ml-2 text-gray-600 ${isSmallMobile ? 'text-xs' : 'text-sm'}`}>
                        Remember Me
                      </span>
                    </label>
                    <button
                      type="button"
                      onClick={() => setCurrentForm('forgot')}
                      className={`text-teal-600 hover:text-teal-700 font-medium transition-colors ${
                        isSmallMobile ? 'text-xs py-2' : 'text-sm'
                      } ${shouldUseTouchUI ? 'min-h-[44px] px-2' : ''}`}
                    >
                      Forgot Password?
                    </button>
                  </div>

                  {/* Login Button - Enhanced for mobile */}
                  <button
                    type="submit"
                    disabled={formLoading}
                    className={`w-full bg-gradient-to-r from-teal-600 to-amber-500 text-white rounded-lg hover:shadow-lg transition-all duration-200 font-semibold disabled:opacity-50 ${
                      shouldUseTouchUI ? 'py-4 text-base min-h-[48px]' : 'py-3 text-sm'
                    }`}
                  >
                    {formLoading ? 'Signing in...' : 'Login'}
                  </button>

                  {/* Sign Up Link - Enhanced for mobile */}
                  <div className="text-center">
                    <button
                      type="button"
                      onClick={() => setCurrentForm('signup')}
                      className={`text-teal-600 hover:text-teal-700 font-medium transition-colors ${
                        isSmallMobile ? 'text-sm py-3' : 'text-base'
                      } ${shouldUseTouchUI ? 'min-h-[44px] px-4' : ''}`}
                    >
                      Don't have an account? Sign Up
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* Sign Up Form - Enhanced for mobile */}
            {currentForm === 'signup' && (
              <div>
                <h2 className={`font-bold text-gray-900 ${
                  isSmallMobile ? 'text-2xl mb-6' : 'text-3xl mb-8'
                }`}>Create your account</h2>

                <form onSubmit={handleSignup} className={`${isSmallMobile ? 'space-y-4' : 'space-y-6'}`}>
                  {/* Error Display */}
                  {error && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-sm text-red-600">{error}</p>
                    </div>
                  )}

                  {/* Full Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name
                    </label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <input
                        type="text"
                        name="fullName"
                        value={formData.fullName}
                        onChange={handleInputChange}
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                        placeholder="Enter your full name"
                        required
                      />
                    </div>
                  </div>

                  {/* Email */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                        placeholder="Enter your email"
                        required
                      />
                    </div>
                  </div>

                  {/* Phone */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number
                    </label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                        placeholder="+212 6 12 34 56 78"
                        required
                      />
                    </div>
                  </div>

                  {/* City */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      City
                    </label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <select
                        name="city"
                        value={formData.city}
                        onChange={handleInputChange}
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                        required
                      >
                        <option value="">Select City</option>
                        {cities.map(city => (
                          <option key={city} value={city}>{city}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Password */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <input
                        type={showPassword ? 'text' : 'password'}
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                        placeholder="Create a password"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                      </button>
                    </div>
                  </div>

                  {/* Confirm Password */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Confirm Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                        placeholder="Confirm your password"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                      </button>
                    </div>
                  </div>

                  {/* Sign Up Button */}
                  <button
                    type="submit"
                    disabled={formLoading}
                    className="w-full bg-gradient-to-r from-teal-600 to-amber-500 text-white py-3 rounded-lg hover:shadow-lg transition-all duration-200 font-semibold disabled:opacity-50"
                  >
                    {formLoading ? 'Creating account...' : 'Sign Up'}
                  </button>

                  {/* Back to Login Link */}
                  <div className="text-center">
                    <button
                      type="button"
                      onClick={() => setCurrentForm('login')}
                      className="text-teal-600 hover:text-teal-700 font-medium"
                    >
                      Already have an account? Sign In
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* Password Recovery Form */}
            {currentForm === 'forgot' && (
              <div>
                {forgotSuccess ? (
                  // Success State
                  <div className="text-center">
                    <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                      <CheckCircle className="h-8 w-8 text-green-600" />
                    </div>

                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Check Your Email</h2>

                    <p className="text-gray-600 mb-6">
                      We've sent a password reset link to <strong>{formData.email}</strong>.
                      Please check your email and follow the instructions to reset your password.
                    </p>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                      <p className="text-sm text-blue-800">
                        <strong>Didn't receive the email?</strong>
                      </p>
                      <ul className="text-sm text-blue-700 mt-2 space-y-1">
                        <li>• Check your spam/junk folder</li>
                        <li>• Make sure the email address is correct</li>
                        <li>• Wait a few minutes and try again</li>
                      </ul>
                    </div>

                    <div className="space-y-3">
                      <button
                        onClick={() => setCurrentForm('login')}
                        className="w-full bg-gradient-to-r from-teal-600 to-amber-500 text-white py-3 rounded-lg hover:shadow-lg transition-all duration-200 font-semibold"
                      >
                        Back to Sign In
                      </button>

                      <button
                        onClick={() => {
                          setForgotSuccess(false);
                          setFormData(prev => ({ ...prev, email: '' }));
                        }}
                        className="w-full text-teal-600 hover:text-teal-700 py-2 font-medium"
                      >
                        Send Another Email
                      </button>
                    </div>
                  </div>
                ) : (
                  // Password Recovery Form
                  <div>
                    <div className="flex items-center space-x-3 mb-6">
                      <button
                        onClick={() => setCurrentForm('login')}
                        className="text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        <ArrowLeft className="h-5 w-5" />
                      </button>
                      <h2 className="text-3xl font-bold text-gray-900">Reset Password</h2>
                    </div>

                    <div className="text-center mb-6">
                      <div className="mx-auto w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mb-4">
                        <Mail className="h-6 w-6 text-teal-600" />
                      </div>
                      <p className="text-gray-600">
                        Enter your email address and we'll send you a link to reset your password.
                      </p>
                    </div>

                    <form onSubmit={handleForgotPassword} className="space-y-6">
                      {/* Error Display */}
                      {error && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded-lg flex items-start space-x-2">
                          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                          <p className="text-sm text-red-600">{error}</p>
                        </div>
                      )}

                      {/* Email Input */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Email Address
                        </label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                          <input
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                            placeholder="Enter your email address"
                            required
                            disabled={formLoading}
                          />
                        </div>
                      </div>

                      {/* Submit Button */}
                      <button
                        type="submit"
                        disabled={formLoading || !formData.email.trim()}
                        className="w-full bg-gradient-to-r from-teal-600 to-amber-500 text-white py-3 rounded-lg hover:shadow-lg transition-all duration-200 font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {formLoading ? 'Sending...' : 'Send Reset Link'}
                      </button>

                      {/* Back to Login */}
                      <div className="text-center">
                        <button
                          type="button"
                          onClick={() => setCurrentForm('login')}
                          className="text-teal-600 hover:text-teal-700 font-medium"
                        >
                          Back to Sign In
                        </button>
                      </div>
                    </form>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Right Section - Marketing/Visual Area */}
      <div className="w-full lg:w-1/2 relative bg-cover bg-center bg-no-repeat min-h-[50vh] lg:min-h-screen"
           style={{
             backgroundImage: 'url(https://images.pexels.com/photos/7718851/pexels-photo-7718851.jpeg)'
           }}>
        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-teal-600/80 via-teal-700/70 to-amber-500/60"></div>
        {/* Content Over Background */}
        <div className="relative z-10 flex flex-col justify-center h-full p-6 lg:p-12 text-white">
          <div className="max-w-lg">
            {/* Badge */}
            <div className="inline-flex items-center bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium mb-8">
              Join 500+ businesses already using YalaOffice
            </div>

            {/* Main Headline */}
            <h1 className="text-3xl lg:text-5xl font-bold mb-6 leading-tight">
              Welcome to YalaOffice
            </h1>

            {/* Subtitle */}
            <p className="text-lg lg:text-xl mb-8 text-white/90 leading-relaxed">
              Revolutionize your supply chain operations with intelligent management tools.
            </p>

            {/* Trust Badge */}
            <div className="inline-flex items-center bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium mb-8">
              Trusted by 500+ Businesses Across Morocco
            </div>

            {/* Contact Information */}
            <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-6">
              <h3 className="text-xl font-bold mb-4">Get in Touch</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                    <span className="text-white text-sm">@</span>
                  </div>
                  <span className="text-white"><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                    <span className="text-white text-sm">📞</span>
                  </div>
                  <span className="text-white">+212 600 00 00 00</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                    <span className="text-white text-sm">💬</span>
                  </div>
                  <a
                    href="https://wa.me/212600000000"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white hover:text-green-200 transition-colors"
                  >
                    WhatsApp: +212 600 00 00 00
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>

      {/* Minimal Footer */}
      <footer className="bg-white border-t border-gray-200 py-4">
        <div className="text-center">
          <p className="text-gray-500 text-sm">
            © 2025 YalaOffice. Modern Supply Chain Management.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Index;
