/**
 * Live Data Hooks for YalaOffice
 * Custom hooks for easy live data integration
 */

import { useState, useEffect, useCallback } from 'react';
import { liveDataService } from '../services/liveDataService';
import { getCategories } from '../services/liveCategoryService';

// Generic live data hook
export const useLiveData = <T>(
  fetchFunction: () => Promise<T[]>,
  dependencies: any[] = []
) => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await fetchFunction();
      setData(result);
    } catch (err) {
      setError(err as Error);
      console.error('Live data fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, dependencies);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch };
};

// Specific hooks for different data types
export const useLiveProducts = (categoryId?: string) => {
  return useLiveData(
    () => categoryId 
      ? liveDataService.getProductsByCategory(categoryId)
      : liveDataService.getAllProducts(),
    [categoryId]
  );
};

export const useLiveOrders = (status?: string) => {
  return useLiveData(
    () => status 
      ? liveDataService.getOrdersByStatus(status)
      : liveDataService.getAllOrders(),
    [status]
  );
};

export const useLiveCustomers = () => {
  return useLiveData(
    () => liveDataService.getAllCustomers(),
    []
  );
};

export const useLiveUsers = (userType?: string) => {
  return useLiveData(
    () => userType 
      ? liveDataService.getUsersByType(userType)
      : liveDataService.getAllUsers(),
    [userType]
  );
};

export const useLiveBranches = () => {
  return useLiveData(
    () => liveDataService.getAllBranches(),
    []
  );
};

export const useLiveCategories = () => {
  return useLiveData(
    () => getCategories(),
    []
  );
};

// Dashboard stats hook
export const useDashboardStats = () => {
  return useLiveData(
    () => liveDataService.getDashboardStats().then(stats => [stats]),
    []
  );
};

// Review hooks
export const useLiveReviews = () => {
  return useLiveData(
    () => liveDataService.getAllReviews(),
    []
  );
};

export const useLiveProductReviews = (productId?: string) => {
  return useLiveData(
    () => productId ? liveDataService.getProductReviews(productId) : Promise.resolve([]),
    [productId]
  );
};

// Real-time subscription hooks
export const useRealtimeProducts = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const data = await liveDataService.getAllProducts();
        setProducts(data);
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };

    // Initial fetch
    fetchProducts();

    // Set up real-time subscription
    const subscription = liveDataService.subscribeToProducts((payload) => {
      console.log('Products updated:', payload);
      fetchProducts(); // Refetch on changes
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return { products, loading };
};

export const useRealtimeOrders = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        const data = await liveDataService.getAllOrders();
        setOrders(data);
      } catch (error) {
        console.error('Error fetching orders:', error);
      } finally {
        setLoading(false);
      }
    };

    // Initial fetch
    fetchOrders();

    // Set up real-time subscription
    const subscription = liveDataService.subscribeToOrders((payload) => {
      console.log('Orders updated:', payload);
      fetchOrders(); // Refetch on changes
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return { orders, loading };
};

export const useRealtimeUsers = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const data = await liveDataService.getAllUsers();
        setUsers(data);
      } catch (error) {
        console.error('Error fetching users:', error);
      } finally {
        setLoading(false);
      }
    };

    // Initial fetch
    fetchUsers();

    // Set up real-time subscription
    const subscription = liveDataService.subscribeToUsers((payload) => {
      console.log('Users updated:', payload);
      fetchUsers(); // Refetch on changes
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return { users, loading };
};

// Search hooks
export const useProductSearch = (query: string) => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    const searchProducts = async () => {
      try {
        setLoading(true);
        const data = await liveDataService.searchProducts(query);
        setResults(data);
      } catch (error) {
        console.error('Error searching products:', error);
        setResults([]);
      } finally {
        setLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchProducts, 300);
    return () => clearTimeout(debounceTimer);
  }, [query]);

  return { results, loading };
};

// Single item hooks
export const useProduct = (id: string) => {
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!id) return;

    const fetchProduct = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await liveDataService.getProductById(id);
        setProduct(data);
      } catch (err) {
        setError(err);
        console.error('Error fetching product:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  return { product, loading, error };
};

export const useOrder = (id: string) => {
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!id) return;

    const fetchOrder = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await liveDataService.getOrderById(id);
        setOrder(data);
      } catch (err) {
        setError(err);
        console.error('Error fetching order:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [id]);

  return { order, loading, error };
};

export const useCustomer = (id: string) => {
  const [customer, setCustomer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!id) return;

    const fetchCustomer = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await liveDataService.getCustomerById(id);
        setCustomer(data);
      } catch (err) {
        setError(err);
        console.error('Error fetching customer:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCustomer();
  }, [id]);

  return { customer, loading, error };
};

export default {
  useLiveData,
  useLiveProducts,
  useLiveOrders,
  useLiveCustomers,
  useLiveUsers,
  useLiveBranches,
  useLiveCategories,
  useDashboardStats,
  useRealtimeProducts,
  useRealtimeOrders,
  useRealtimeUsers,
  useProductSearch,
  useProduct,
  useOrder,
  useCustomer
};
