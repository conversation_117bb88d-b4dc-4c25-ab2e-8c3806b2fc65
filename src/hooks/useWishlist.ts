import { useState, useEffect, useCallback } from 'react';
import { wishlistService, WishlistProduct } from '../services/wishlistService';

export interface UseWishlistReturn {
  wishlistItems: WishlistProduct[];
  wishlistCount: number;
  loading: boolean;
  error: string | null;
  addToWishlist: (productId: string) => Promise<void>;
  removeFromWishlist: (productId: string) => Promise<void>;
  isInWishlist: (productId: string) => boolean;
  clearWishlist: () => Promise<void>;
  moveToCart: (productId: string, onAddToCart: (product: any) => void) => Promise<void>;
  refreshWishlist: () => Promise<void>;
}

export const useWishlist = (customerId: string): UseWishlistReturn => {
  const [wishlistItems, setWishlistItems] = useState<WishlistProduct[]>([]);
  const [wishlistCount, setWishlistCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load wishlist data
  const loadWishlist = useCallback(async () => {
    if (!customerId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const [items, count] = await Promise.all([
        wishlistService.getWishlist(customerId),
        wishlistService.getWishlistCount(customerId)
      ]);
      
      setWishlistItems(items);
      setWishlistCount(count);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load wishlist');
      console.error('Error loading wishlist:', err);
    } finally {
      setLoading(false);
    }
  }, [customerId]);

  // Add item to wishlist
  const addToWishlist = useCallback(async (productId: string) => {
    if (!customerId) return;
    
    try {
      setError(null);
      await wishlistService.addToWishlist(customerId, productId);
      
      // Refresh wishlist
      await loadWishlist();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add to wishlist');
      console.error('Error adding to wishlist:', err);
      throw err;
    }
  }, [customerId, loadWishlist]);

  // Remove item from wishlist
  const removeFromWishlist = useCallback(async (productId: string) => {
    if (!customerId) return;
    
    try {
      setError(null);
      await wishlistService.removeFromWishlist(customerId, productId);
      
      // Update local state immediately for better UX
      setWishlistItems(prev => prev.filter(item => item.id !== productId));
      setWishlistCount(prev => Math.max(0, prev - 1));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove from wishlist');
      console.error('Error removing from wishlist:', err);
      // Refresh to get correct state
      await loadWishlist();
      throw err;
    }
  }, [customerId, loadWishlist]);

  // Check if product is in wishlist
  const isInWishlist = useCallback((productId: string): boolean => {
    return wishlistItems.some(item => item.id === productId);
  }, [wishlistItems]);

  // Clear entire wishlist
  const clearWishlist = useCallback(async () => {
    if (!customerId) return;
    
    try {
      setError(null);
      await wishlistService.clearWishlist(customerId);
      
      // Update local state
      setWishlistItems([]);
      setWishlistCount(0);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear wishlist');
      console.error('Error clearing wishlist:', err);
      throw err;
    }
  }, [customerId]);

  // Move item from wishlist to cart
  const moveToCart = useCallback(async (productId: string, onAddToCart: (product: any) => void) => {
    if (!customerId) return;
    
    try {
      setError(null);
      await wishlistService.moveToCart(customerId, productId, onAddToCart);
      
      // Update local state
      setWishlistItems(prev => prev.filter(item => item.id !== productId));
      setWishlistCount(prev => Math.max(0, prev - 1));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to move to cart');
      console.error('Error moving to cart:', err);
      throw err;
    }
  }, [customerId]);

  // Refresh wishlist
  const refreshWishlist = useCallback(async () => {
    await loadWishlist();
  }, [loadWishlist]);

  // Load wishlist on mount and when customerId changes
  useEffect(() => {
    loadWishlist();
  }, [loadWishlist]);

  // Subscribe to real-time wishlist changes
  useEffect(() => {
    if (!customerId) return;

    const subscription = wishlistService.subscribeToWishlist(customerId, (payload) => {
      console.log('Wishlist real-time update:', payload);
      
      // Refresh wishlist data when changes occur
      loadWishlist();
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [customerId, loadWishlist]);

  return {
    wishlistItems,
    wishlistCount,
    loading,
    error,
    addToWishlist,
    removeFromWishlist,
    isInWishlist,
    clearWishlist,
    moveToCart,
    refreshWishlist
  };
};

export default useWishlist;
