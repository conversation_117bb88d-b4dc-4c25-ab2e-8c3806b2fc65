import { useState, useEffect } from 'react';

// Enhanced breakpoint definitions for mobile-first design
const breakpoints = {
  xs: 0,      // Mobile phones (portrait)
  sm: 480,    // Mobile phones (landscape)
  md: 768,    // Tablets (portrait)
  lg: 1024,   // Tablets (landscape) / Small laptops
  xl: 1280,   // Desktop
  '2xl': 1536 // Large desktop
};

// Touch-friendly minimum sizes
export const TOUCH_TARGET_SIZE = 44; // Minimum touch target size in pixels
export const MOBILE_PADDING = 16;    // Standard mobile padding
export const TABLET_PADDING = 24;    // Standard tablet padding
export const DESKTOP_PADDING = 32;   // Standard desktop padding

// Enhanced device type detection
export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouch: boolean;
  orientation: 'portrait' | 'landscape';
  screenSize: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  width: number;
  height: number;
  // Enhanced mobile detection
  isMobilePortrait: boolean;
  isMobileLandscape: boolean;
  isTabletPortrait: boolean;
  isTabletLandscape: boolean;
  // Device pixel ratio for high-DPI displays
  pixelRatio: number;
  // Safe area support for mobile devices
  hasSafeArea: boolean;
}



// Combined responsive information
export interface ResponsiveInfo extends DeviceInfo {
  isOnline: boolean;
  supportsTouch: boolean;
  supportsHover: boolean;
  prefersReducedMotion: boolean;
  colorScheme: 'light' | 'dark';
}

// Custom hook for responsive design
export const useResponsive = (): ResponsiveInfo => {
  const [responsiveInfo, setResponsiveInfo] = useState<ResponsiveInfo>({
    // Device info
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isTouch: false,
    orientation: 'landscape',
    screenSize: 'lg',
    width: 1024,
    height: 768,
    // Enhanced mobile detection
    isMobilePortrait: false,
    isMobileLandscape: false,
    isTabletPortrait: false,
    isTabletLandscape: false,
    pixelRatio: 1,
    hasSafeArea: false,

    // Additional info
    isOnline: true,
    supportsTouch: false,
    supportsHover: true,
    prefersReducedMotion: false,
    colorScheme: 'light'
  });

  // Detect screen size category
  const getScreenSize = (width: number): 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' => {
    if (width >= breakpoints['2xl']) return '2xl';
    if (width >= breakpoints.xl) return 'xl';
    if (width >= breakpoints.lg) return 'lg';
    if (width >= breakpoints.md) return 'md';
    if (width >= breakpoints.sm) return 'sm';
    return 'xs';
  };

  // Enhanced device type detection
  const getDeviceType = (width: number, height: number, userAgent: string) => {
    const isMobileUA = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    const isTabletUA = /iPad|Android(?!.*Mobile)/i.test(userAgent);
    const isPortrait = height > width;
    const isLandscape = width > height;

    // Enhanced mobile detection
    const isMobile = width < breakpoints.md || (isMobileUA && !isTabletUA);
    const isTablet = (width >= breakpoints.md && width < breakpoints.lg) || isTabletUA;
    const isDesktop = width >= breakpoints.lg && !isMobileUA;

    return {
      isMobile,
      isTablet,
      isDesktop,
      isMobilePortrait: isMobile && isPortrait,
      isMobileLandscape: isMobile && isLandscape,
      isTabletPortrait: isTablet && isPortrait,
      isTabletLandscape: isTablet && isLandscape,
      pixelRatio: window.devicePixelRatio || 1,
      hasSafeArea: 'CSS' in window && 'supports' in window.CSS &&
                   window.CSS.supports('padding-top: env(safe-area-inset-top)')
    };
  };



  // Update responsive information
  const updateResponsiveInfo = () => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const userAgent = navigator.userAgent;

    const deviceType = getDeviceType(width, height, userAgent);
    const screenSize = getScreenSize(width);
    
    const orientation: 'portrait' | 'landscape' = height > width ? 'portrait' : 'landscape';
    
    // Feature detection
    const supportsTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const supportsHover = window.matchMedia('(hover: hover)').matches;
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    const colorScheme: 'light' | 'dark' = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    
    setResponsiveInfo({
      ...deviceType,
      isTouch: supportsTouch,
      orientation,
      screenSize,
      width,
      height,
      isOnline: navigator.onLine,
      supportsTouch,
      supportsHover,
      prefersReducedMotion,
      colorScheme
    });
  };

  useEffect(() => {
    // Initial update
    updateResponsiveInfo();

    // Event listeners
    const handleResize = () => updateResponsiveInfo();
    const handleOrientationChange = () => {
      // Delay to ensure dimensions are updated
      setTimeout(updateResponsiveInfo, 100);
    };
    const handleOnlineStatusChange = () => updateResponsiveInfo();

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('online', handleOnlineStatusChange);
    window.addEventListener('offline', handleOnlineStatusChange);

    // Media query listeners
    const mediaQueries = [
      window.matchMedia('(display-mode: standalone)'),
      window.matchMedia('(hover: hover)'),
      window.matchMedia('(prefers-reduced-motion: reduce)'),
      window.matchMedia('(prefers-color-scheme: dark)')
    ];

    const handleMediaQueryChange = () => updateResponsiveInfo();
    
    mediaQueries.forEach(mq => {
      if (mq.addEventListener) {
        mq.addEventListener('change', handleMediaQueryChange);
      } else {
        // Fallback for older browsers
        mq.addListener(handleMediaQueryChange);
      }
    });

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('online', handleOnlineStatusChange);
      window.removeEventListener('offline', handleOnlineStatusChange);
      
      mediaQueries.forEach(mq => {
        if (mq.removeEventListener) {
          mq.removeEventListener('change', handleMediaQueryChange);
        } else {
          // Fallback for older browsers
          mq.removeListener(handleMediaQueryChange);
        }
      });
    };
  }, []);

  return responsiveInfo;
};

// Hook for specific breakpoint checks
export const useBreakpoint = (breakpoint: keyof typeof breakpoints): boolean => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia(`(min-width: ${breakpoints[breakpoint]}px)`);
    
    const handleChange = (e: MediaQueryListEvent) => {
      setMatches(e.matches);
    };

    setMatches(mediaQuery.matches);
    
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handleChange);
      return () => mediaQuery.removeListener(handleChange);
    }
  }, [breakpoint]);

  return matches;
};

// Hook for mobile-first responsive design
export const useMobileFirst = () => {
  const responsive = useResponsive();
  
  return {
    ...responsive,
    // Convenience methods
    showMobileLayout: responsive.isMobile,
    showTabletLayout: responsive.isTablet && !responsive.isMobile,
    showDesktopLayout: responsive.isDesktop,
    
    // Responsive utilities
    isSmallScreen: responsive.screenSize === 'xs' || responsive.screenSize === 'sm',
    isMediumScreen: responsive.screenSize === 'md',
    isLargeScreen: responsive.screenSize === 'lg' || responsive.screenSize === 'xl' || responsive.screenSize === '2xl',
    
    // Touch and interaction
    shouldUseTouchUI: responsive.isTouch || responsive.isMobile,
    shouldShowHoverEffects: responsive.supportsHover && !responsive.isTouch,

    // Performance
    shouldReduceAnimations: responsive.prefersReducedMotion,
    shouldOptimizeForTouch: responsive.isTouch || responsive.isMobile
  };
};



// Mobile-first utility functions
export const getMobilePadding = (screenSize: string): number => {
  switch (screenSize) {
    case 'xs':
    case 'sm':
      return MOBILE_PADDING;
    case 'md':
      return TABLET_PADDING;
    default:
      return DESKTOP_PADDING;
  }
};

export const getTouchTargetSize = (isTouch: boolean): number => {
  return isTouch ? TOUCH_TARGET_SIZE : 32; // Smaller for mouse interactions
};

export const getResponsiveColumns = (screenSize: string): number => {
  switch (screenSize) {
    case 'xs':
      return 1;
    case 'sm':
      return 2;
    case 'md':
      return 3;
    case 'lg':
      return 4;
    case 'xl':
    case '2xl':
      return 6;
    default:
      return 4;
  }
};

export const getResponsiveGridGap = (screenSize: string): string => {
  switch (screenSize) {
    case 'xs':
    case 'sm':
      return '0.5rem'; // 8px
    case 'md':
      return '0.75rem'; // 12px
    case 'lg':
      return '1rem'; // 16px
    default:
      return '1.5rem'; // 24px
  }
};

// Hook for responsive spacing
export const useResponsiveSpacing = () => {
  const responsive = useResponsive();

  return {
    padding: getMobilePadding(responsive.screenSize),
    touchTarget: getTouchTargetSize(responsive.isTouch),
    gridGap: getResponsiveGridGap(responsive.screenSize),
    columns: getResponsiveColumns(responsive.screenSize),

    // Responsive spacing utilities
    xs: responsive.screenSize === 'xs' ? '0.25rem' : '0.5rem',
    sm: responsive.screenSize === 'xs' ? '0.5rem' : '0.75rem',
    md: responsive.screenSize === 'xs' ? '0.75rem' : '1rem',
    lg: responsive.screenSize === 'xs' ? '1rem' : '1.5rem',
    xl: responsive.screenSize === 'xs' ? '1.5rem' : '2rem',
  };
};

export default useResponsive;
