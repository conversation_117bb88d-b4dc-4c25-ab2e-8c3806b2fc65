
import { useEffect } from 'react';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import { ComparisonProvider } from "./contexts/ComparisonContext";

import { performanceMonitoring } from "./services/performanceMonitoringService";
import { initializeDataSync } from "./hooks/useSyncedData";
import ErrorBoundary from "./components/ErrorBoundary";
import Index from "./pages/Index";
import ResetPassword from "./pages/ResetPassword";
import ReviewTestPage from "./pages/ReviewTestPage";
import AdminReviewTestPage from "./pages/AdminReviewTestPage";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => {
  // Initialize performance monitoring and data sync
  useEffect(() => {
    // Initialize real-time data synchronization
    initializeDataSync();

    // Track initial page load
    performanceMonitoring.trackPageView(window.location.pathname);

    // Track app initialization
    performanceMonitoring.trackFeatureUsage('app_initialization', {
      userAgent: navigator.userAgent,
      timestamp: Date.now()
    });
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <ComparisonProvider>
            <TooltipProvider>
              <Toaster />
              <Sonner />
              <BrowserRouter>
                <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/reset-password" element={<ResetPassword />} />
                  <Route path="/test-reviews" element={<ReviewTestPage />} />
                  <Route path="/admin-reviews" element={<AdminReviewTestPage />} />
                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </BrowserRouter>
            </TooltipProvider>
          </ComparisonProvider>
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
