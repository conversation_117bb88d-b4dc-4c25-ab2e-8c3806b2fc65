import { supabase } from '../integrations/supabase/client';
import { realTimeService } from './realTimeService';
import {
  PromoCode,
  PromoCodeFormData,
  PromoCodeUsage,
  PromoCodeValidation,
  PromoCodesListResponse,
  PromoCodeFilters,
  PromoCodeSort,
  PromoCodeStats,
  BulkPromoCodeOperation,
  BulkOperationResult,
  PromoCodeStatus
} from '../types/promoCodes';

class PromoCodesService {
  private cache = new Map<string, any>();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  /**
   * Get all promo codes with filtering, sorting, and pagination
   */
  async getPromoCodes(
    filters: PromoCodeFilters = {},
    sort: PromoCodeSort = { field: 'created_at', direction: 'desc' },
    page: number = 1,
    limit: number = 20
  ): Promise<PromoCodesListResponse> {
    try {
      console.log('PromoCodesService: Fetching promo codes', { filters, sort, page, limit });

      // Check if promo_codes table exists
      const { data: tableExists, error: tableError } = await supabase
        .from('promo_codes')
        .select('id')
        .limit(1);

      if (tableError) {
        console.warn('PromoCodesService: promo_codes table does not exist or is not accessible:', tableError);
        // Return empty result if table doesn't exist
        return {
          data: [],
          pagination: {
            page: 1,
            limit: 20,
            total: 0,
            total_pages: 0
          },
          filters,
          sort
        };
      }

      let query = supabase
        .from('promo_codes')
        .select('*', { count: 'exact' });

      // Apply filters
      if (filters.status && filters.status.length > 0) {
        const statusConditions = filters.status.map(status => {
          switch (status) {
            case 'active':
              return 'is_active.eq.true';
            case 'inactive':
              return 'is_active.eq.false';
            case 'expired':
              return `valid_until.lt.${new Date().toISOString()}`;
            case 'used_up':
              return 'current_usage_count.gte.usage_limit_total';
            default:
              return null;
          }
        }).filter(Boolean);
        
        if (statusConditions.length > 0) {
          query = query.or(statusConditions.join(','));
        }
      }

      if (filters.discount_type && filters.discount_type.length > 0) {
        query = query.in('discount_type', filters.discount_type);
      }

      if (filters.search) {
        query = query.or(`code.ilike.%${filters.search}%,name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }

      if (filters.is_featured !== undefined) {
        query = query.eq('is_featured', filters.is_featured);
      }

      if (filters.date_range) {
        const { start, end, type } = filters.date_range;
        const field = type === 'created' ? 'created_at' : type;
        query = query.gte(field, start).lte(field, end);
      }

      // Apply sorting
      query = query.order(sort.field, { ascending: sort.direction === 'asc' });

      // Apply pagination
      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching promo codes:', error);
        throw error;
      }

      const total = count || 0;
      const total_pages = Math.ceil(total / limit);

      return {
        data: data || [],
        pagination: {
          page,
          limit,
          total,
          total_pages
        },
        filters,
        sort
      };
    } catch (error) {
      console.error('PromoCodesService: Error in getPromoCodes:', error);
      throw error;
    }
  }

  /**
   * Get a single promo code by ID
   */
  async getPromoCodeById(id: string): Promise<PromoCode | null> {
    try {
      const cacheKey = `promo_code_${id}`;
      const cached = this.getCachedData(cacheKey);
      if (cached) return cached;

      const { data, error } = await supabase
        .from('promo_codes')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        throw error;
      }

      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('PromoCodesService: Error in getPromoCodeById:', error);
      throw error;
    }
  }

  /**
   * Get a promo code by code string
   */
  async getPromoCodeByCode(code: string): Promise<PromoCode | null> {
    try {
      const { data, error } = await supabase
        .from('promo_codes')
        .select('*')
        .eq('code', code.toUpperCase())
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        throw error;
      }

      return data;
    } catch (error) {
      console.error('PromoCodesService: Error in getPromoCodeByCode:', error);
      throw error;
    }
  }

  /**
   * Create a new promo code
   */
  async createPromoCode(formData: PromoCodeFormData): Promise<PromoCode> {
    try {
      console.log('PromoCodesService: Creating promo code', formData);

      // Ensure code is uppercase
      const promoData = {
        ...formData,
        code: formData.code.toUpperCase(),
        current_usage_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('promo_codes')
        .insert([promoData])
        .select()
        .single();

      if (error) {
        console.error('Error creating promo code:', error);
        throw error;
      }

      // Clear cache and emit real-time event
      this.clearCache();
      realTimeService.emit('promo-code-created', data);

      console.log('PromoCodesService: Promo code created successfully', data);
      return data;
    } catch (error) {
      console.error('PromoCodesService: Error in createPromoCode:', error);
      throw error;
    }
  }

  /**
   * Update an existing promo code
   */
  async updatePromoCode(id: string, formData: Partial<PromoCodeFormData>): Promise<PromoCode> {
    try {
      console.log('PromoCodesService: Updating promo code', { id, formData });

      const updateData = {
        ...formData,
        ...(formData.code && { code: formData.code.toUpperCase() }),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('promo_codes')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating promo code:', error);
        throw error;
      }

      // Clear cache and emit real-time event
      this.clearCache();
      realTimeService.emit('promo-code-updated', data);

      console.log('PromoCodesService: Promo code updated successfully', data);
      return data;
    } catch (error) {
      console.error('PromoCodesService: Error in updatePromoCode:', error);
      throw error;
    }
  }

  /**
   * Delete a promo code
   */
  async deletePromoCode(id: string): Promise<boolean> {
    try {
      console.log('PromoCodesService: Deleting promo code', id);

      const { error } = await supabase
        .from('promo_codes')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting promo code:', error);
        throw error;
      }

      // Clear cache and emit real-time event
      this.clearCache();
      realTimeService.emit('promo-code-deleted', { id });

      console.log('PromoCodesService: Promo code deleted successfully');
      return true;
    } catch (error) {
      console.error('PromoCodesService: Error in deletePromoCode:', error);
      throw error;
    }
  }

  /**
   * Validate a promo code for use
   */
  async validatePromoCode(
    code: string,
    userId?: string,
    orderAmount: number = 0
  ): Promise<PromoCodeValidation> {
    try {
      console.log('PromoCodesService: Validating promo code', { code, userId, orderAmount });

      const { data, error } = await supabase
        .rpc('is_promo_code_valid', {
          p_code: code.toUpperCase(),
          p_user_id: userId,
          p_order_amount: orderAmount
        });

      if (error) {
        console.error('Error validating promo code:', error);
        throw error;
      }

      const result = data[0];
      let discountAmount = 0;

      if (result.is_valid) {
        const { data: discountData, error: discountError } = await supabase
          .rpc('calculate_promo_discount', {
            p_code: code.toUpperCase(),
            p_order_amount: orderAmount
          });

        if (!discountError && discountData) {
          discountAmount = discountData;
        }
      }

      return {
        is_valid: result.is_valid,
        error_message: result.error_message,
        promo_data: result.promo_data,
        discount_amount: discountAmount
      };
    } catch (error) {
      console.error('PromoCodesService: Error in validatePromoCode:', error);
      return {
        is_valid: false,
        error_message: 'Error validating promo code'
      };
    }
  }

  /**
   * Get promo code statistics
   */
  async getPromoCodeStats(): Promise<PromoCodeStats> {
    try {
      const cacheKey = 'promo_code_stats';
      const cached = this.getCachedData(cacheKey);
      if (cached) return cached;

      // Get basic counts
      const { data: allCodes, error: allError } = await supabase
        .from('promo_codes')
        .select('id, is_active, valid_until, current_usage_count, usage_limit_total, is_featured');

      if (allError) {
        console.warn('PromoCodesService: Error fetching promo code stats, returning defaults:', allError);
        // Return default stats if table doesn't exist
        const defaultStats: PromoCodeStats = {
          total_codes: 0,
          active_codes: 0,
          expired_codes: 0,
          featured_codes: 0,
          total_usage: 0,
          total_discount_given: 0,
          expiring_soon: []
        };
        return defaultStats;
      }

      const now = new Date();
      const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

      const stats: PromoCodeStats = {
        total_codes: allCodes?.length || 0,
        active_codes: allCodes?.filter(code => code.is_active).length || 0,
        expired_codes: allCodes?.filter(code =>
          code.valid_until && new Date(code.valid_until) < now
        ).length || 0,
        featured_codes: allCodes?.filter(code => code.is_featured).length || 0,
        total_usage: allCodes?.reduce((sum, code) => sum + (code.current_usage_count || 0), 0) || 0,
        total_discount_given: 0,
        expiring_soon: []
      };

      // Get total discount given
      const { data: usageData, error: usageError } = await supabase
        .from('promo_code_usage')
        .select('discount_amount');

      if (!usageError && usageData) {
        stats.total_discount_given = usageData.reduce((sum, usage) => sum + usage.discount_amount, 0);
      }

      // Get most used code
      const mostUsedCode = allCodes?.reduce((max, code) =>
        (code.current_usage_count || 0) > (max?.current_usage_count || 0) ? code : max
      );

      if (mostUsedCode && mostUsedCode.current_usage_count > 0) {
        const { data: codeDetails } = await supabase
          .from('promo_codes')
          .select('code, name, current_usage_count')
          .eq('id', mostUsedCode.id)
          .single();

        if (codeDetails) {
          stats.most_used_code = {
            code: codeDetails.code,
            name: codeDetails.name,
            usage_count: codeDetails.current_usage_count
          };
        }
      }

      // Get codes expiring soon
      const { data: expiringSoon, error: expiringError } = await supabase
        .from('promo_codes')
        .select('*')
        .gte('valid_until', now.toISOString())
        .lte('valid_until', sevenDaysFromNow.toISOString())
        .eq('is_active', true)
        .order('valid_until', { ascending: true });

      if (!expiringError && expiringSoon) {
        stats.expiring_soon = expiringSoon;
      }

      this.setCachedData(cacheKey, stats);
      return stats;
    } catch (error) {
      console.error('PromoCodesService: Error in getPromoCodeStats:', error);
      throw error;
    }
  }

  /**
   * Generate a unique promo code
   */
  async generateUniqueCode(prefix: string = '', length: number = 8): Promise<string> {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      let code = prefix.toUpperCase();

      for (let i = 0; i < length - prefix.length; i++) {
        code += characters.charAt(Math.floor(Math.random() * characters.length));
      }

      // Check if code already exists
      const existing = await this.getPromoCodeByCode(code);
      if (!existing) {
        return code;
      }

      attempts++;
    }

    throw new Error('Unable to generate unique promo code after multiple attempts');
  }

  /**
   * Cache management methods
   */
  private getCachedData(key: string): any {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  private clearCache(): void {
    this.cache.clear();
  }

  /**
   * Clear specific cache entries
   */
  clearPromoCodeCache(id?: string): void {
    if (id) {
      this.cache.delete(`promo_code_${id}`);
    } else {
      this.clearCache();
    }
  }
}

// Export singleton instance
export const promoCodesService = new PromoCodesService();
