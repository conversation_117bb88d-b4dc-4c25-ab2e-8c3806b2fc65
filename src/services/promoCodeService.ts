
import { supabase } from '../integrations/supabase/client';
import { realTimeService } from './realTimeService';

export interface PromoCode {
  id: string;
  code: string;
  name: string;
  description?: string;
  discount_type: 'percentage' | 'fixed_amount';
  discount_value: number;
  minimum_order_amount?: number;
  maximum_discount_amount?: number;
  usage_limit_total?: number;
  current_usage_count: number;
  valid_from: string;
  valid_until?: string;
  is_active: boolean;
  is_featured?: boolean;
  created_at: string;
  updated_at: string;
}

export interface PromoCodeValidation {
  isValid: boolean;
  discount: number;
  discountType: 'percentage' | 'fixed';
  message: string;
  promoCode?: PromoCode;
}

class PromoCodeService {
  private promoCodes: PromoCode[] = [];
  private subscribers: ((promoCodes: PromoCode[]) => void)[] = [];

  constructor() {
    this.initializeRealTimeSubscription();
  }

  private initializeRealTimeSubscription() {
    // Subscribe to real-time updates from admin dashboard
    realTimeService.subscribe('promo-code-created', (data) => {
      console.log('PromoCodeService: New promo code created', data);
      this.loadActivePromoCodes();
    });

    realTimeService.subscribe('promo-code-updated', (data) => {
      console.log('PromoCodeService: Promo code updated', data);
      this.loadActivePromoCodes();
    });

    realTimeService.subscribe('promo-code-used', (data) => {
      console.log('PromoCodeService: Promo code used', data);
      // Update the local cache with the new usage count
      this.promoCodes = this.promoCodes.map(promo =>
        promo.id === data.promoCode.id
          ? { ...promo, used_count: data.newUsedCount, updated_at: data.timestamp }
          : promo
      );
      this.notifySubscribers();
    });

    // Subscribe to database changes
    supabase
      .channel('promo-codes-changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'promo_codes' },
        (payload) => {
          console.log('PromoCodeService: Database change detected', payload);
          this.loadActivePromoCodes();
        }
      )
      .subscribe();
  }

  async loadActivePromoCodes(): Promise<PromoCode[]> {
    try {
      console.log('PromoCodeService: Loading active promo codes from database...');

      const { data, error } = await supabase
        .from('promo_codes')
        .select(`
          id,
          code,
          name,
          description,
          discount_type,
          discount_value,
          minimum_order_amount,
          maximum_discount_amount,
          usage_limit_total,
          current_usage_count,
          valid_from,
          valid_until,
          is_active,
          is_featured,
          created_at,
          updated_at
        `)
        .eq('is_active', true)
        .order('is_featured', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) {
        console.error('PromoCodeService: Error loading active promo codes:', error);
        return this.promoCodes;
      }

      // Filter out expired codes and codes that have reached usage limit
      const now = new Date();
      const activePromoCodes = (data || []).filter(promo => {
        // Check if expired
        if (promo.valid_until && new Date(promo.valid_until) <= now) {
          return false;
        }

        // Check if usage limit reached
        if (promo.usage_limit_total && promo.current_usage_count >= promo.usage_limit_total) {
          return false;
        }

        // Check if valid period has started
        if (promo.valid_from && new Date(promo.valid_from) > now) {
          return false;
        }

        return true;
      });

      this.promoCodes = activePromoCodes;
      this.notifySubscribers();

      console.log('PromoCodeService: Loaded active promo codes:', activePromoCodes.length, 'codes');
      return activePromoCodes;
    } catch (error) {
      console.error('PromoCodeService: Error loading active promo codes:', error);
      return this.promoCodes;
    }
  }

  async getActivePromoCodes(): Promise<PromoCode[]> {
    if (this.promoCodes.length === 0) {
      await this.loadActivePromoCodes();
    }
    return this.promoCodes;
  }

  async validatePromoCode(code: string, orderTotal: number): Promise<PromoCodeValidation> {
    try {
      const { data, error } = await supabase
        .from('promo_codes')
        .select('*')
        .eq('code', code.toUpperCase())
        .eq('is_active', true)
        .single();

      if (error || !data) {
        return {
          isValid: false,
          discount: 0,
          discountType: 'percentage',
          message: 'Invalid promo code'
        };
      }

      const promoCode = data as PromoCode;

      // Check if expired
      if (promoCode.valid_until && new Date(promoCode.valid_until) <= new Date()) {
        return {
          isValid: false,
          discount: 0,
          discountType: promoCode.discount_type,
          message: 'This promo code has expired'
        };
      }

      // Check if valid period has started
      if (promoCode.valid_from && new Date(promoCode.valid_from) > new Date()) {
        return {
          isValid: false,
          discount: 0,
          discountType: promoCode.discount_type,
          message: 'This promo code is not yet valid'
        };
      }

      // Check minimum order requirement
      if (promoCode.minimum_order_amount && orderTotal < promoCode.minimum_order_amount) {
        return {
          isValid: false,
          discount: 0,
          discountType: promoCode.discount_type,
          message: `Minimum order amount is ${promoCode.minimum_order_amount} Dh`
        };
      }

      // Check usage limit
      if (promoCode.usage_limit_total && promoCode.current_usage_count >= promoCode.usage_limit_total) {
        return {
          isValid: false,
          discount: 0,
          discountType: promoCode.discount_type,
          message: 'This promo code has reached its usage limit'
        };
      }

      // Calculate discount
      let discount = 0;
      if (promoCode.discount_type === 'percentage') {
        discount = (orderTotal * promoCode.discount_value) / 100;
        // Apply maximum discount limit if specified
        if (promoCode.maximum_discount_amount && discount > promoCode.maximum_discount_amount) {
          discount = promoCode.maximum_discount_amount;
        }
      } else if (promoCode.discount_type === 'fixed_amount') {
        discount = promoCode.discount_value;
      }

      return {
        isValid: true,
        discount: Math.min(discount, orderTotal), // Don't exceed order total
        discountType: promoCode.discount_type,
        message: `Promo code applied! You saved ${discount.toFixed(2)} Dh`,
        promoCode
      };
    } catch (error) {
      console.error('Error validating promo code:', error);
      return {
        isValid: false,
        discount: 0,
        discountType: 'percentage',
        message: 'Error validating promo code'
      };
    }
  }

  async applyPromoCode(code: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Applying promo code:', code);

      // First get the current promo code data
      const { data: currentPromo, error: fetchError } = await supabase
        .from('promo_codes')
        .select('*')
        .eq('code', code.toUpperCase())
        .eq('is_active', true)
        .single();

      if (fetchError || !currentPromo) {
        console.error('Error fetching promo code for application:', fetchError);
        return { success: false, error: 'Promo code not found or inactive' };
      }

      // Check if usage limit would be exceeded
      if (currentPromo.usage_limit && currentPromo.used_count >= currentPromo.usage_limit) {
        console.error('Promo code usage limit exceeded:', code);
        return { success: false, error: 'Promo code usage limit exceeded' };
      }

      // Increment usage count
      const { data: updatedPromo, error: updateError } = await supabase
        .from('promo_codes')
        .update({
          used_count: (currentPromo.used_count || 0) + 1,
          updated_at: new Date().toISOString()
        })
        .eq('code', code.toUpperCase())
        .select('*')
        .single();

      if (updateError) {
        console.error('Error updating promo code usage count:', updateError);
        return { success: false, error: 'Failed to update usage count' };
      }

      console.log('Promo code usage count incremented successfully:', updatedPromo);

      // Emit real-time event for synchronization
      realTimeService.emit('promo-code-used', {
        promoCode: updatedPromo,
        previousUsedCount: currentPromo.used_count || 0,
        newUsedCount: updatedPromo.used_count,
        timestamp: new Date().toISOString()
      });

      // Reload active promo codes to update the cache
      await this.loadActivePromoCodes();

      return { success: true };
    } catch (error) {
      console.error('Error applying promo code:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  subscribe(callback: (promoCodes: PromoCode[]) => void): () => void {
    this.subscribers.push(callback);

    // Immediately call with current data
    callback(this.promoCodes);

    // Return unsubscribe function
    return () => {
      const index = this.subscribers.indexOf(callback);
      if (index > -1) {
        this.subscribers.splice(index, 1);
      }
    };
  }

  private notifySubscribers() {
    this.subscribers.forEach(callback => callback(this.promoCodes));
  }

  // Get promo codes for display in shopping cart
  async getAvailableOffers(): Promise<PromoCode[]> {
    const activePromoCodes = await this.getActivePromoCodes();

    // Return only codes that haven't reached their usage limit and are currently valid
    const now = new Date();
    return activePromoCodes.filter(promo => {
      // Check usage limit
      if (promo.usage_limit_total && promo.current_usage_count >= promo.usage_limit_total) {
        return false;
      }

      // Check validity period
      if (promo.valid_until && new Date(promo.valid_until) <= now) {
        return false;
      }

      if (promo.valid_from && new Date(promo.valid_from) > now) {
        return false;
      }

      return true;
    });
  }
}

// Export singleton instance
export const promoCodeService = new PromoCodeService();

// Legacy exports for backward compatibility
export const getPromoCodes = () => promoCodeService.getActivePromoCodes();
export const getActivePromoCodes = () => promoCodeService.getActivePromoCodes();

export const validatePromoCode = (code: string, orderAmount: number) => promoCodeService.validatePromoCode(code, orderAmount);
export const applyPromoCode = (code: string) => promoCodeService.applyPromoCode(code);

// Additional exports for admin functionality
export const createPromoCode = async (promoData: Omit<PromoCode, 'id' | 'created_at' | 'updated_at' | 'used_count'>): Promise<PromoCode> => {
  try {
    const { data, error } = await supabase
      .from('promo_codes')
      .insert({
        code: promoData.code,
        discount_type: promoData.discount_type,
        discount_value: promoData.discount_value,
        minimum_order: promoData.minimum_order || null,
        usage_limit: promoData.usage_limit || null,
        expires_at: promoData.expires_at || null,
        is_active: promoData.is_active,
        used_count: 0
      })
      .select('*')
      .single();

    if (error) {
      console.error('Error creating promo code:', error);
      throw error;
    }

    const newPromo = data as PromoCode;

    // Emit real-time event
    realTimeService.emit('promo-code-created', {
      promoCode: newPromo,
      timestamp: new Date().toISOString()
    });

    return newPromo;
  } catch (error) {
    console.error('Error creating promo code:', error);
    throw error;
  }
};

export const updatePromoCode = async (id: string, updates: Partial<PromoCode>): Promise<PromoCode | null> => {
  try {
    const { data, error } = await supabase
      .from('promo_codes')
      .update({
        code: updates.code,
        discount_type: updates.discount_type,
        discount_value: updates.discount_value,
        minimum_order: updates.minimum_order || null,
        usage_limit: updates.usage_limit || null,
        expires_at: updates.expires_at || null,
        is_active: updates.is_active,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating promo code:', error);
      throw error;
    }

    const updatedPromo = data as PromoCode;

    // Emit real-time event
    realTimeService.emit('promo-code-updated', {
      promoCode: updatedPromo,
      timestamp: new Date().toISOString()
    });

    return updatedPromo;
  } catch (error) {
    console.error('Error updating promo code:', error);
    throw error;
  }
};

export const deletePromoCode = async (id: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('promo_codes')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting promo code:', error);
      return false;
    }

    // Emit real-time event
    realTimeService.emit('promo-code-deleted', {
      promoCodeId: id,
      timestamp: new Date().toISOString()
    });

    return true;
  } catch (error) {
    console.error('Error deleting promo code:', error);
    return false;
  }
};

export default promoCodeService;
