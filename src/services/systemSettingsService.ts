/**
 * System Settings Service
 * Handles all database operations for system settings with real-time synchronization
 */

import { supabase } from '../integrations/supabase/client';
import { realTimeService } from './realTimeService';
import {
  CompanySettings,
  SystemConfig,
  PaymentMethodConfig,
  SystemSettingsState,
  GeneralSettings,
  SecuritySettings,
  NotificationSettings,
  EmailSettings,
  SystemConfigCategory
} from '../types/systemSettings';

class SystemSettingsService {
  private cache: Map<string, any> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  // ===== COMPANY SETTINGS =====

  /**
   * Load company settings from database
   */
  async getCompanySettings(): Promise<CompanySettings> {
    const cacheKey = 'company_settings';
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const { data, error } = await supabase
        .from('company_settings')
        .select('*')
        .single();

      if (error) {
        console.error('Error loading company settings:', error);
        throw error;
      }

      const companySettings: CompanySettings = {
        id: data.id,
        name: data.name,
        address: data.address || '',
        phone: data.phone || '',
        email: data.email || '',
        tax_id: data.tax_id || '',
        ice_number: data.ice_number || '',
        logo_url: data.logo_url || '',
        created_at: data.created_at,
        updated_at: data.updated_at
      };

      this.setCachedData(cacheKey, companySettings);
      return companySettings;
    } catch (error) {
      console.error('Error in getCompanySettings:', error);
      throw error;
    }
  }

  /**
   * Save company settings to database
   */
  async saveCompanySettings(settings: Partial<CompanySettings>): Promise<CompanySettings> {
    try {
      const updateData = {
        name: settings.name,
        address: settings.address || '',
        phone: settings.phone || '',
        email: settings.email || '',
        tax_id: settings.tax_id || '',
        ice_number: settings.ice_number || '',
        logo_url: settings.logo_url || '',
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('company_settings')
        .upsert({
          id: '550e8400-e29b-41d4-a716-************', // Fixed UUID from database setup
          ...updateData
        })
        .select()
        .single();

      if (error) {
        console.error('Error saving company settings:', error);
        throw error;
      }

      // Clear cache and broadcast update
      this.clearCache('company_settings');
      realTimeService.emit('company-settings-updated', {
        type: 'company_settings',
        data,
        timestamp: new Date().toISOString()
      });

      return data;
    } catch (error) {
      console.error('Error in saveCompanySettings:', error);
      throw error;
    }
  }

  // ===== SYSTEM CONFIGS =====

  /**
   * Load all system configs from database
   */
  async getSystemConfigs(): Promise<SystemConfig[]> {
    const cacheKey = 'system_configs';
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const { data, error } = await supabase
        .from('system_configs')
        .select('*')
        .order('category', { ascending: true })
        .order('key', { ascending: true });

      if (error) {
        console.error('Error loading system configs:', error);
        throw error;
      }

      this.setCachedData(cacheKey, data || []);
      return data || [];
    } catch (error) {
      console.error('Error in getSystemConfigs:', error);
      throw error;
    }
  }

  /**
   * Get system configs organized by category
   */
  async getSystemConfigsByCategory(): Promise<{
    general: GeneralSettings;
    security: SecuritySettings;
    notifications: NotificationSettings;
    email: EmailSettings;
  }> {
    const configs = await this.getSystemConfigs();
    
    const result = {
      general: {} as GeneralSettings,
      security: {} as SecuritySettings,
      notifications: {} as NotificationSettings,
      email: {} as EmailSettings
    };

    configs.forEach(config => {
      const value = this.parseConfigValue(config.value, config.data_type);
      
      switch (config.category) {
        case 'general':
          (result.general as any)[config.key] = value;
          break;
        case 'security':
          (result.security as any)[config.key] = value;
          break;
        case 'notifications':
          (result.notifications as any)[config.key] = value;
          break;
        case 'email':
          (result.email as any)[config.key] = value;
          break;
      }
    });

    return result;
  }

  /**
   * Save system configs to database
   */
  async saveSystemConfigs(configs: SystemConfig[]): Promise<SystemConfig[]> {
    try {
      const updateData = configs.map(config => ({
        category: config.category,
        key: config.key,
        value: this.stringifyConfigValue(config.value, config.data_type),
        description: config.description || '',
        data_type: config.data_type,
        updated_at: new Date().toISOString()
      }));

      const { data, error } = await supabase
        .from('system_configs')
        .upsert(updateData, { 
          onConflict: 'category,key',
          ignoreDuplicates: false 
        })
        .select();

      if (error) {
        console.error('Error saving system configs:', error);
        throw error;
      }

      // Clear cache and broadcast update
      this.clearCache('system_configs');
      realTimeService.emit('system-configs-updated', {
        type: 'system_configs',
        data: data || [],
        timestamp: new Date().toISOString()
      });

      return data || [];
    } catch (error) {
      console.error('Error in saveSystemConfigs:', error);
      throw error;
    }
  }

  // ===== PAYMENT METHODS =====

  /**
   * Load payment methods from database
   */
  async getPaymentMethods(): Promise<PaymentMethodConfig[]> {
    const cacheKey = 'payment_methods';
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const { data, error } = await supabase
        .from('payment_methods_config')
        .select('*')
        .order('method_type', { ascending: true });

      if (error) {
        console.error('Error loading payment methods:', error);
        throw error;
      }

      this.setCachedData(cacheKey, data || []);
      return data || [];
    } catch (error) {
      console.error('Error in getPaymentMethods:', error);
      throw error;
    }
  }

  /**
   * Save payment methods to database
   */
  async savePaymentMethods(methods: PaymentMethodConfig[]): Promise<PaymentMethodConfig[]> {
    try {
      const updateData = methods.map(method => ({
        id: method.id,
        method_type: method.method_type,
        is_enabled: method.is_enabled,
        configuration: method.configuration,
        updated_at: new Date().toISOString()
      }));

      const { data, error } = await supabase
        .from('payment_methods_config')
        .upsert(updateData)
        .select();

      if (error) {
        console.error('Error saving payment methods:', error);
        throw error;
      }

      // Clear cache and broadcast update
      this.clearCache('payment_methods');
      realTimeService.emit('payment-methods-updated', {
        type: 'payment_methods',
        data: data || [],
        timestamp: new Date().toISOString()
      });

      return data || [];
    } catch (error) {
      console.error('Error in savePaymentMethods:', error);
      throw error;
    }
  }

  // ===== UTILITY METHODS =====

  /**
   * Parse config value based on data type
   */
  parseConfigValue(value: string, dataType: string): any {
    try {
      switch (dataType) {
        case 'boolean':
          return value === 'true' || value === true;
        case 'number':
          return parseFloat(value);
        case 'json':
          return JSON.parse(value);
        case 'string':
        default:
          // Remove quotes if present
          return value.startsWith('"') && value.endsWith('"') 
            ? value.slice(1, -1) 
            : value;
      }
    } catch (error) {
      console.error('Error parsing config value:', { value, dataType, error });
      return value;
    }
  }

  /**
   * Stringify config value based on data type
   */
  stringifyConfigValue(value: any, dataType: string): string {
    try {
      switch (dataType) {
        case 'boolean':
          return String(Boolean(value));
        case 'number':
          return String(Number(value));
        case 'json':
          return JSON.stringify(value);
        case 'string':
        default:
          return typeof value === 'string' ? `"${value}"` : String(value);
      }
    } catch (error) {
      console.error('Error stringifying config value:', { value, dataType, error });
      return String(value);
    }
  }

  // ===== CACHE MANAGEMENT =====

  private getCachedData(key: string): any {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data;
    }
    return null;
  }

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  private clearCache(key?: string): void {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Clear all system settings cache
   */
  clearAllCache(): void {
    this.clearCache();
  }
}

// Export singleton instance
export const systemSettingsService = new SystemSettingsService();
