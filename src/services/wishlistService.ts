import { supabase } from '@/integrations/supabase/client';
import { liveDataService } from './liveDataService';

export interface WishlistItem {
  id: string;
  customer_id: string;
  product_id: string;
  added_at: string;
  product?: {
    id: string;
    name: string;
    price: number;
    reseller_price?: number;
    stock_quantity: number;
    featured_image?: string;
    category_name?: string;
    is_active: boolean;
  };
}

export interface WishlistProduct {
  id: string;
  title: string;
  price: number;
  resellerPrice: number;
  image: string;
  category: string;
  inStock: boolean;
  stock: number;
  rating: number;
  addedAt: string;
}

class WishlistService {
  // Add item to wishlist
  async addToWishlist(customerId: string, productId: string): Promise<WishlistItem> {
    try {
      // Check if item already exists
      const { data: existing } = await supabase
        .from('wishlists')
        .select('id')
        .eq('customer_id', customerId)
        .eq('product_id', productId)
        .single();

      if (existing) {
        throw new Error('Product already in wishlist');
      }

      const { data, error } = await supabase
        .from('wishlists')
        .insert({
          customer_id: customerId,
          product_id: productId
        })
        .select(`
          *,
          products (
            id,
            title,
            price,
            reseller_price,
            stock,
            featured_image,
            categories (name),
            is_active
          )
        `)
        .single();

      if (error) {
        console.error('Error adding to wishlist:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in addToWishlist:', error);
      throw error;
    }
  }

  // Remove item from wishlist
  async removeFromWishlist(customerId: string, productId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('wishlists')
        .delete()
        .eq('customer_id', customerId)
        .eq('product_id', productId);

      if (error) {
        console.error('Error removing from wishlist:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error in removeFromWishlist:', error);
      throw error;
    }
  }

  // Get user's wishlist
  async getWishlist(customerId: string): Promise<WishlistProduct[]> {
    try {
      const { data, error } = await supabase
        .from('wishlists')
        .select(`
          *,
          products (
            id,
            title,
            price,
            reseller_price,
            stock,
            featured_image,
            categories (name),
            is_active
          )
        `)
        .eq('customer_id', customerId)
        .eq('products.is_active', true)
        .order('added_at', { ascending: false });

      if (error) {
        console.error('Error fetching wishlist:', error);
        throw error;
      }

      // Transform to frontend format
      const wishlistProducts: WishlistProduct[] = (data || []).map(item => ({
        id: item.product_id,
        title: item.products?.title || 'Unknown Product',
        price: Number(item.products?.price || 0),
        resellerPrice: Number(item.products?.reseller_price || item.products?.price || 0),
        image: item.products?.featured_image || '/placeholder.svg',
        category: item.products?.categories?.name || 'Unknown',
        inStock: (item.products?.stock || 0) > 0,
        stock: item.products?.stock || 0,
        rating: 4.0 + Math.random() * 1, // Placeholder - would get from reviews
        addedAt: item.added_at
      }));

      return wishlistProducts;
    } catch (error) {
      console.error('Error in getWishlist:', error);
      return [];
    }
  }

  // Check if product is in wishlist
  async isInWishlist(customerId: string, productId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('wishlists')
        .select('id')
        .eq('customer_id', customerId)
        .eq('product_id', productId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        console.error('Error checking wishlist:', error);
        return false;
      }

      return !!data;
    } catch (error) {
      console.error('Error in isInWishlist:', error);
      return false;
    }
  }

  // Get wishlist count for user
  async getWishlistCount(customerId: string): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('wishlists')
        .select('*', { count: 'exact', head: true })
        .eq('customer_id', customerId);

      if (error) {
        console.error('Error getting wishlist count:', error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      console.error('Error in getWishlistCount:', error);
      return 0;
    }
  }

  // Clear entire wishlist
  async clearWishlist(customerId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('wishlists')
        .delete()
        .eq('customer_id', customerId);

      if (error) {
        console.error('Error clearing wishlist:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error in clearWishlist:', error);
      throw error;
    }
  }

  // Move wishlist item to cart (remove from wishlist, add to cart)
  async moveToCart(customerId: string, productId: string, onAddToCart: (product: any) => void): Promise<void> {
    try {
      // Get product details first
      const { data: wishlistItem, error: fetchError } = await supabase
        .from('wishlists')
        .select(`
          *,
          products (
            id,
            name,
            price,
            reseller_price,
            stock_quantity,
            featured_image,
            categories (name)
          )
        `)
        .eq('customer_id', customerId)
        .eq('product_id', productId)
        .single();

      if (fetchError || !wishlistItem) {
        throw new Error('Wishlist item not found');
      }

      // Add to cart
      const cartProduct = {
        id: parseInt(productId.replace('PRD-', '')) || parseInt(productId),
        title: wishlistItem.products?.name || 'Unknown Product',
        category: wishlistItem.products?.categories?.name || 'Unknown',
        price: Number(wishlistItem.products?.price || 0),
        resellerPrice: Number(wishlistItem.products?.reseller_price || wishlistItem.products?.price || 0),
        image: wishlistItem.products?.featured_image || '/placeholder.svg',
        rating: 4.0 + Math.random() * 1,
        inStock: (wishlistItem.products?.stock_quantity || 0) > 0,
        stock: wishlistItem.products?.stock_quantity || 0
      };

      onAddToCart(cartProduct);

      // Remove from wishlist
      await this.removeFromWishlist(customerId, productId);
    } catch (error) {
      console.error('Error in moveToCart:', error);
      throw error;
    }
  }

  // Subscribe to wishlist changes
  subscribeToWishlist(customerId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`wishlist-${customerId}`)
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'wishlists',
          filter: `customer_id=eq.${customerId}`
        }, 
        callback
      )
      .subscribe();
  }
}

export const wishlistService = new WishlistService();
export default wishlistService;
