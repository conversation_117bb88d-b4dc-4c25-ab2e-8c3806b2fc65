@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    /* YalaOffice Brand Colors */
    --yala-teal: 158 64% 52%; /* #0d9488 - teal-600 */
    --yala-amber: 45 93% 47%; /* #f29f06 - amber-500 */

    /* Mobile-specific variables */
    --mobile-padding: 1rem; /* 16px */
    --tablet-padding: 1.5rem; /* 24px */
    --desktop-padding: 2rem; /* 32px */
    --touch-target: 2.75rem; /* 44px - minimum touch target */
    --mobile-header-height: 4rem; /* 64px */
    --mobile-nav-height: 4rem; /* 64px */

    /* Safe area support for mobile devices */
    --safe-area-top: env(safe-area-inset-top, 0px);
    --safe-area-bottom: env(safe-area-inset-bottom, 0px);
    --safe-area-left: env(safe-area-inset-left, 0px);
    --safe-area-right: env(safe-area-inset-right, 0px);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    /* Improve text rendering on mobile */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Prevent horizontal scroll on mobile */
    overflow-x: hidden;
  }

  /* Mobile-first responsive typography */
  html {
    /* Base font size for mobile */
    font-size: 14px;
  }

  @media (min-width: 480px) {
    html {
      font-size: 15px;
    }
  }

  @media (min-width: 768px) {
    html {
      font-size: 16px;
    }
  }

  /* Improve touch interactions */
  button,
  [role="button"],
  input[type="submit"],
  input[type="button"] {
    touch-action: manipulation;
  }

  /* Prevent zoom on input focus on iOS */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  textarea,
  select {
    font-size: 16px;
  }

  @media (max-width: 767px) {
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="number"],
    input[type="tel"],
    input[type="url"],
    input[type="search"],
    textarea,
    select {
      font-size: 16px !important;
    }
  }
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  /* Mobile-specific utility classes */
  .mobile-padding {
    padding: var(--mobile-padding);
  }

  .tablet-padding {
    padding: var(--tablet-padding);
  }

  .desktop-padding {
    padding: var(--desktop-padding);
  }

  .touch-target {
    min-height: var(--touch-target);
    min-width: var(--touch-target);
  }

  .safe-area-top {
    padding-top: var(--safe-area-top);
  }

  .safe-area-bottom {
    padding-bottom: var(--safe-area-bottom);
  }

  .safe-area-left {
    padding-left: var(--safe-area-left);
  }

  .safe-area-right {
    padding-right: var(--safe-area-right);
  }

  .pb-safe {
    padding-bottom: calc(1rem + var(--safe-area-bottom));
  }

  .pt-safe {
    padding-top: calc(1rem + var(--safe-area-top));
  }

  /* YalaOffice brand gradients */
  .bg-yala-gradient {
    background: linear-gradient(135deg, hsl(var(--yala-teal)) 0%, hsl(var(--yala-amber)) 100%);
  }

  .text-yala-gradient {
    background: linear-gradient(135deg, hsl(var(--yala-teal)) 0%, hsl(var(--yala-amber)) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Mobile-optimized animations */
  @media (prefers-reduced-motion: reduce) {
    .animate-spin,
    .animate-pulse,
    .animate-bounce {
      animation: none;
    }
  }

  /* Touch-friendly hover states */
  @media (hover: hover) and (pointer: fine) {
    .hover-lift:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  /* Mobile-first responsive grid */
  .mobile-grid {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
  }

  @media (min-width: 480px) {
    .mobile-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 768px) {
    .mobile-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .mobile-grid {
      grid-template-columns: repeat(4, 1fr);
      gap: 2rem;
    }
  }

  /* Mobile-optimized text sizes */
  .text-mobile-xs { font-size: 0.75rem; line-height: 1rem; }
  .text-mobile-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .text-mobile-base { font-size: 1rem; line-height: 1.5rem; }
  .text-mobile-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .text-mobile-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .text-mobile-2xl { font-size: 1.5rem; line-height: 2rem; }

  /* Responsive spacing utilities */
  .space-mobile-y > * + * {
    margin-top: 0.5rem;
  }

  @media (min-width: 768px) {
    .space-mobile-y > * + * {
      margin-top: 1rem;
    }
  }

  /* Mobile-optimized shadows */
  .shadow-mobile {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  }

  .shadow-mobile-lg {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  }

  /* Prevent text selection on touch interfaces */
  .touch-none {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Mobile-optimized focus states */
  .focus-mobile:focus {
    outline: 2px solid hsl(var(--yala-teal));
    outline-offset: 2px;
  }

  /* Responsive container */
  .container-mobile {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 640px) {
    .container-mobile {
      max-width: 640px;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 768px) {
    .container-mobile {
      max-width: 768px;
    }
  }

  @media (min-width: 1024px) {
    .container-mobile {
      max-width: 1024px;
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  @media (min-width: 1280px) {
    .container-mobile {
      max-width: 1280px;
    }
  }

  /* Hide scrollbars while maintaining scroll functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }

  /* Mobile touch enhancements */
  .touch-action-manipulation {
    touch-action: manipulation;
  }

  /* Safe area support for devices with notches/home indicators */
  .pb-safe {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .pt-safe {
    padding-top: env(safe-area-inset-top);
  }

  /* Enhanced mobile tap targets */
  .mobile-tap-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Smooth scrolling for mobile */
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Mobile-optimized transitions */
  .mobile-transition {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Touch feedback */
  .touch-feedback:active {
    transform: scale(0.98);
    transition: transform 0.1s ease-in-out;
  }
}