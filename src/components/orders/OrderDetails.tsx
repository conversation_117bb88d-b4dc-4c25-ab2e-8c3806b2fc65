
import { useState } from 'react';
import { X, Package, MapPin, CreditCard, Clock, Download, Edit3, FileText } from 'lucide-react';
import { Order, OrderStatus, PaymentStatus } from '../../types/order';
import { updateOrderStatus, updatePaymentStatus } from '../../services/orderService';
import { getOrderStatusColor, getPaymentStatusColor, formatOrderDate, generateInvoiceNumber } from '../../utils/orderUtils';
import Invoice from './Invoice';

interface OrderDetailsProps {
  order: Order;
  onClose: () => void;
  onUpdate: () => void;
  isAdmin?: boolean;
}

const OrderDetails = ({ order, onClose, onUpdate, isAdmin = false }: OrderDetailsProps) => {
  const [selectedOrder, setSelectedOrder] = useState(order);
  const [showInvoice, setShowInvoice] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Format delivery address from JSON to readable string
  const formatDeliveryAddress = (address: any): string => {
    if (!address) return 'No delivery address provided';

    // If it's already a string, return it
    if (typeof address === 'string') {
      try {
        // Try to parse if it's a JSON string
        const parsed = JSON.parse(address);
        return formatAddressObject(parsed);
      } catch {
        // If parsing fails, return the string as is
        return address;
      }
    }

    // If it's an object, format it
    if (typeof address === 'object') {
      return formatAddressObject(address);
    }

    return 'Invalid address format';
  };

  const formatAddressObject = (addressObj: any): string => {
    const parts = [];

    if (addressObj.address) parts.push(addressObj.address);
    if (addressObj.city) parts.push(addressObj.city);
    if (addressObj.postalCode) parts.push(addressObj.postalCode);

    return parts.filter(Boolean).join(', ') || 'Address not available';
  };

  const orderStatuses: OrderStatus[] = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];
  const paymentStatuses: PaymentStatus[] = ['pending', 'paid', 'failed', 'refunded'];

  const handleStatusUpdate = async (newStatus: OrderStatus) => {
    setIsUpdating(true);
    try {
      const updatedOrder = await updateOrderStatus(selectedOrder.id, newStatus);
      if (updatedOrder) {
        setSelectedOrder(updatedOrder);
        onUpdate();
      }
    } catch (error) {
      console.error('Error updating order status:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handlePaymentUpdate = async (newStatus: PaymentStatus) => {
    setIsUpdating(true);
    try {
      const updatedOrder = await updatePaymentStatus(selectedOrder.id, newStatus);
      if (updatedOrder) {
        setSelectedOrder(updatedOrder);
        onUpdate();
      }
    } catch (error) {
      console.error('Error updating payment status:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Order Details</h2>
              <p className="text-gray-600">{selectedOrder.id}</p>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowInvoice(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <Download className="h-4 w-4" />
                <span>Invoice</span>
              </button>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          <div className="p-6 space-y-8">
            {/* Order Status & Payment */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-3">Order Status</h3>
                <div className="space-y-3">
                  <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getOrderStatusColor(selectedOrder.status)}`}>
                    {selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1)}
                  </span>
                  {isAdmin && (
                    <div className="flex flex-wrap gap-2 mt-3">
                      {orderStatuses.map(status => (
                        <button
                          key={status}
                          onClick={() => handleStatusUpdate(status)}
                          disabled={isUpdating || selectedOrder.status === status}
                          className={`px-3 py-1 rounded-full text-sm transition-colors ${
                            selectedOrder.status === status
                              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                              : 'bg-white border border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          {status.charAt(0).toUpperCase() + status.slice(1)}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-3">Payment Status</h3>
                <div className="space-y-3">
                  <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getPaymentStatusColor(selectedOrder.paymentStatus)}`}>
                    {selectedOrder.paymentStatus.charAt(0).toUpperCase() + selectedOrder.paymentStatus.slice(1)}
                  </span>
                  {isAdmin && (
                    <div className="flex flex-wrap gap-2 mt-3">
                      {paymentStatuses.map(status => (
                        <button
                          key={status}
                          onClick={() => handlePaymentUpdate(status)}
                          disabled={isUpdating || selectedOrder.paymentStatus === status}
                          className={`px-3 py-1 rounded-full text-sm transition-colors ${
                            selectedOrder.paymentStatus === status
                              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                              : 'bg-white border border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          {status.charAt(0).toUpperCase() + status.slice(1)}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Customer & Delivery Info */}
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Customer Information</h3>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Name:</span> {selectedOrder.customerName}</p>
                  <p><span className="font-medium">Email:</span> {selectedOrder.customerEmail}</p>
                  <p><span className="font-medium">Order Date:</span> {formatOrderDate(selectedOrder.createdAt)}</p>
                  {selectedOrder.estimatedDelivery && (
                    <p><span className="font-medium">Est. Delivery:</span> {formatOrderDate(selectedOrder.estimatedDelivery)}</p>
                  )}
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Delivery Information</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex items-start space-x-2">
                    <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                    <div>
                      <p className="font-medium text-gray-900">Delivery Address</p>
                      <p className="text-gray-700">{formatDeliveryAddress(selectedOrder.deliveryAddress)}</p>
                    </div>
                  </div>
                  <p><span className="font-medium">Branch:</span> {selectedOrder.selectedBranch}</p>
                  <div className="flex items-center space-x-2">
                    <CreditCard className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="font-medium text-gray-900">Payment Method</p>
                      <p className="text-gray-700 capitalize">{selectedOrder.paymentMethod?.replace('_', ' ')}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Notes */}
            {selectedOrder.notes && (
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Order Notes</h3>
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                  <div className="flex items-start space-x-2">
                    <FileText className="h-4 w-4 text-amber-600 mt-0.5" />
                    <p className="text-gray-700">{selectedOrder.notes}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Order Items */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Order Items</h3>
              <div className="space-y-3">
                {selectedOrder.items.map(item => (
                  <div key={item.id} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    {item.image && item.image !== '/placeholder.svg' ? (
                      <img
                        src={item.image}
                        alt={item.title}
                        className="w-16 h-16 object-cover rounded-lg"
                        onError={(e) => {
                          // Hide the broken image and show fallback
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const fallback = target.nextElementSibling as HTMLElement;
                          if (fallback) fallback.classList.remove('hidden');
                        }}
                      />
                    ) : null}
                    <div className={`w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center ${item.image && item.image !== '/placeholder.svg' ? 'hidden' : ''}`}>
                      <Package className="h-8 w-8 text-gray-400" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{item.title}</h4>
                      <p className="text-sm text-gray-600">{item.category}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">{item.quantity} x {item.price.toFixed(2)} Dh</p>
                      <p className="text-sm text-gray-600">Total: {(item.quantity * item.price).toFixed(2)} Dh</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Order Summary */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-4">Order Summary</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-gray-600">
                  <span>Subtotal</span>
                  <span>{selectedOrder.subtotal.toFixed(2)} Dh</span>
                </div>
                <div className="flex justify-between text-gray-600">
                  <span>Delivery Fee</span>
                  <span>{selectedOrder.deliveryFee === 0 ? 'Free' : `${selectedOrder.deliveryFee.toFixed(2)} Dh`}</span>
                </div>
                {selectedOrder.promoCode && (
                  <div className="flex justify-between text-green-600">
                    <span>Promo Code ({selectedOrder.promoCode})</span>
                    <span>-10.00 Dh</span>
                  </div>
                )}
                <div className="flex justify-between text-xl font-bold text-gray-900 border-t pt-2">
                  <span>Total</span>
                  <span>{selectedOrder.total.toFixed(2)} Dh</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {showInvoice && (
        <Invoice
          order={selectedOrder}
          onClose={() => setShowInvoice(false)}
          paymentMethod={selectedOrder.paymentMethod as 'bank_transfer' | 'check' | 'cash'}
        />
      )}
    </>
  );
};

export default OrderDetails;
