import { useState, useEffect } from 'react';
import { Plus, Search, Filter, Eye, Edit, Trash2, Package, Clock, CheckCircle, XCircle, AlertTriangle, Download, ChevronUp, ChevronDown, ChevronLeft, ChevronRight, Truck } from 'lucide-react';
import { supabase } from '../../integrations/supabase/client';
import { liveDataService } from '../../services/liveDataService';
import { realTimeService, syncOrderStatusChanged, syncOrderDeleted, syncOrderUpdated } from '../../services/realTimeService';
import { useAuth } from '../../contexts/AuthContext';
import CreateOrderModal from './CreateOrderModal';
import DeliveryAssignmentModal from './DeliveryAssignmentModal';
import OrderViewModal from './OrderViewModal';
import OrderEditModal from './OrderEditModal';
import OrderStatistics from './OrderStatistics';
import Invoice from './Invoice';

interface Order {
  id: string;
  customerName: string;
  customerEmail: string;
  items: number;
  total: number;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'shipped' | 'delivered' | 'cancelled' | 'returned';
  paymentStatus?: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded' | 'cancelled';
  date: string;
  dueDate: string;
  assignedDeliveryPerson?: string;
  assignedDeliveryPersonName?: string;
}

const OrderManagement = () => {
  const { user } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [paymentStatusFilter, setPaymentStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [loading, setLoading] = useState(true);

  // Sorting state
  const [sortBy, setSortBy] = useState<'id' | 'customerName' | 'date' | 'status' | 'total'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [paginatedOrders, setPaginatedOrders] = useState<Order[]>([]);

  // Modal state
  const [showCreateOrderModal, setShowCreateOrderModal] = useState(false);
  const [showDeliveryAssignmentModal, setShowDeliveryAssignmentModal] = useState(false);
  const [selectedOrderForDelivery, setSelectedOrderForDelivery] = useState<Order | null>(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [selectedOrderForInvoice, setSelectedOrderForInvoice] = useState<any>(null);
  const [statisticsRefreshTrigger, setStatisticsRefreshTrigger] = useState(0);
  const [deletingOrderId, setDeletingOrderId] = useState<string | null>(null);

  // Load live orders data
  useEffect(() => {
    const loadOrders = async () => {
      try {
        setLoading(true);
        const liveOrders = await liveDataService.getAllOrders();

        // Transform live orders to match component interface
        const transformedOrders: Order[] = liveOrders.map(order => ({
          id: order.id,
          customerName: order.users?.full_name || 'Unknown Customer',
          customerEmail: order.users?.email || '<EMAIL>',
          items: order.order_items?.length || 0,
          total: order.total,
          status: order.status as Order['status'],
          paymentStatus: (order.payment_status as Order['paymentStatus']) || 'pending',
          date: new Date(order.created_at).toISOString().split('T')[0],
          dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 5 days from creation
          assignedDeliveryPerson: order.assigned_delivery_person,
          assignedDeliveryPersonName: order.delivery_person_name || 'Unassigned'
        }));

        // Apply role-based filtering
        const roleFilteredOrders = getFilteredOrdersByRole(transformedOrders);
        setOrders(roleFilteredOrders);
      } catch (error) {
        console.error('Error loading orders:', error);
        // Fallback to empty array if live data fails
        setOrders([]);
      } finally {
        setLoading(false);
      }
    };

    loadOrders();

    // Set up real-time subscription for orders
    const subscription = liveDataService.subscribeToOrders((payload) => {
      console.log('OrderManagement: Order database change detected:', payload);
      loadOrders(); // Reload orders when changes occur
    });

    // Set up real-time event subscriptions
    const unsubscribeOrderCreated = realTimeService.subscribe('order-created', (event) => {
      console.log('OrderManagement: Order created event received:', event);
      loadOrders();
    });

    const unsubscribeOrderUpdated = realTimeService.subscribe('order-updated', (event) => {
      console.log('OrderManagement: Order updated event received:', event);
      loadOrders();
    });

    const unsubscribeOrderStatusChanged = realTimeService.subscribe('order-status-changed', (event) => {
      console.log('OrderManagement: Order status changed event received:', event);
      loadOrders();
    });

    const unsubscribeDeliveryAssigned = realTimeService.subscribe('delivery-assigned', (event) => {
      console.log('OrderManagement: Delivery assigned event received:', event);
      loadOrders();
    });

    const unsubscribeOrderDeleted = realTimeService.subscribe('order-deleted', (event) => {
      console.log('OrderManagement: Order deleted event received:', event);
      loadOrders();
    });

    const unsubscribeStatisticsUpdated = realTimeService.subscribe('statistics-updated', (event) => {
      console.log('OrderManagement: Statistics updated event received:', event);
      setStatisticsRefreshTrigger(prev => prev + 1);
    });

    // Subscribe to user-related events for customer information updates
    const unsubscribeUserUpdated = realTimeService.subscribe('user-updated', (event) => {
      console.log('OrderManagement: User updated event received:', event);
      loadOrders(); // Reload orders to get updated customer information
    });

    const unsubscribeUsersUpdated = realTimeService.subscribe('users-updated', (event) => {
      console.log('OrderManagement: Users updated event received:', event);
      loadOrders(); // Reload orders to get updated customer information
    });

    return () => {
      subscription.unsubscribe();
      unsubscribeOrderCreated();
      unsubscribeOrderUpdated();
      unsubscribeOrderStatusChanged();
      unsubscribeDeliveryAssigned();
      unsubscribeOrderDeleted();
      unsubscribeStatisticsUpdated();
      unsubscribeUserUpdated();
      unsubscribeUsersUpdated();
    };
  }, []);

  // Filter and sort orders
  useEffect(() => {
    let filtered = orders.filter(order => {
      const matchesSearch = searchTerm === '' ||
        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerEmail.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === 'all' || order.status === statusFilter;

      const matchesPaymentStatus = paymentStatusFilter === 'all' ||
        (order.paymentStatus || 'pending') === paymentStatusFilter;

      const matchesDate = dateFilter === 'all' || (() => {
        const orderDate = new Date(order.date);
        const today = new Date();
        const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const startOfWeek = new Date(startOfToday);
        startOfWeek.setDate(startOfToday.getDate() - startOfToday.getDay());
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

        switch (dateFilter) {
          case 'today':
            return orderDate >= startOfToday;
          case 'week':
            return orderDate >= startOfWeek;
          case 'month':
            return orderDate >= startOfMonth;
          default:
            return true;
        }
      })();

      return matchesSearch && matchesStatus && matchesPaymentStatus && matchesDate;
    });

    // Sort orders
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'id':
          aValue = a.id;
          bValue = b.id;
          break;
        case 'customerName':
          aValue = a.customerName.toLowerCase();
          bValue = b.customerName.toLowerCase();
          break;
        case 'date':
          aValue = new Date(a.date);
          bValue = new Date(b.date);
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'total':
          aValue = a.total;
          bValue = b.total;
          break;
        default:
          aValue = a.date;
          bValue = b.date;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredOrders(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [orders, searchTerm, statusFilter, paymentStatusFilter, dateFilter, sortBy, sortOrder]);

  // Paginate orders
  useEffect(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    setPaginatedOrders(filteredOrders.slice(startIndex, endIndex));
  }, [filteredOrders, currentPage, pageSize]);

  // Sorting functions
  const handleSort = (column: 'id' | 'customerName' | 'date' | 'status' | 'total') => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const getSortIcon = (column: string) => {
    if (sortBy !== column) return null;
    return sortOrder === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />;
  };

  // Pagination functions
  const totalPages = Math.ceil(filteredOrders.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize + 1;
  const endIndex = Math.min(currentPage * pageSize, filteredOrders.length);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  const handleOrderCreated = () => {
    // Reload orders to show the new order
    const loadOrders = async () => {
      try {
        setLoading(true);
        const liveOrders = await liveDataService.getAllOrders();

        // Transform live orders to match component interface (including delivery assignment fields)
        const transformedOrders: Order[] = liveOrders.map(order => ({
          id: order.id,
          customerName: order.users?.full_name || 'Unknown Customer',
          customerEmail: order.users?.email || '<EMAIL>',
          items: order.order_items?.length || 0,
          total: order.total,
          status: order.status as Order['status'],
          paymentStatus: (order.payment_status as Order['paymentStatus']) || 'pending',
          date: new Date(order.created_at).toISOString().split('T')[0],
          dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 5 days from creation
          assignedDeliveryPerson: order.assigned_delivery_person,
          assignedDeliveryPersonName: order.delivery_person_name || 'Unassigned'
        }));

        // Apply role-based filtering
        const roleFilteredOrders = getFilteredOrdersByRole(transformedOrders);
        setOrders(roleFilteredOrders);
      } catch (error) {
        console.error('Error loading orders:', error);
        setOrders([]);
      } finally {
        setLoading(false);
      }
    };

    loadOrders();
  };

  const handleAssignDelivery = (order: Order) => {
    setSelectedOrderForDelivery(order);
    setShowDeliveryAssignmentModal(true);
  };

  const handleDeliveryAssigned = async () => {
    setShowDeliveryAssignmentModal(false);
    setSelectedOrderForDelivery(null);

    // Refresh orders to show updated assignment using the main loadOrders function
    try {
      setLoading(true);
      const liveOrders = await liveDataService.getAllOrders();

      // Transform live orders to match component interface (including delivery assignment fields)
      const transformedOrders: Order[] = liveOrders.map(order => ({
        id: order.id,
        customerName: order.users?.full_name || 'Unknown Customer',
        customerEmail: order.users?.email || '<EMAIL>',
        items: order.order_items?.length || 0,
        total: order.total,
        status: order.status as Order['status'],
        paymentStatus: (order.payment_status as Order['paymentStatus']) || 'pending',
        date: new Date(order.created_at).toISOString().split('T')[0],
        dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 5 days from creation
        assignedDeliveryPerson: order.assigned_delivery_person,
        assignedDeliveryPersonName: order.delivery_person_name || 'Unassigned'
      }));

      // Apply role-based filtering
      const roleFilteredOrders = getFilteredOrdersByRole(transformedOrders);
      setOrders(roleFilteredOrders);

      console.log('Orders refreshed after delivery assignment:', roleFilteredOrders);
    } catch (error) {
      console.error('Error refreshing orders after delivery assignment:', error);
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  // Action handlers for new functionality
  const handleViewOrder = (orderId: string) => {
    setSelectedOrderId(orderId);
    setShowViewModal(true);
  };

  const handleEditOrder = (orderId: string) => {
    setSelectedOrderId(orderId);
    setShowEditModal(true);
  };

  const handleDownloadOrder = async (orderId: string) => {
    try {
      // Get full order details for PDF generation
      const { data: orderData, error } = await supabase
        .from('orders')
        .select(`
          *,
          users!orders_customer_id_fkey (
            id,
            full_name,
            email,
            phone
          ),
          order_items (
            id,
            product_id,
            quantity,
            unit_price,
            total_price,
            products (
              id,
              title,
              sku,
              featured_image,
              category_id,
              categories (
                id,
                name
              )
            )
          )
        `)
        .eq('id', orderId)
        .single();

      if (error) {
        throw error;
      }

      if (!orderData) {
        throw new Error('Order not found');
      }

      // Transform the order data to match the Order interface expected by Invoice component
      const transformedOrder = {
        id: orderData.id,
        customerId: orderData.customer_id || '',
        customerName: orderData.users?.full_name || 'Unknown Customer',
        customerEmail: orderData.users?.email || '<EMAIL>',
        items: orderData.order_items?.map((item: any) => ({
          id: item.id,
          title: item.products?.title || 'Unknown Product',
          category: item.products?.categories?.name || 'Uncategorized',
          price: item.unit_price,
          quantity: item.quantity,
          image: item.products?.featured_image || '',
          // Additional properties for flexibility
          product_name: item.products?.title,
          unit_price: item.unit_price,
          total_price: item.total_price,
          category_name: item.products?.categories?.name // Alternative property name for flexibility
        })) || [],
        subtotal: orderData.subtotal || (orderData.total - (orderData.delivery_fee || 0)),
        deliveryFee: orderData.delivery_fee || 0,
        total: orderData.total,
        status: orderData.status,
        paymentStatus: orderData.payment_status,
        paymentMethod: orderData.payment_method || 'cash',
        deliveryAddress: orderData.delivery_address,
        selectedBranch: orderData.branch || 'Main Branch',
        createdAt: orderData.created_at,
        updatedAt: orderData.updated_at || orderData.created_at,
        estimatedDelivery: orderData.estimated_delivery,
        trackingNumber: orderData.tracking_number,
        notes: orderData.notes
      };

      // Set the order data and show the invoice modal
      setSelectedOrderForInvoice(transformedOrder);
      setShowInvoiceModal(true);
    } catch (error) {
      console.error('Error loading order for invoice:', error);
      alert('Failed to load order details. Please try again.');
    }
  };

  const handleOrderUpdated = () => {
    // Reload orders after update
    handleOrderCreated();
    // Trigger statistics refresh
    setStatisticsRefreshTrigger(prev => prev + 1);
  };

  const handleDeleteOrder = async (orderId: string) => {
    try {
      setDeletingOrderId(orderId);

      // Get order data before deletion for real-time sync
      const orderToDelete = orders.find(order => order.id === orderId);

      const success = await liveDataService.deleteOrder(orderId);

      if (success) {
        // Emit real-time events
        if (orderToDelete) {
          syncOrderDeleted(orderId, orderToDelete);
        }

        // Remove order from local state immediately for better UX
        setOrders(prevOrders => prevOrders.filter(order => order.id !== orderId));

        // Trigger statistics refresh
        setStatisticsRefreshTrigger(prev => prev + 1);

        // Show success message
        alert('Order deleted successfully!');

        // Refresh orders from database to ensure consistency
        handleOrderCreated();
      } else {
        throw new Error('Failed to delete order');
      }
    } catch (error) {
      console.error('Error deleting order:', error);
      alert('Error deleting order: ' + (error as Error).message);
    } finally {
      setDeletingOrderId(null);
    }
  };

  const handleStatusChange = async (orderId: string, newStatus: string) => {
    try {
      const success = await liveDataService.updateOrderStatus(orderId, newStatus);

      if (success) {
        // Emit real-time events
        const oldOrder = orders.find(o => o.id === orderId);
        if (oldOrder) {
          syncOrderStatusChanged(orderId, oldOrder.status, newStatus);
        }

        console.log('Order status updated successfully');
        handleOrderCreated(); // Refresh orders
      } else {
        throw new Error('Failed to update order status');
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      alert('Error updating order status: ' + (error as Error).message);
    }
  };

  // Role-based permissions
  console.log('OrderManagement: Current user role:', user?.role, 'User type:', user?.userType, 'User:', user);

  // Check both role and userType fields for compatibility
  const userRole = user?.role || user?.userType;
  const isAdmin = userRole === 'admin';
  const isManager = userRole === 'store_manager' || userRole === 'manager';
  const isDelivery = userRole === 'delivery';

  const canCreateOrders = isAdmin || isManager;
  const canAssignDelivery = isAdmin || isManager;
  const canChangeStatus = isAdmin || isManager || isDelivery;
  const canDeleteOrders = isAdmin;
  const canViewAllOrders = isAdmin || isManager;

  console.log('OrderManagement: Permissions:', {
    userRole,
    isAdmin,
    isManager,
    canCreateOrders,
    canAssignDelivery,
    canChangeStatus,
    canDeleteOrders,
    canViewAllOrders
  });

  // Filter orders based on user role
  const getFilteredOrdersByRole = (allOrders: Order[]) => {
    if (canViewAllOrders) {
      return allOrders; // Admin and Store Manager can see all orders
    }

    if (user?.role === 'client' || user?.role === 'reseller') {
      // Clients and Resellers can only see their own orders
      return allOrders.filter(order => order.customerEmail === user.email);
    }

    if (user?.role === 'delivery') {
      // Delivery personnel can see orders assigned to them
      return allOrders.filter(order => order.assignedDeliveryPerson === user.id);
    }

    return allOrders;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'confirmed':
        return <CheckCircle className="h-4 w-4" />;
      case 'preparing':
        return <Package className="h-4 w-4" />;
      case 'ready':
        return <Package className="h-4 w-4" />;
      case 'shipped':
        return <AlertTriangle className="h-4 w-4" />;
      case 'delivered':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      case 'returned':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'preparing':
        return 'bg-orange-100 text-orange-800';
      case 'ready':
        return 'bg-teal-100 text-teal-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'returned':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (paymentStatus: string) => {
    switch (paymentStatus) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'refunded':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };



  const orderStats = {
    total: orders.length,
    pending: orders.filter(o => o.status === 'pending').length,
    processing: orders.filter(o => o.status === 'processing').length,
    shipped: orders.filter(o => o.status === 'shipped').length,
    delivered: orders.filter(o => o.status === 'delivered').length,
    cancelled: orders.filter(o => o.status === 'cancelled').length,
    totalRevenue: orders.reduce((sum, order) => sum + order.total, 0)
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Order Statistics */}
      <OrderStatistics refreshTrigger={statisticsRefreshTrigger} />

      {/* Order Management */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              {canViewAllOrders ? 'Order Management' : 'My Orders'}
            </h3>
            <button
              onClick={() => setShowCreateOrderModal(true)}
              className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors flex items-center space-x-2"
              title={`User role: ${user?.role || 'unknown'} - Can create: ${canCreateOrders}`}
            >
              <Plus className="h-5 w-5" />
              <span>Create Order</span>
            </button>
          </div>

          {/* Search and Filters - Reorganized in single responsive row */}
          <div className="mt-4">
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center">
              {/* Search Bar */}
              <div className="flex-1 min-w-0">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search orders by ID, customer name, or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Filter Controls - All in one row */}
              <div className="flex flex-wrap gap-2 items-center">
                {/* Order Status Filter */}
                <div className="flex items-center space-x-1">
                  <Filter className="h-4 w-4 text-gray-400" />
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent text-sm min-w-[120px]"
                  >
                    <option value="all">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="preparing">Preparing</option>
                    <option value="ready">Ready</option>
                    <option value="shipped">Shipped</option>
                    <option value="delivered">Delivered</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="returned">Returned</option>
                  </select>
                </div>

                {/* Payment Status Filter */}
                <select
                  value={paymentStatusFilter}
                  onChange={(e) => setPaymentStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent text-sm min-w-[140px]"
                >
                  <option value="all">All Payment</option>
                  <option value="pending">Pending</option>
                  <option value="processing">Processing</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                  <option value="refunded">Refunded</option>
                  <option value="cancelled">Cancelled</option>
                </select>

                {/* Date Filter */}
                <select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent text-sm min-w-[100px]"
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                </select>

                {/* Clear Filters Button */}
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('all');
                    setPaymentStatusFilter('all');
                    setDateFilter('all');
                  }}
                  className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors whitespace-nowrap"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Orders Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('id')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Order ID</span>
                    {getSortIcon('id')}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('customerName')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Customer</span>
                    {getSortIcon('customerName')}
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Items
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('total')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Total</span>
                    {getSortIcon('total')}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('status')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Status</span>
                    {getSortIcon('status')}
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Status
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('date')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Date</span>
                    {getSortIcon('date')}
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Delivery Person
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedOrders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{order.id}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{order.customerName}</div>
                      <div className="text-sm text-gray-500">{order.customerEmail}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{order.items} items</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{order.total.toFixed(2)} Dh</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {getStatusIcon(order.status)}
                      <span className="ml-1">{order.status}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(order.paymentStatus || 'pending')}`}>
                      {order.paymentStatus || 'pending'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {order.date}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      {order.assignedDeliveryPerson ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <Truck className="h-3 w-3 mr-1" />
                          {order.assignedDeliveryPersonName}
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          Unassigned
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      {/* View Order Button */}
                      <button
                        onClick={() => handleViewOrder(order.id)}
                        className="text-teal-600 hover:text-teal-900 transition-colors"
                        title="View Order Details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>

                      {/* Edit Order Button */}
                      {(canViewAllOrders || canCreateOrders) && (
                        <button
                          onClick={() => handleEditOrder(order.id)}
                          className={`text-amber-600 hover:text-amber-900 transition-colors ${
                            order.status === 'delivered' || order.status === 'cancelled'
                              ? 'opacity-50 cursor-not-allowed'
                              : ''
                          }`}
                          title={
                            order.status === 'delivered' || order.status === 'cancelled'
                              ? `Cannot edit ${order.status} order`
                              : "Edit Order"
                          }
                          disabled={order.status === 'delivered' || order.status === 'cancelled'}
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                      )}

                      {/* Download PDF Button */}
                      <button
                        onClick={() => handleDownloadOrder(order.id)}
                        className="text-green-600 hover:text-green-900 transition-colors"
                        title="Download Invoice PDF"
                      >
                        <Download className="h-4 w-4" />
                      </button>



                      {/* Assign Delivery Button */}
                      {canAssignDelivery && (
                        <button
                          onClick={() => handleAssignDelivery(order)}
                          className="text-purple-600 hover:text-purple-900 transition-colors"
                          title="Assign Delivery Person"
                        >
                          <Truck className="h-4 w-4" />
                        </button>
                      )}

                      {/* Delete Order Button */}
                      {canDeleteOrders && (
                        <button
                          onClick={() => {
                            if (window.confirm('Are you sure you want to delete this order? This action cannot be undone.')) {
                              handleDeleteOrder(order.id);
                            }
                          }}
                          disabled={deletingOrderId === order.id}
                          className={`transition-colors ${
                            deletingOrderId === order.id
                              ? 'text-gray-400 cursor-not-allowed'
                              : 'text-red-600 hover:text-red-900'
                          }`}
                          title={deletingOrderId === order.id ? "Deleting..." : "Delete Order"}
                        >
                          {deletingOrderId === order.id ? (
                            <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-red-600 rounded-full"></div>
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination Controls */}
        {filteredOrders.length > 0 && (
          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-700">
                  Showing {startIndex} to {endIndex} of {filteredOrders.length} results
                </span>
                <select
                  value={pageSize}
                  onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                  className="px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  <option value={10}>10 per page</option>
                  <option value={20}>20 per page</option>
                  <option value={50}>50 per page</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                >
                  <ChevronLeft className="h-4 w-4" />
                  <span>Previous</span>
                </button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => handlePageChange(pageNum)}
                        className={`px-3 py-1 text-sm rounded ${
                          currentPage === pageNum
                            ? 'bg-orange-500 text-white'
                            : 'border border-gray-300 hover:bg-gray-100'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                >
                  <span>Next</span>
                  <ChevronRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        )}

        {filteredOrders.length === 0 && (
          <div className="text-center py-8">
            <Package className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No orders found</p>
          </div>
        )}
      </div>

      {/* Create Order Modal */}
      {showCreateOrderModal && (
        <CreateOrderModal
          onClose={() => setShowCreateOrderModal(false)}
          onOrderCreated={handleOrderCreated}
        />
      )}

      {/* Delivery Assignment Modal */}
      {showDeliveryAssignmentModal && selectedOrderForDelivery && (
        <DeliveryAssignmentModal
          order={selectedOrderForDelivery}
          onClose={() => {
            setShowDeliveryAssignmentModal(false);
            setSelectedOrderForDelivery(null);
          }}
          onAssigned={handleDeliveryAssigned}
        />
      )}

      {/* Order View Modal */}
      {showViewModal && selectedOrderId && (
        <OrderViewModal
          orderId={selectedOrderId}
          onClose={() => {
            setShowViewModal(false);
            setSelectedOrderId(null);
          }}
        />
      )}

      {/* Order Edit Modal */}
      {showEditModal && selectedOrderId && (
        <OrderEditModal
          orderId={selectedOrderId}
          onClose={() => {
            setShowEditModal(false);
            setSelectedOrderId(null);
          }}
          onOrderUpdated={handleOrderUpdated}
        />
      )}

      {/* Invoice Modal */}
      {showInvoiceModal && selectedOrderForInvoice && (
        <Invoice
          order={selectedOrderForInvoice}
          onClose={() => {
            setShowInvoiceModal(false);
            setSelectedOrderForInvoice(null);
          }}
          paymentMethod={selectedOrderForInvoice.paymentMethod as 'bank_transfer' | 'check' | 'cash'}
        />
      )}
    </div>
  );
};

export default OrderManagement;
