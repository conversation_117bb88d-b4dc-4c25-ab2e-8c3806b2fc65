import React, { useState, useEffect } from 'react';
import { X, Package, User, MapPin, CreditCard, Clock, CheckCircle, AlertCircle, Building2, Image } from 'lucide-react';
import { supabase } from '../../integrations/supabase/client';
import { formatCurrency } from '../../utils/currency';
import { getBranches } from '../../services/branchService';
import { Branch } from '../../types/branch';

interface OrderItem {
  id: string;
  product_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  products?: {
    id: string;
    title: string;
    sku?: string;
    featured_image?: string;
    category_id?: string;
    categories?: {
      id: string;
      name: string;
    };
  };
}

interface OrderDetails {
  id: string;
  order_number: string;
  customer_id: string;
  branch_id?: string;
  status: string;
  payment_status?: string;
  payment_method?: string;
  subtotal: number;
  delivery_fee?: number;
  discount_amount?: number;
  tax_amount?: number;
  total: number;
  created_at: string;
  updated_at: string;
  delivery_address?: any;
  billing_address?: any;
  notes?: string;
  branches?: {
    id: string;
    name: string;
    address: any;
    coordinates?: any;
  };
  users?: {
    id: string;
    full_name: string;
    email: string;
    phone?: string;
  };
  order_items?: OrderItem[];
}

interface OrderViewModalProps {
  orderId: string;
  onClose: () => void;
}

const OrderViewModal: React.FC<OrderViewModalProps> = ({ orderId, onClose }) => {
  const [order, setOrder] = useState<OrderDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [branches, setBranches] = useState<Branch[]>([]);

  // Calculate distance between two coordinates using Haversine formula
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c; // Distance in kilometers
    return Math.round(distance * 10) / 10; // Round to 1 decimal place
  };

  useEffect(() => {
    loadOrderDetails();
    loadBranches();
  }, [orderId]);

  const loadBranches = async () => {
    try {
      const activeBranches = await getBranches(true);
      setBranches(activeBranches);
    } catch (error) {
      console.error('Error loading branches:', error);
    }
  };

  // Get branch information for display
  const getBranchInfo = () => {
    if (!order) return null;

    // Access branch information from the joined branch data
    const orderBranch = order.branches;
    const branchId = order.branch_id;

    console.log('Branch info from order:', { branchId, orderBranch }); // Debug log

    if (!branchId && !orderBranch) {
      return null; // No branch information available
    }

    let displayText = 'Unknown Branch';
    let fullAddress = '';
    let branch = orderBranch;

    if (orderBranch) {
      displayText = `${orderBranch.name} - ${orderBranch.address?.city || 'Unknown City'}`;
      fullAddress = orderBranch.address ?
        `${orderBranch.address.street || ''}, ${orderBranch.address.city || ''}`.trim().replace(/^,\s*/, '') :
        '';
    } else if (branchId) {
      // Try to find the branch in our loaded branches as fallback
      const fallbackBranch = branches.find(b => b.id === branchId);
      if (fallbackBranch) {
        branch = fallbackBranch;
        displayText = `${fallbackBranch.name} - ${fallbackBranch.address.city}`;
        fullAddress = `${fallbackBranch.address.street}, ${fallbackBranch.address.city}`;
      }
    }

    // Calculate distance if coordinates are available
    let distance = null;
    if (order.delivery_address?.latitude && order.delivery_address?.longitude && branch?.coordinates) {
      distance = calculateDistance(
        order.delivery_address.latitude,
        order.delivery_address.longitude,
        branch.coordinates.latitude,
        branch.coordinates.longitude
      );
    }

    return {
      displayText,
      fullAddress,
      distance,
      branch
    };
  };

  const loadOrderDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get order details with items and branch information
      const { data, error: fetchError } = await supabase
        .from('orders')
        .select(`
          *,
          branches!orders_branch_id_fkey (
            id,
            name,
            address,
            coordinates
          ),
          users!orders_customer_id_fkey (
            id,
            full_name,
            email,
            phone,
            user_type
          ),
          order_items (
            id,
            product_id,
            quantity,
            unit_price,
            total_price,
            products (
              id,
              title,
              sku,
              featured_image,
              category_id,
              categories (
                id,
                name
              )
            )
          )
        `)
        .eq('id', orderId)
        .single();

      if (fetchError) {
        throw fetchError;
      }

      setOrder(data);
    } catch (err) {
      console.error('Error loading order details:', err);
      setError(err instanceof Error ? err.message : 'Failed to load order details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'processing': return 'bg-orange-100 text-orange-800';
      case 'shipped': return 'bg-purple-100 text-purple-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status?: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatAddress = (address: any) => {
    if (!address) return 'Not provided';
    if (typeof address === 'string') return address;
    if (typeof address === 'object') {
      return address.address || address.street || 'Address not formatted';
    }
    return 'Not provided';
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-8 max-w-md w-full mx-4">
          <div className="flex items-center justify-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-teal-600"></div>
            <span className="text-gray-600">Loading order details...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Order</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={onClose}
              className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!order) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-teal-600 to-amber-500 text-white rounded-t-xl">
          <div>
            <h2 className="text-2xl font-bold">Order Details</h2>
            <p className="text-teal-100">Order #{order.order_number}</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Order Status and Info */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Package className="h-5 w-5 text-teal-600" />
                <h3 className="font-semibold text-gray-900">Order Status</h3>
              </div>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
              </span>
              <p className="text-sm text-gray-600 mt-2">
                Created: {new Date(order.created_at).toLocaleDateString()}
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <CreditCard className="h-5 w-5 text-teal-600" />
                <h3 className="font-semibold text-gray-900">Payment</h3>
              </div>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPaymentStatusColor(order.payment_status)}`}>
                {order.payment_status?.charAt(0).toUpperCase() + order.payment_status?.slice(1) || 'Pending'}
              </span>
              <p className="text-sm text-gray-600 mt-2">
                Method: {order.payment_method || 'Not specified'}
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Clock className="h-5 w-5 text-teal-600" />
                <h3 className="font-semibold text-gray-900">Total Amount</h3>
              </div>
              <p className="text-2xl font-bold text-teal-600">
                {formatCurrency(order.total)}
              </p>
            </div>
          </div>

          {/* Customer Information */}
          <div className="bg-gray-50 rounded-lg p-6">
            <div className="flex items-center space-x-2 mb-4">
              <User className="h-5 w-5 text-teal-600" />
              <h3 className="text-lg font-semibold text-gray-900">Customer Information</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Name</p>
                <p className="font-medium text-gray-900">{order.users?.full_name || 'Unknown Customer'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Email</p>
                <p className="font-medium text-gray-900">{order.users?.email || 'Not provided'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Type</p>
                <p className="font-medium text-gray-900">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    order.users?.user_type === 'client'
                      ? 'bg-teal-100 text-teal-800'
                      : order.users?.user_type === 'reseller'
                      ? 'bg-amber-100 text-amber-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {order.users?.user_type === 'client' ? 'Client' :
                     order.users?.user_type === 'reseller' ? 'Reseller' :
                     'Unknown'}
                  </span>
                </p>
              </div>
              {order.users?.phone && (
                <div>
                  <p className="text-sm text-gray-600">Phone</p>
                  <p className="font-medium text-gray-900">{order.users.phone}</p>
                </div>
              )}
            </div>
          </div>

          {/* Delivery Information */}
          {(order.delivery_address || order.billing_address) && (
            <div className="bg-gray-50 rounded-lg p-6">
              <div className="flex items-center space-x-2 mb-4">
                <MapPin className="h-5 w-5 text-teal-600" />
                <h3 className="text-lg font-semibold text-gray-900">Delivery Information</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {order.delivery_address && (
                  <div>
                    <p className="text-sm text-gray-600">Delivery Address</p>
                    <p className="font-medium text-gray-900">{formatAddress(order.delivery_address)}</p>
                  </div>
                )}
                {order.billing_address && (
                  <div>
                    <p className="text-sm text-gray-600">Billing Address</p>
                    <p className="font-medium text-gray-900">{formatAddress(order.billing_address)}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Delivery Branch Information */}
          <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
            <div className="flex items-center space-x-2 mb-4">
              <Building2 className="h-5 w-5 text-teal-600" />
              <h3 className="text-lg font-semibold text-gray-900">Delivery Branch Information</h3>
            </div>
            {(() => {
              const branchInfo = getBranchInfo();
              if (!branchInfo) {
                return (
                  <p className="text-gray-500 italic">No branch selected</p>
                );
              }

              return (
                <div className="space-y-2">
                  <div>
                    <p className="font-medium text-gray-900">
                      {branchInfo.displayText}
                      {branchInfo.distance && (
                        <span className="text-teal-600 ml-2">(Distance: {branchInfo.distance} km)</span>
                      )}
                    </p>
                    {branchInfo.fullAddress && (
                      <p className="text-sm text-gray-600 mt-1">{branchInfo.fullAddress}</p>
                    )}
                  </div>
                  {branchInfo.branch && (
                    <div className="text-xs text-gray-500">
                      <p>Branch ID: {branchInfo.branch.id}</p>
                      {branchInfo.branch.contact?.phone && (
                        <p>Contact: {branchInfo.branch.contact.phone}</p>
                      )}
                    </div>
                  )}
                </div>
              );
            })()}
          </div>

          {/* Order Items */}
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Order Items</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      SKU
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Unit Price
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {order.order_items?.map((item) => (
                    <tr key={item.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {item.products?.featured_image ? (
                            <img
                              className="h-12 w-12 rounded-lg object-cover mr-3"
                              src={item.products.featured_image}
                              alt={item.products.title || 'Product'}
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                const fallback = target.nextElementSibling as HTMLElement;
                                if (fallback) fallback.classList.remove('hidden');
                              }}
                            />
                          ) : null}
                          <div className={`h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center mr-3 ${item.products?.featured_image ? 'hidden' : ''}`}>
                            <Package className="h-6 w-6 text-gray-400" />
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {item.products?.title || 'Unknown Product'}
                            </div>
                            <div className="text-xs text-gray-500">
                              SKU: {item.products?.sku || 'N/A'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.products?.categories?.name || 'Uncategorized'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.products?.sku || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.quantity}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(item.unit_price)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {formatCurrency(item.total_price)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Order Summary */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal:</span>
                <span className="font-medium">{formatCurrency(order.subtotal)}</span>
              </div>
              {order.delivery_fee && order.delivery_fee > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Delivery Fee:</span>
                  <span className="font-medium">{formatCurrency(order.delivery_fee)}</span>
                </div>
              )}
              {order.discount_amount && order.discount_amount > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Discount:</span>
                  <span className="font-medium text-green-600">-{formatCurrency(order.discount_amount)}</span>
                </div>
              )}
              {order.tax_amount && order.tax_amount > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax:</span>
                  <span className="font-medium">{formatCurrency(order.tax_amount)}</span>
                </div>
              )}
              <div className="border-t border-gray-200 pt-2">
                <div className="flex justify-between">
                  <span className="text-lg font-semibold text-gray-900">Total:</span>
                  <span className="text-lg font-bold text-teal-600">{formatCurrency(order.total)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          {order.notes && (
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Notes</h3>
              <p className="text-gray-700">{order.notes}</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl">
          <button
            onClick={onClose}
            className="bg-teal-600 text-white px-6 py-2 rounded-lg hover:bg-teal-700 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default OrderViewModal;
