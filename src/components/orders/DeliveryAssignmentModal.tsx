import { useState, useEffect } from 'react';
import { X, User, Truck, Package, Clock, MapPin, Phone, Mail, Building2 } from 'lucide-react';
import { liveDataService } from '../../services/liveDataService';
import { syncDeliveryAssigned, realTimeService } from '../../services/realTimeService';
import { formatCurrency } from '../../utils/currency';
import { supabase } from '../../integrations/supabase/client';
import { getBranches } from '../../services/branchService';
import { Branch } from '../../types/branch';

interface DeliveryPerson {
  id: string;
  full_name: string;
  email: string;
  phone?: string;
  address?: any;
  currentOrders?: number;
  isAvailable?: boolean;
}

interface Order {
  id: string;
  customerName: string;
  customerEmail: string;
  items: number;
  total: number;
  status: string;
  date: string;
  assignedDeliveryPerson?: string;
  assignedDeliveryPersonName?: string;
}

interface DeliveryAssignmentModalProps {
  order: Order;
  onClose: () => void;
  onAssigned: () => void;
}

const DeliveryAssignmentModal = ({ order, onClose, onAssigned }: DeliveryAssignmentModalProps) => {
  const [deliveryPersonnel, setDeliveryPersonnel] = useState<DeliveryPerson[]>([]);
  const [selectedDeliveryPerson, setSelectedDeliveryPerson] = useState<DeliveryPerson | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingPersonnel, setLoadingPersonnel] = useState(true);
  const [branches, setBranches] = useState<Branch[]>([]);

  // Calculate distance between two coordinates using Haversine formula
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c; // Distance in kilometers
    return Math.round(distance * 10) / 10; // Round to 1 decimal place
  };

  // Get branch information for display
  const getBranchInfo = () => {
    // Access branch information from the order object
    // The order object might have branch_id or branches relationship
    const branchId = (order as any).branch_id;
    const orderBranch = (order as any).branches;

    console.log('DeliveryAssignment - Branch info from order:', { branchId, orderBranch }); // Debug log

    if (!branchId && !orderBranch) {
      return null; // No branch information available
    }

    let displayText = 'Unknown Branch';
    let branch = orderBranch;

    if (orderBranch) {
      displayText = `${orderBranch.name} - ${orderBranch.address?.city || 'Unknown City'}`;
    } else if (branchId) {
      // Try to find the branch in our loaded branches as fallback
      const fallbackBranch = branches.find(b => b.id === branchId);
      if (fallbackBranch) {
        branch = fallbackBranch;
        displayText = `${fallbackBranch.name} - ${fallbackBranch.address.city}`;
      }
    }

    // Calculate distance if coordinates are available
    let distance = null;
    if ((order as any).delivery_coordinates?.latitude && (order as any).delivery_coordinates?.longitude && branch?.coordinates) {
      distance = calculateDistance(
        (order as any).delivery_coordinates.latitude,
        (order as any).delivery_coordinates.longitude,
        branch.coordinates.latitude,
        branch.coordinates.longitude
      );
    }

    return {
      displayText,
      distance,
      branch
    };
  };

  const loadBranches = async () => {
    try {
      const activeBranches = await getBranches(true);
      setBranches(activeBranches);
    } catch (error) {
      console.error('Error loading branches:', error);
    }
  };

  useEffect(() => {
    loadDeliveryPersonnel();
    loadBranches();

    // Subscribe to user-related real-time events
    const unsubscribeUserUpdated = realTimeService.subscribe('user-updated', (event) => {
      console.log('DeliveryAssignmentModal: User updated event received:', event);
      loadDeliveryPersonnel();
    });

    const unsubscribeUserRoleChanged = realTimeService.subscribe('user-role-changed', (event) => {
      console.log('DeliveryAssignmentModal: User role changed event received:', event);
      loadDeliveryPersonnel();
    });

    const unsubscribeDeliveryPersonnelUpdated = realTimeService.subscribe('delivery-personnel-updated', (event) => {
      console.log('DeliveryAssignmentModal: Delivery personnel updated event received:', event);
      loadDeliveryPersonnel();
    });

    const unsubscribeUsersUpdated = realTimeService.subscribe('users-updated', (event) => {
      console.log('DeliveryAssignmentModal: Users updated event received:', event);
      loadDeliveryPersonnel();
    });

    return () => {
      unsubscribeUserUpdated();
      unsubscribeUserRoleChanged();
      unsubscribeDeliveryPersonnelUpdated();
      unsubscribeUsersUpdated();
    };
  }, []);

  const loadDeliveryPersonnel = async () => {
    try {
      setLoadingPersonnel(true);
      console.log('DeliveryAssignmentModal: Loading delivery personnel...');

      const users = await liveDataService.getAllUsers();
      console.log('DeliveryAssignmentModal: All users loaded:', users?.length || 0);

      if (!Array.isArray(users)) {
        throw new Error('getAllUsers did not return an array');
      }

      // Filter for delivery personnel - check multiple possible field names
      const deliveryUsers = users.filter(user => {
        // Check all possible field variations - database uses 'delivery_person', transformed to 'userType'
        const userRole = user.role || user.user_type || user.userType;
        const isDelivery = userRole === 'delivery_person' || userRole === 'delivery';
        const isActive = user.is_active !== false && user.isActive !== false;

        console.log(`User ${user.full_name || user.fullName}: role=${user.role}, user_type=${user.user_type}, userType=${user.userType}, isDelivery=${isDelivery}, isActive=${isActive}, is_active=${user.is_active}`);

        return isDelivery && isActive;
      });

      console.log('DeliveryAssignmentModal: Found delivery users:', deliveryUsers.length, deliveryUsers);

      // If no delivery users found, let's try multiple fallback queries
      if (deliveryUsers.length === 0) {
        console.log('DeliveryAssignmentModal: No delivery users found, trying fallback queries...');

        // Try fallback 1: user_type = 'delivery_person' (correct database value)
        try {
          const { data: directUsers1, error: directError1 } = await supabase
            .from('users')
            .select('*')
            .eq('user_type', 'delivery_person')
            .eq('is_active', true);

          console.log('DeliveryAssignmentModal: Fallback 1 (user_type=delivery_person) result:', { directUsers1, directError1 });

          if (directUsers1 && directUsers1.length > 0) {
            const transformedDirectUsers = directUsers1.map(user => ({
              id: user.id,
              full_name: user.full_name,
              email: user.email,
              phone: user.phone,
              address: user.address,
              currentOrders: 0,
              isAvailable: true
            }));

            setDeliveryPersonnel(transformedDirectUsers);
            setLoadingPersonnel(false);
            return;
          }
        } catch (directQueryError1) {
          console.error('DeliveryAssignmentModal: Fallback 1 failed:', directQueryError1);
        }

        // Try fallback 2: role = 'delivery_person' (in case role field exists)
        try {
          const { data: directUsers2, error: directError2 } = await supabase
            .from('users')
            .select('*')
            .eq('role', 'delivery_person')
            .eq('is_active', true);

          console.log('DeliveryAssignmentModal: Fallback 2 (role=delivery_person) result:', { directUsers2, directError2 });

          if (directUsers2 && directUsers2.length > 0) {
            const transformedDirectUsers = directUsers2.map(user => ({
              id: user.id,
              full_name: user.full_name,
              email: user.email,
              phone: user.phone,
              address: user.address,
              currentOrders: 0,
              isAvailable: true
            }));

            setDeliveryPersonnel(transformedDirectUsers);
            setLoadingPersonnel(false);
            return;
          }
        } catch (directQueryError2) {
          console.error('DeliveryAssignmentModal: Fallback 2 failed:', directQueryError2);
        }

        // Try fallback 3: Get all users and check what fields exist
        try {
          const { data: allUsers, error: allUsersError } = await supabase
            .from('users')
            .select('*')
            .limit(10);

          console.log('DeliveryAssignmentModal: Sample users for debugging:', { allUsers, allUsersError });

          if (allUsers && allUsers.length > 0) {
            console.log('DeliveryAssignmentModal: Sample user fields:', Object.keys(allUsers[0]));
            console.log('DeliveryAssignmentModal: Sample user data:', allUsers[0]);

            // Check specifically for delivery personnel
            const deliveryPersonnelInDb = allUsers.filter(user =>
              user.user_type === 'delivery_person' || user.user_type === 'delivery'
            );
            console.log('DeliveryAssignmentModal: Delivery personnel found in database:', deliveryPersonnelInDb);
          }

          // Try a specific query for delivery personnel with different approaches
          const queries = [
            { field: 'user_type', value: 'delivery_person' },
            { field: 'user_type', value: 'delivery' },
            { field: 'role', value: 'delivery_person' },
            { field: 'role', value: 'delivery' }
          ];

          for (const query of queries) {
            try {
              const { data: testUsers, error: testError } = await supabase
                .from('users')
                .select('*')
                .eq(query.field, query.value);

              console.log(`DeliveryAssignmentModal: Query ${query.field}=${query.value} result:`, {
                count: testUsers?.length || 0,
                users: testUsers,
                error: testError
              });

              if (testUsers && testUsers.length > 0) {
                // Found delivery personnel! Transform and use them
                const transformedUsers = testUsers.map(user => ({
                  id: user.id,
                  full_name: user.full_name,
                  email: user.email,
                  phone: user.phone,
                  address: user.address,
                  currentOrders: 0,
                  isAvailable: true
                }));

                setDeliveryPersonnel(transformedUsers);
                setLoadingPersonnel(false);
                return;
              }
            } catch (queryError) {
              console.error(`DeliveryAssignmentModal: Query ${query.field}=${query.value} failed:`, queryError);
            }
          }
        } catch (debugError) {
          console.error('DeliveryAssignmentModal: Debug query failed:', debugError);
        }
      }

      // Get current workload for each delivery person
      const deliveryPersonnelWithWorkload = await Promise.all(
        deliveryUsers.map(async (person) => {
          try {
            // Get current orders assigned to this delivery person
            const orders = await liveDataService.getAllOrders();
            const currentOrders = orders.filter(order => 
              order.assigned_delivery_person === person.id && 
              ['processing', 'shipped'].includes(order.status)
            ).length;

            return {
              id: person.id,
              full_name: person.full_name,
              email: person.email,
              phone: person.phone,
              address: person.address,
              currentOrders,
              isAvailable: currentOrders < 10 // Assume max 10 orders per delivery person
            };
          } catch (error) {
            console.error('Error getting workload for delivery person:', person.id, error);
            return {
              id: person.id,
              full_name: person.full_name,
              email: person.email,
              phone: person.phone,
              address: person.address,
              currentOrders: 0,
              isAvailable: true
            };
          }
        })
      );

      setDeliveryPersonnel(deliveryPersonnelWithWorkload);
    } catch (error) {
      console.error('Error loading delivery personnel:', error);
      setDeliveryPersonnel([]);
    } finally {
      setLoadingPersonnel(false);
    }
  };

  const handleAssignDelivery = async () => {
    if (!selectedDeliveryPerson) {
      alert('Please select a delivery person');
      return;
    }

    setLoading(true);
    try {
      // Update the order with assigned delivery person
      const success = await liveDataService.updateOrderDeliveryAssignment(
        order.id, 
        selectedDeliveryPerson.id
      );

      if (success) {
        // Emit real-time events with delivery person information
        syncDeliveryAssigned(order.id, selectedDeliveryPerson.id, selectedDeliveryPerson.full_name);

        alert(`Order assigned to ${selectedDeliveryPerson.full_name} successfully!`);
        onAssigned();
      } else {
        throw new Error('Failed to assign delivery person');
      }
    } catch (error) {
      console.error('Error assigning delivery person:', error);
      alert('Error assigning delivery person: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const getWorkloadColor = (currentOrders: number) => {
    if (currentOrders === 0) return 'bg-green-100 text-green-800';
    if (currentOrders <= 3) return 'bg-blue-100 text-blue-800';
    if (currentOrders <= 6) return 'bg-yellow-100 text-yellow-800';
    if (currentOrders <= 9) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  const getWorkloadText = (currentOrders: number) => {
    if (currentOrders === 0) return 'Available';
    if (currentOrders <= 3) return 'Light Load';
    if (currentOrders <= 6) return 'Moderate Load';
    if (currentOrders <= 9) return 'Heavy Load';
    return 'At Capacity';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="bg-purple-100 p-3 rounded-lg">
              <Truck className="h-8 w-8 text-purple-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Assign Delivery Person</h2>
              <p className="text-gray-600">Order #{order.id}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Order Information */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <User className="h-5 w-5 text-gray-400" />
                <span className="font-medium text-gray-900">Customer</span>
              </div>
              <p className="text-gray-700">{order.customerName}</p>
              <p className="text-sm text-gray-500">{order.customerEmail}</p>
            </div>
            <div className="bg-white rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Package className="h-5 w-5 text-gray-400" />
                <span className="font-medium text-gray-900">Order Info</span>
              </div>
              <p className="text-gray-700">{order.items} items</p>
              <p className="text-sm text-gray-500">Total: {formatCurrency(order.total)}</p>
            </div>
            <div className="bg-white rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Clock className="h-5 w-5 text-gray-400" />
                <span className="font-medium text-gray-900">Status</span>
              </div>
              <p className="text-gray-700 capitalize">{order.status}</p>
              <p className="text-sm text-gray-500">Date: {order.date}</p>
            </div>
          </div>

          {/* Preferred Branch Information */}
          {(() => {
            const branchInfo = getBranchInfo();
            if (branchInfo) {
              return (
                <div className="mt-4 bg-white rounded-lg p-4 border border-teal-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <Building2 className="h-5 w-5 text-teal-600" />
                    <span className="font-medium text-gray-900">Preferred Branch</span>
                  </div>
                  <p className="text-gray-700">
                    {branchInfo.displayText}
                    {branchInfo.distance && (
                      <span className="text-teal-600 ml-2">(Distance: {branchInfo.distance} km)</span>
                    )}
                  </p>
                  <p className="text-sm text-gray-500">This helps optimize delivery routing</p>
                </div>
              );
            }
            return null;
          })()}
        </div>

        {/* Delivery Personnel Selection */}
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Delivery Person</h3>
          
          {loadingPersonnel ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            </div>
          ) : deliveryPersonnel.length === 0 ? (
            <div className="text-center py-8">
              <Truck className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">No delivery personnel available</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {deliveryPersonnel.map(person => (
                <div
                  key={person.id}
                  onClick={() => setSelectedDeliveryPerson(person)}
                  className={`border rounded-lg p-4 cursor-pointer transition-all hover:shadow-md ${
                    selectedDeliveryPerson?.id === person.id
                      ? 'border-purple-500 bg-purple-50'
                      : person.isAvailable
                      ? 'border-gray-200 hover:border-gray-300'
                      : 'border-red-200 bg-red-50 opacity-75'
                  }`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="bg-gray-100 p-2 rounded-full">
                        <User className="h-6 w-6 text-gray-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{person.full_name}</h4>
                        <p className="text-sm text-gray-500">{person.email}</p>
                      </div>
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getWorkloadColor(person.currentOrders || 0)}`}>
                      {getWorkloadText(person.currentOrders || 0)}
                    </span>
                  </div>
                  
                  <div className="space-y-2">
                    {person.phone && (
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <Phone className="h-4 w-4" />
                        <span>{person.phone}</span>
                      </div>
                    )}
                    
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Package className="h-4 w-4" />
                      <span>Current Orders: {person.currentOrders || 0}</span>
                    </div>
                    
                    {person.address && (
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <MapPin className="h-4 w-4" />
                        <span>{person.address.city || 'Location not specified'}</span>
                      </div>
                    )}
                  </div>
                  
                  {!person.isAvailable && (
                    <div className="mt-2 text-xs text-red-600 font-medium">
                      At maximum capacity
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {selectedDeliveryPerson && (
            <div className="mt-6 bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h4 className="font-medium text-purple-900 mb-2">Selected Delivery Person:</h4>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-800 font-medium">{selectedDeliveryPerson.full_name}</p>
                  <p className="text-sm text-purple-600">{selectedDeliveryPerson.email}</p>
                  <p className="text-sm text-purple-600">Current workload: {selectedDeliveryPerson.currentOrders || 0} orders</p>
                </div>
                <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getWorkloadColor(selectedDeliveryPerson.currentOrders || 0)}`}>
                  {getWorkloadText(selectedDeliveryPerson.currentOrders || 0)}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleAssignDelivery}
            disabled={!selectedDeliveryPerson || loading}
            className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Assigning...' : 'Assign Delivery Person'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeliveryAssignmentModal;
