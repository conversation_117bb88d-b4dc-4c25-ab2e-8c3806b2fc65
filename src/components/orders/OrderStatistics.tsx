import React, { useState, useEffect } from 'react';
import { Package, Clock, CheckCircle, XCircle, TrendingUp, TrendingDown, Users, DollarSign } from 'lucide-react';
import { supabase } from '../../integrations/supabase/client';
import { formatCurrency } from '../../utils/currency';

interface OrderStats {
  totalOrders: number;
  pendingOrders: number;
  processingOrders: number;
  deliveredOrders: number;
  cancelledOrders: number;
  totalRevenue: number;
  ordersThisMonth: number;
  ordersLastMonth: number;
  topCustomer: {
    name: string;
    orderCount: number;
    totalValue: number;
  } | null;
}

interface OrderStatisticsProps {
  refreshTrigger?: number;
}

const OrderStatistics: React.FC<OrderStatisticsProps> = ({ refreshTrigger = 0 }) => {
  const [stats, setStats] = useState<OrderStats>({
    totalOrders: 0,
    pendingOrders: 0,
    processingOrders: 0,
    deliveredOrders: 0,
    cancelledOrders: 0,
    totalRevenue: 0,
    ordersThisMonth: 0,
    ordersLastMonth: 0,
    topCustomer: null
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadStatistics();
  }, [refreshTrigger]);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get current date ranges
      const now = new Date();
      const startOfThisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

      // Fetch all orders with customer information
      const { data: orders, error: ordersError } = await supabase
        .from('orders')
        .select(`
          id,
          status,
          total,
          created_at,
          customer_id,
          users!orders_customer_id_fkey (
            id,
            full_name
          )
        `);

      if (ordersError) throw ordersError;

      if (!orders) {
        throw new Error('No orders data received');
      }

      // Calculate basic statistics
      const totalOrders = orders.length;
      const pendingOrders = orders.filter(o => o.status === 'pending').length;
      const processingOrders = orders.filter(o => ['confirmed', 'preparing', 'ready'].includes(o.status)).length;
      const deliveredOrders = orders.filter(o => o.status === 'delivered').length;
      const cancelledOrders = orders.filter(o => ['cancelled', 'returned'].includes(o.status)).length;
      const totalRevenue = orders.reduce((sum, order) => sum + (order.total || 0), 0);

      // Calculate monthly statistics
      const ordersThisMonth = orders.filter(order => 
        new Date(order.created_at) >= startOfThisMonth
      ).length;

      const ordersLastMonth = orders.filter(order => {
        const orderDate = new Date(order.created_at);
        return orderDate >= startOfLastMonth && orderDate <= endOfLastMonth;
      }).length;

      // Calculate top customer
      const customerStats = orders.reduce((acc, order) => {
        const customerId = order.customer_id;
        const customerName = order.users?.full_name || 'Unknown Customer';
        
        if (!acc[customerId]) {
          acc[customerId] = {
            name: customerName,
            orderCount: 0,
            totalValue: 0
          };
        }
        
        acc[customerId].orderCount += 1;
        acc[customerId].totalValue += order.total || 0;
        
        return acc;
      }, {} as Record<string, { name: string; orderCount: number; totalValue: number }>);

      const topCustomer = Object.values(customerStats).reduce((top, current) => {
        if (!top || current.totalValue > top.totalValue) {
          return current;
        }
        return top;
      }, null as { name: string; orderCount: number; totalValue: number } | null);

      setStats({
        totalOrders,
        pendingOrders,
        processingOrders,
        deliveredOrders,
        cancelledOrders,
        totalRevenue,
        ordersThisMonth,
        ordersLastMonth,
        topCustomer
      });

    } catch (err) {
      console.error('Error loading order statistics:', err);
      setError(err instanceof Error ? err.message : 'Failed to load statistics');
    } finally {
      setLoading(false);
    }
  };

  const getMonthlyChangePercentage = () => {
    if (stats.ordersLastMonth === 0) {
      return stats.ordersThisMonth > 0 ? 100 : 0;
    }
    return Math.round(((stats.ordersThisMonth - stats.ordersLastMonth) / stats.ordersLastMonth) * 100);
  };

  const monthlyChange = getMonthlyChangePercentage();
  const isPositiveChange = monthlyChange >= 0;

  if (loading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-xl p-6 mb-8">
        <div className="flex items-center space-x-2">
          <XCircle className="h-5 w-5 text-red-500" />
          <span className="text-red-700">Error loading statistics: {error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {/* Processing Orders - Moved to first position */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Processing Orders</p>
            <p className="text-3xl font-bold text-blue-600">{stats.processingOrders}</p>
          </div>
          <div className="p-3 bg-blue-100 rounded-full">
            <Package className="h-6 w-6 text-blue-600" />
          </div>
        </div>
        <div className="mt-4 flex items-center space-x-2">
          <span className="text-sm text-gray-500">Currently processing</span>
        </div>
      </div>

      {/* Pending Orders */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Pending Orders</p>
            <p className="text-3xl font-bold text-yellow-600">{stats.pendingOrders}</p>
          </div>
          <div className="p-3 bg-yellow-100 rounded-full">
            <Clock className="h-6 w-6 text-yellow-600" />
          </div>
        </div>
        <div className="mt-4 flex items-center space-x-2">
          <span className="text-sm text-gray-500">Awaiting processing</span>
        </div>
      </div>

      {/* Delivered Orders */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Delivered Orders</p>
            <p className="text-3xl font-bold text-green-600">{stats.deliveredOrders}</p>
          </div>
          <div className="p-3 bg-green-100 rounded-full">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
        </div>
        <div className="mt-4 flex items-center space-x-2">
          <span className="text-sm text-gray-500">Successfully completed</span>
        </div>
      </div>

      {/* Cancelled Orders */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Cancelled Orders</p>
            <p className="text-3xl font-bold text-red-600">{stats.cancelledOrders}</p>
          </div>
          <div className="p-3 bg-red-100 rounded-full">
            <XCircle className="h-6 w-6 text-red-600" />
          </div>
        </div>
        <div className="mt-4 flex items-center space-x-2">
          <span className="text-sm text-gray-500">Cancelled orders</span>
        </div>
      </div>

      {/* Total Revenue */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Total Revenue</p>
            <p className="text-3xl font-bold text-teal-600">{formatCurrency(stats.totalRevenue)}</p>
          </div>
          <div className="p-3 bg-amber-100 rounded-full">
            <DollarSign className="h-6 w-6 text-amber-600" />
          </div>
        </div>
        <div className="mt-4 flex items-center space-x-2">
          <span className="text-sm text-gray-500">All time revenue</span>
        </div>
      </div>

      {/* Monthly Comparison */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">This Month</p>
            <p className="text-3xl font-bold text-gray-900">{stats.ordersThisMonth}</p>
          </div>
          <div className={`p-3 rounded-full ${isPositiveChange ? 'bg-green-100' : 'bg-red-100'}`}>
            {isPositiveChange ? (
              <TrendingUp className={`h-6 w-6 ${isPositiveChange ? 'text-green-600' : 'text-red-600'}`} />
            ) : (
              <TrendingDown className={`h-6 w-6 ${isPositiveChange ? 'text-green-600' : 'text-red-600'}`} />
            )}
          </div>
        </div>
        <div className="mt-4 flex items-center space-x-2">
          <span className={`text-sm font-medium ${isPositiveChange ? 'text-green-600' : 'text-red-600'}`}>
            {isPositiveChange ? '+' : ''}{monthlyChange}%
          </span>
          <span className="text-sm text-gray-500">vs last month</span>
        </div>
      </div>

      {/* Top Customer */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Top Customer</p>
            <p className="text-lg font-bold text-gray-900 truncate">
              {stats.topCustomer?.name || 'No customers'}
            </p>
          </div>
          <div className="p-3 bg-purple-100 rounded-full">
            <Users className="h-6 w-6 text-purple-600" />
          </div>
        </div>
        <div className="mt-4 space-y-1">
          {stats.topCustomer && (
            <>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Orders:</span>
                <span className="font-medium">{stats.topCustomer.orderCount}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Value:</span>
                <span className="font-medium">{formatCurrency(stats.topCustomer.totalValue)}</span>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Total Orders - Moved to last position */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Total Orders</p>
            <p className="text-3xl font-bold text-gray-900">{stats.totalOrders}</p>
          </div>
          <div className="p-3 bg-teal-100 rounded-full">
            <Package className="h-6 w-6 text-teal-600" />
          </div>
        </div>
        <div className="mt-4 flex items-center space-x-2">
          <span className="text-sm text-gray-500">All time orders</span>
        </div>
      </div>
    </div>
  );
};

export default OrderStatistics;
