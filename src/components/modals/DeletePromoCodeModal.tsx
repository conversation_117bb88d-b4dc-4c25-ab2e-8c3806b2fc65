import { useState } from 'react';
import { 
  <PERSON>, 
  Trash2, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>,
  Info,
  Tag
} from 'lucide-react';
import { promoCodesService } from '../../services/promoCodesService';
import { PromoCode } from '../../types/promoCodes';

interface DeletePromoCodeModalProps {
  isOpen: boolean;
  promoCode: PromoCode | null;
  promoCodes?: PromoCode[]; // For bulk delete
  onClose: () => void;
  onSuccess: () => void;
}

const DeletePromoCodeModal = ({ 
  isOpen, 
  promoCode, 
  promoCodes = [], 
  onClose, 
  onSuccess 
}: DeletePromoCodeModalProps) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // Determine if this is a bulk delete or single delete
  const isBulkDelete = promoCodes.length > 1;
  const targetCodes = isBulkDelete ? promoCodes : (promoCode ? [promoCode] : []);

  // Check if any codes have been used
  const usedCodes = targetCodes.filter(code => code.current_usage_count > 0);
  const hasUsedCodes = usedCodes.length > 0;

  // Handle delete confirmation
  const handleDelete = async () => {
    if (targetCodes.length === 0) return;

    setLoading(true);
    setError('');

    try {
      console.log('DeletePromoCodeModal: Deleting promo codes', {
        count: targetCodes.length,
        codes: targetCodes.map(c => c.code)
      });

      if (isBulkDelete) {
        // Bulk delete using service
        const result = await promoCodesService.bulkOperation({
          action: 'delete',
          promo_code_ids: targetCodes.map(code => code.id)
        });

        if (!result.success) {
          throw new Error(result.errors?.join(', ') || 'Bulk delete failed');
        }

        console.log('DeletePromoCodeModal: Bulk delete successful', {
          affected: result.affected_count
        });
      } else {
        // Single delete
        await promoCodesService.deletePromoCode(targetCodes[0].id);
        console.log('DeletePromoCodeModal: Single delete successful');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('DeletePromoCodeModal: Error deleting promo codes:', error);
      setError(error instanceof Error ? error.message : 'Failed to delete promo code(s). Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || targetCodes.length === 0) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg mr-3">
              <Trash2 className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {isBulkDelete ? 'Delete Promo Codes' : 'Delete Promo Code'}
              </h2>
              <p className="text-sm text-gray-500">
                {isBulkDelete 
                  ? `${targetCodes.length} promo codes selected`
                  : 'This action cannot be undone'
                }
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <AlertTriangle className="h-4 w-4 text-red-600 mr-2" />
                <span className="text-sm text-red-800">{error}</span>
              </div>
            </div>
          )}

          {/* Warning for used codes */}
          {hasUsedCodes && (
            <div className="mb-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-amber-600 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-amber-800 mb-1">
                    Warning: Used Promo Codes
                  </h4>
                  <p className="text-sm text-amber-700 mb-2">
                    {isBulkDelete 
                      ? `${usedCodes.length} of the selected promo codes have been used by customers:`
                      : 'This promo code has been used by customers:'
                    }
                  </p>
                  <div className="space-y-1">
                    {usedCodes.slice(0, 5).map(code => (
                      <div key={code.id} className="text-xs text-amber-600 font-mono">
                        {code.code} - {code.current_usage_count} use{code.current_usage_count !== 1 ? 's' : ''}
                      </div>
                    ))}
                    {usedCodes.length > 5 && (
                      <div className="text-xs text-amber-600">
                        ...and {usedCodes.length - 5} more
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-amber-700 mt-2">
                    Deleting these codes will not affect past orders, but customers won't be able to use them again.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Confirmation Message */}
          <div className="mb-6">
            <p className="text-gray-700 mb-3">
              {isBulkDelete 
                ? `Are you sure you want to delete ${targetCodes.length} promo codes? This action cannot be undone.`
                : `Are you sure you want to delete the promo code "${promoCode?.code}"? This action cannot be undone.`
              }
            </p>

            {/* List of codes to be deleted */}
            {isBulkDelete && (
              <div className="bg-gray-50 rounded-lg p-3 max-h-32 overflow-y-auto">
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  Promo codes to be deleted:
                </h4>
                <div className="space-y-1">
                  {targetCodes.map(code => (
                    <div key={code.id} className="flex items-center justify-between text-sm">
                      <span className="font-mono text-gray-900">{code.code}</span>
                      <span className="text-gray-500">{code.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Additional Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6">
            <div className="flex items-start">
              <Info className="h-4 w-4 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">What happens when you delete promo codes:</p>
                <ul className="text-xs space-y-1 list-disc list-inside">
                  <li>The promo code(s) will be permanently removed from the system</li>
                  <li>Customers will no longer be able to use these codes</li>
                  <li>Past orders and usage history will remain intact</li>
                  <li>This action cannot be reversed</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            disabled={loading}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
          >
            Cancel
          </button>
          
          <button
            onClick={handleDelete}
            disabled={loading}
            className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
              loading
                ? 'bg-gray-400 text-white cursor-not-allowed'
                : 'bg-red-600 text-white hover:bg-red-700'
            }`}
          >
            {loading ? (
              <>
                <Loader className="h-4 w-4 mr-2 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                {isBulkDelete 
                  ? `Delete ${targetCodes.length} Codes`
                  : 'Delete Promo Code'
                }
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeletePromoCodeModal;
