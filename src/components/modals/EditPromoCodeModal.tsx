import { useState, useEffect } from 'react';
import {
  X,
  Edit,
  Percent,
  DollarSign,
  AlertCircle,
  Loader,
  Save,
  Info,
  Check
} from 'lucide-react';
import { getActiveCategories } from '../../services/liveCategoryService';
import { getProducts } from '../../services/inventoryService';
import { Category, Product } from '../../types/inventory';
import { promoCodesService } from '../../services/promoCodesService';
import {
  PromoCode,
  PromoCodeFormData,
  PromoCodeFormErrors,
  DiscountType,
  ApplicableTo,
  DEFAULT_PROMO_CODE_VALIDATION_RULES
} from '../../types/promoCodes';

interface EditPromoCodeModalProps {
  isOpen: boolean;
  promoCode: PromoCode | null;
  onClose: () => void;
  onSuccess: () => void;
}

const EditPromoCodeModal = ({ isOpen, promoCode, onClose, onSuccess }: EditPromoCodeModalProps) => {
  // Form state
  const [formData, setFormData] = useState<PromoCodeFormData>({
    code: '',
    name: '',
    description: '',
    discount_type: 'percentage',
    discount_value: 0,
    usage_limit_total: undefined,
    usage_limit_per_customer: 1,
    valid_from: new Date().toISOString().split('T')[0],
    valid_until: '',
    minimum_order_amount: 0,
    maximum_discount_amount: undefined,
    applicable_to: 'all',
    restricted_categories: [],
    restricted_products: [],
    is_active: true,
    is_featured: false
  });

  // Form validation and UI state
  const [errors, setErrors] = useState<PromoCodeFormErrors>({});
  const [loading, setLoading] = useState(false);

  // Categories and products data
  const [categories, setCategories] = useState<Category[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [loadingProducts, setLoadingProducts] = useState(false);

  // Load categories and products
  const loadCategories = async () => {
    setLoadingCategories(true);
    try {
      const categoriesData = await getActiveCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setLoadingCategories(false);
    }
  };

  const loadProducts = async () => {
    setLoadingProducts(true);
    try {
      const productsData = await getProducts({ isActive: true });
      setProducts(productsData);
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setLoadingProducts(false);
    }
  };

  // Populate form when modal opens or promo code changes
  useEffect(() => {
    if (isOpen && promoCode) {
      setFormData({
        code: promoCode.code,
        name: promoCode.name,
        description: promoCode.description || '',
        discount_type: promoCode.discount_type,
        discount_value: promoCode.discount_value,
        usage_limit_total: promoCode.usage_limit_total,
        usage_limit_per_customer: promoCode.usage_limit_per_customer,
        valid_from: promoCode.valid_from.split('T')[0],
        valid_until: promoCode.valid_until ? promoCode.valid_until.split('T')[0] : '',
        minimum_order_amount: promoCode.minimum_order_amount,
        maximum_discount_amount: promoCode.maximum_discount_amount,
        applicable_to: promoCode.applicable_to,
        restricted_categories: promoCode.restricted_categories,
        restricted_products: promoCode.restricted_products,
        is_active: promoCode.is_active,
        is_featured: promoCode.is_featured
      });
      setErrors({});

      // Load categories and products when modal opens
      loadCategories();
      loadProducts();
    }
  }, [isOpen, promoCode]);

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: PromoCodeFormErrors = {};
    const rules = DEFAULT_PROMO_CODE_VALIDATION_RULES;

    // Code validation (can't change existing code)
    if (!formData.code.trim()) {
      newErrors.code = 'Promo code is required';
    }

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.length < rules.name.minLength) {
      newErrors.name = `Name must be at least ${rules.name.minLength} characters`;
    }

    // Discount value validation
    if (formData.discount_value <= 0) {
      newErrors.discount_value = 'Discount value must be greater than 0';
    } else if (formData.discount_type === 'percentage' && formData.discount_value > 100) {
      newErrors.discount_value = 'Percentage discount cannot exceed 100%';
    }

    // Usage limit validation
    if (formData.usage_limit_per_customer < 1) {
      newErrors.usage_limit_per_customer = 'Usage limit per customer must be at least 1';
    }

    // Date validation
    if (!formData.valid_from) {
      newErrors.valid_from = 'Valid from date is required';
    }

    if (formData.valid_until && formData.valid_from) {
      const validFrom = new Date(formData.valid_from);
      const validUntil = new Date(formData.valid_until);
      if (validUntil <= validFrom) {
        newErrors.valid_until = 'Valid until date must be after valid from date';
      }
    }

    // Minimum order amount validation
    if (formData.minimum_order_amount < 0) {
      newErrors.minimum_order_amount = 'Minimum order amount cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm() || !promoCode) {
      return;
    }

    setLoading(true);
    try {
      console.log('EditPromoCodeModal: Updating promo code', { id: promoCode.id, formData });

      // Prepare form data with proper null handling for dates
      const submitData = {
        ...formData,
        valid_until: formData.valid_until || null, // Convert empty string to null
        usage_limit_total: formData.usage_limit_total || null, // Convert undefined to null
        maximum_discount_amount: formData.maximum_discount_amount || null // Convert undefined to null
      };

      await promoCodesService.updatePromoCode(promoCode.id, submitData);
      
      console.log('EditPromoCodeModal: Promo code updated successfully');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('EditPromoCodeModal: Error updating promo code:', error);
      setErrors({ 
        general: error instanceof Error ? error.message : 'Failed to update promo code. Please try again.' 
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof PromoCodeFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  if (!isOpen || !promoCode) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-teal-100 rounded-lg mr-3">
              <Edit className="h-6 w-6 text-teal-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Edit Promo Code</h2>
              <p className="text-sm text-gray-500">Modify promo code details</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Form Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {/* General Error */}
          {errors.general && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
                <span className="text-sm text-red-800">{errors.general}</span>
              </div>
            </div>
          )}

          {/* Usage Warning */}
          {promoCode.current_usage_count > 0 && (
            <div className="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-center">
                <Info className="h-4 w-4 text-amber-600 mr-2" />
                <span className="text-sm text-amber-800">
                  This promo code has been used {promoCode.current_usage_count} time{promoCode.current_usage_count !== 1 ? 's' : ''}. 
                  Some changes may affect existing usage.
                </span>
              </div>
            </div>
          )}

          <div className="space-y-6">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Promo Code (Read-only) */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Promo Code
                  </label>
                  <input
                    type="text"
                    value={formData.code}
                    readOnly
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 cursor-not-allowed"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Promo code cannot be changed after creation
                  </p>
                </div>

                {/* Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Display Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 ${
                      errors.name ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Welcome Discount"
                    maxLength={255}
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                  )}
                </div>
              </div>

              {/* Description */}
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  placeholder="Brief description of this promo code..."
                  rows={3}
                />
              </div>

              {/* Status Toggles */}
              <div className="grid grid-cols-2 gap-4 mt-4">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Active</label>
                    <p className="text-xs text-gray-500">Enable this promo code</p>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleInputChange('is_active', !formData.is_active)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      formData.is_active ? 'bg-teal-600' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        formData.is_active ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Featured</label>
                    <p className="text-xs text-gray-500">Highlight this promo</p>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleInputChange('is_featured', !formData.is_featured)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      formData.is_featured ? 'bg-amber-500' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        formData.is_featured ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              </div>
            </div>

            {/* Discount Configuration */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Discount Configuration</h3>

              {/* Discount Type */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Discount Type *
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    type="button"
                    onClick={() => handleInputChange('discount_type', 'percentage')}
                    className={`p-4 border-2 rounded-lg transition-colors ${
                      formData.discount_type === 'percentage'
                        ? 'border-teal-500 bg-teal-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <Percent className="h-6 w-6 mx-auto mb-2 text-green-600" />
                    <div className="text-sm font-medium">Percentage</div>
                    <div className="text-xs text-gray-500">% off total</div>
                  </button>
                  <button
                    type="button"
                    onClick={() => handleInputChange('discount_type', 'fixed_amount')}
                    className={`p-4 border-2 rounded-lg transition-colors ${
                      formData.discount_type === 'fixed_amount'
                        ? 'border-teal-500 bg-teal-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <DollarSign className="h-6 w-6 mx-auto mb-2 text-blue-600" />
                    <div className="text-sm font-medium">Fixed Amount</div>
                    <div className="text-xs text-gray-500">Dh off total</div>
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Discount Value */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Discount Value *
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      value={formData.discount_value}
                      onChange={(e) => handleInputChange('discount_value', parseFloat(e.target.value) || 0)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 ${
                        errors.discount_value ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="10"
                      min="0"
                      step="0.01"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 text-sm">
                        {formData.discount_type === 'percentage' ? '%' : 'Dh'}
                      </span>
                    </div>
                  </div>
                  {errors.discount_value && (
                    <p className="mt-1 text-sm text-red-600">{errors.discount_value}</p>
                  )}
                </div>

                {/* Minimum Order Amount */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Minimum Order Amount
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      value={formData.minimum_order_amount}
                      onChange={(e) => handleInputChange('minimum_order_amount', parseFloat(e.target.value) || 0)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 ${
                        errors.minimum_order_amount ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="0"
                      min="0"
                      step="0.01"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 text-sm">Dh</span>
                    </div>
                  </div>
                  {errors.minimum_order_amount && (
                    <p className="mt-1 text-sm text-red-600">{errors.minimum_order_amount}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Usage Limits & Validity */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Usage Limits & Validity</h3>

              {/* Usage Limits */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Total Usage Limit
                  </label>
                  <input
                    type="number"
                    value={formData.usage_limit_total || ''}
                    onChange={(e) => handleInputChange('usage_limit_total', e.target.value ? parseInt(e.target.value) : undefined)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                    placeholder="Unlimited"
                    min="1"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Current usage: {promoCode.current_usage_count}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Per Customer Limit *
                  </label>
                  <input
                    type="number"
                    value={formData.usage_limit_per_customer}
                    onChange={(e) => handleInputChange('usage_limit_per_customer', parseInt(e.target.value) || 1)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 ${
                      errors.usage_limit_per_customer ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="1"
                    min="1"
                  />
                  {errors.usage_limit_per_customer && (
                    <p className="mt-1 text-sm text-red-600">{errors.usage_limit_per_customer}</p>
                  )}
                </div>
              </div>

              {/* Validity Period */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Valid From *
                  </label>
                  <input
                    type="date"
                    value={formData.valid_from}
                    onChange={(e) => handleInputChange('valid_from', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 ${
                      errors.valid_from ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {errors.valid_from && (
                    <p className="mt-1 text-sm text-red-600">{errors.valid_from}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Valid Until
                  </label>
                  <input
                    type="date"
                    value={formData.valid_until}
                    onChange={(e) => handleInputChange('valid_until', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 ${
                      errors.valid_until ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {errors.valid_until && (
                    <p className="mt-1 text-sm text-red-600">{errors.valid_until}</p>
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    Leave empty for no expiry date
                  </p>
                </div>
              </div>

              {/* Applicable To */}
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Applicable To
                </label>
                <select
                  value={formData.applicable_to}
                  onChange={(e) => handleInputChange('applicable_to', e.target.value as ApplicableTo)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                >
                  <option value="all">All Products</option>
                  <option value="categories">Specific Categories</option>
                  <option value="products">Specific Products</option>
                </select>
              </div>

              {/* Category Selection */}
              {formData.applicable_to === 'categories' && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Categories
                  </label>
                  {loadingCategories ? (
                    <div className="flex items-center justify-center py-4">
                      <Loader className="h-4 w-4 animate-spin text-teal-600 mr-2" />
                      <span className="text-sm text-gray-500">Loading categories...</span>
                    </div>
                  ) : (
                    <div className="border border-gray-300 rounded-lg p-3 max-h-40 overflow-y-auto">
                      {categories.length === 0 ? (
                        <p className="text-sm text-gray-500 text-center py-2">No categories available</p>
                      ) : (
                        <div className="space-y-2">
                          {categories.map((category) => (
                            <label key={category.id} className="flex items-center">
                              <input
                                type="checkbox"
                                checked={formData.restricted_categories.includes(category.id)}
                                onChange={(e) => {
                                  const newCategories = e.target.checked
                                    ? [...formData.restricted_categories, category.id]
                                    : formData.restricted_categories.filter(id => id !== category.id);
                                  handleInputChange('restricted_categories', newCategories);
                                }}
                                className="rounded border-gray-300 text-teal-600 focus:ring-teal-500 mr-2"
                              />
                              <span className="text-sm text-gray-700">{category.name}</span>
                              {formData.restricted_categories.includes(category.id) && (
                                <Check className="h-4 w-4 text-teal-600 ml-auto" />
                              )}
                            </label>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    Selected: {formData.restricted_categories.length} categor{formData.restricted_categories.length !== 1 ? 'ies' : 'y'}
                  </p>
                </div>
              )}

              {/* Product Selection */}
              {formData.applicable_to === 'products' && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Products
                  </label>
                  {loadingProducts ? (
                    <div className="flex items-center justify-center py-4">
                      <Loader className="h-4 w-4 animate-spin text-teal-600 mr-2" />
                      <span className="text-sm text-gray-500">Loading products...</span>
                    </div>
                  ) : (
                    <div className="border border-gray-300 rounded-lg p-3 max-h-40 overflow-y-auto">
                      {products.length === 0 ? (
                        <p className="text-sm text-gray-500 text-center py-2">No products available</p>
                      ) : (
                        <div className="space-y-2">
                          {products.map((product) => (
                            <label key={product.id} className="flex items-center">
                              <input
                                type="checkbox"
                                checked={formData.restricted_products.includes(product.id)}
                                onChange={(e) => {
                                  const newProducts = e.target.checked
                                    ? [...formData.restricted_products, product.id]
                                    : formData.restricted_products.filter(id => id !== product.id);
                                  handleInputChange('restricted_products', newProducts);
                                }}
                                className="rounded border-gray-300 text-teal-600 focus:ring-teal-500 mr-2"
                              />
                              <div className="flex-1">
                                <span className="text-sm text-gray-700">{product.title}</span>
                                <span className="text-xs text-gray-500 ml-2">({product.sku})</span>
                              </div>
                              {formData.restricted_products.includes(product.id) && (
                                <Check className="h-4 w-4 text-teal-600 ml-auto" />
                              )}
                            </label>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    Selected: {formData.restricted_products.length} product{formData.restricted_products.length !== 1 ? 's' : ''}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-500">
            Created: {new Date(promoCode.created_at).toLocaleDateString()}
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>

            <button
              onClick={handleSubmit}
              disabled={loading}
              className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                loading
                  ? 'bg-gray-400 text-white cursor-not-allowed'
                  : 'bg-teal-600 text-white hover:bg-teal-700'
              }`}
            >
              {loading ? (
                <>
                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Update Promo Code
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditPromoCodeModal;
