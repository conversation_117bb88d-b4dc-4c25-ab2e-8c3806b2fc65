import { useState, useEffect } from 'react';
import { X, Plus, Minus, Trash2, ShoppingBag, Tag, Percent, Gift, AlertCircle, CheckCircle } from 'lucide-react';
import { validatePromoCode, promoCodeService, PromoCode } from '../../services/promoCodeService';
import { createOrder } from '../../services/orderService';
import CheckoutModal from '../checkout/CheckoutModal';
import { liveDataService } from '../../services/liveDataService';

interface CartItem {
  id: number;
  title: string;
  price: number;
  quantity: number;
  image: string;
  category: string;
}

interface CartProps {
  isOpen: boolean;
  onClose: () => void;
  items: CartItem[];
  onUpdateQuantity: (id: number, quantity: number) => void;
  onRemoveItem: (id: number) => void;
  onClearCart: () => void;
  userType?: string;
  user?: {
    id: string;
    fullName: string;
    email: string;
  };
}

const Cart = ({ isOpen, onClose, items, onUpdateQuantity, onRemoveItem, onClearCart, userType = 'client', user }: CartProps) => {
  const [promoCode, setPromoCode] = useState('');
  const [appliedPromo, setAppliedPromo] = useState<PromoCode | null>(null);
  const [promoError, setPromoError] = useState('');
  const [promoSuccess, setPromoSuccess] = useState('');
  const [isValidatingPromo, setIsValidatingPromo] = useState(false);
  const [showCheckout, setShowCheckout] = useState(false);
  const [availablePromoCodes, setAvailablePromoCodes] = useState<PromoCode[]>([]);
  const [showPromoSuggestions, setShowPromoSuggestions] = useState(false);
  const [showAllOffers, setShowAllOffers] = useState(false);
  const [loadingOffers, setLoadingOffers] = useState(false);

  const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const deliveryFee = subtotal > 200 ? 0 : 25;
  const discountAmount = appliedPromo ? calculateDiscount(subtotal, appliedPromo) : 0;
  const total = subtotal + deliveryFee - discountAmount;

  // Load available promo codes on mount
  useEffect(() => {
    loadAvailablePromoCodes();

    // Subscribe to real-time promo code updates
    const unsubscribe = promoCodeService.subscribe((promoCodes) => {
      console.log('Cart: Promo codes updated', promoCodes);
      setAvailablePromoCodes(promoCodes);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  const loadAvailablePromoCodes = async () => {
    try {
      console.log('Cart: Loading available promo codes...');
      const promoCodes = await promoCodeService.getAvailableOffers();
      console.log('Cart: Loaded promo codes:', promoCodes);
      setAvailablePromoCodes(promoCodes);
    } catch (error) {
      console.error('Error loading promo codes:', error);
    }
  };

  function calculateDiscount(amount: number, promo: PromoCode): number {
    if (promo.discount_type === 'percentage') {
      let discount = (amount * promo.discount_value) / 100;
      // Apply maximum discount limit if specified
      if (promo.maximum_discount_amount && discount > promo.maximum_discount_amount) {
        discount = promo.maximum_discount_amount;
      }
      return discount;
    } else if (promo.discount_type === 'fixed_amount') {
      return promo.discount_value;
    }
    return 0;
  }

  const handleApplyPromo = async () => {
    if (!promoCode.trim()) return;

    setIsValidatingPromo(true);
    setPromoError('');
    setPromoSuccess('');

    try {
      const validation = await validatePromoCode(promoCode, subtotal);
      if (validation.isValid) {
        setAppliedPromo(validation.promoCode!);
        setPromoSuccess(validation.message);
        setPromoCode('');
        setShowPromoSuggestions(false);
      } else {
        setPromoError(validation.message);
        setAppliedPromo(null);
      }
    } catch (error) {
      setPromoError('Error validating promo code');
      setAppliedPromo(null);
    } finally {
      setIsValidatingPromo(false);
    }
  };

  const handleRemovePromo = () => {
    setAppliedPromo(null);
    setPromoCode('');
    setPromoError('');
    setPromoSuccess('');
  };

  const handleApplySuggestedPromo = async (suggestedPromo: PromoCode) => {
    setPromoCode(suggestedPromo.code);
    setShowPromoSuggestions(false);

    // Auto-apply the suggested promo
    setIsValidatingPromo(true);
    setPromoError('');
    setPromoSuccess('');

    try {
      const validation = await validatePromoCode(suggestedPromo.code, subtotal);
      if (validation.isValid) {
        setAppliedPromo(validation.promoCode!);
        setPromoSuccess(validation.message);
        setPromoCode('');
      } else {
        setPromoError(validation.message);
        setAppliedPromo(null);
      }
    } catch (error) {
      setPromoError('Error validating promo code');
      setAppliedPromo(null);
    } finally {
      setIsValidatingPromo(false);
    }
  };

  const getSuggestedPromoCodes = () => {
    return availablePromoCodes.filter(promo =>
      promo.is_active &&
      (!promo.minimum_order_amount || subtotal >= promo.minimum_order_amount) &&
      promo.id !== appliedPromo?.id
    );
  };

  const handleShowAllOffers = async () => {
    setLoadingOffers(true);
    setShowAllOffers(true);

    try {
      // Refresh available promo codes to ensure we have the latest data
      await loadAvailablePromoCodes();
    } catch (error) {
      console.error('Error loading offers:', error);
    } finally {
      setLoadingOffers(false);
    }
  };

  const formatExpiryDate = (dateString?: string) => {
    if (!dateString) return 'No expiry';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDiscountText = (promo: PromoCode) => {
    if (promo.discount_type === 'percentage') {
      const maxText = promo.maximum_discount_amount
        ? ` (max ${promo.maximum_discount_amount} Dh)`
        : '';
      return `${promo.discount_value}% off${maxText}`;
    } else {
      return `${promo.discount_value} Dh off`;
    }
  };

  const handleCheckout = async (orderData: any) => {
    try {
      console.log('Processing order:', orderData);
      console.log('Current user:', user);

      // Validate user is logged in
      if (!user?.id) {
        throw new Error('You must be logged in to place an order');
      }

      // Validate cart has items
      if (!items || items.length === 0) {
        throw new Error('Your cart is empty');
      }

      // Validate cart items
      const validItems = items.filter(item =>
        item.id &&
        item.title &&
        item.price > 0 &&
        item.quantity > 0
      );

      if (validItems.length === 0) {
        throw new Error('No valid items in cart');
      }

      if (validItems.length !== items.length) {
        console.warn(`${items.length - validItems.length} invalid items removed from cart`);
      }

      // Create the order with proper structure
      const orderPayload = {
        customerId: user.id,
        customerName: user?.fullName || user?.full_name || 'User',
        customerEmail: user?.email || '',
        items: validItems.map(item => ({
          id: item.id, // Keep original ID format for now
          title: item.title,
          category: item.category,
          price: Number(item.price),
          quantity: Number(item.quantity),
          image: item.image
        })),
        subtotal: Number(subtotal.toFixed(2)),
        deliveryFee: Number(deliveryFee.toFixed(2)),
        discount: Number(discountAmount.toFixed(2)),
        total: Number(total.toFixed(2)),
        status: 'pending' as const,
        paymentMethod: orderData.paymentMethod || 'cash', // Use the actual payment method from checkout
        paymentStatus: 'pending' as const,
        deliveryAddress: {
          address: orderData.address,
          city: orderData.city,
          postalCode: orderData.postalCode,
          coordinates: orderData.deliveryCoordinates || null
        },
        selectedBranch: null, // Will be set by the system
        promoCode: appliedPromo?.code,
        notes: orderData.notes
      };

      const newOrder = await createOrder(orderPayload);
      console.log('Order created successfully:', newOrder);

      if (!newOrder) {
        throw new Error('Order creation failed - no order returned');
      }

      // Apply promo code usage tracking if a promo was used
      if (appliedPromo) {
        console.log('Applying promo code usage tracking for:', appliedPromo.code);
        const applyResult = await promoCodeService.applyPromoCode(appliedPromo.code);

        if (!applyResult.success) {
          console.warn('Failed to update promo code usage count:', applyResult.error);
          // Don't fail the order, just log the warning
        } else {
          console.log('Promo code usage count updated successfully');
        }
      }

      // Clear cart and reset promo
      onClearCart();
      setAppliedPromo(null);
      setPromoCode('');

      // Show success message (you can add a toast here)
      alert(`Order placed successfully! Order ID: ${newOrder.id}`);

    } catch (error) {
      console.error('Error creating order:', error);
      const errorMessage = error instanceof Error ? error.message : 'Error placing order. Please try again.';
      alert(errorMessage);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end sm:items-center justify-center z-50">
        <div className="bg-white w-full sm:w-[500px] sm:max-w-lg h-full sm:h-auto sm:max-h-[90vh] sm:rounded-2xl shadow-2xl flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <ShoppingBag className="h-6 w-6 text-teal-600" />
              <h2 className="text-xl font-bold text-gray-900">Shopping Cart</h2>
              <span className="bg-teal-100 text-teal-800 text-sm font-medium px-2 py-1 rounded-full">
                {items.length}
              </span>
            </div>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Cart Items */}
          <div className="flex-1 overflow-y-auto p-6">
            {items.length === 0 ? (
              <div className="text-center py-12">
                <ShoppingBag className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 text-lg">Your cart is empty</p>
                <p className="text-gray-400">Add some products to get started</p>
              </div>
            ) : (
              <div className="space-y-4">
                {items.map(item => (
                  <div key={item.id} className="flex items-center space-x-4 bg-gray-50 p-4 rounded-lg">
                    <img src={item.image} alt={item.title} className="w-16 h-16 object-cover rounded-lg" />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 text-sm">{item.title}</h3>
                      <p className="text-gray-500 text-xs">{item.category}</p>
                      <p className="text-teal-600 font-bold">{item.price.toFixed(2)} DH</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => onUpdateQuantity(item.id, Math.max(0, item.quantity - 1))}
                        className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300"
                      >
                        {item.quantity === 1 ? <Trash2 className="h-4 w-4" /> : <Minus className="h-4 w-4" />}
                      </button>
                      <span className="w-8 text-center font-medium">{item.quantity}</span>
                      <button
                        onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                        className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300"
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}

                {/* Enhanced Promo Code Section */}
                {items.length > 0 && (
                  <div className="border-t pt-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium text-gray-900 flex items-center">
                        <Tag className="h-4 w-4 mr-2 text-teal-600" />
                        Promo Code
                      </h3>
                      {!appliedPromo && availablePromoCodes.length > 0 && (
                        <button
                          onClick={handleShowAllOffers}
                          disabled={loadingOffers}
                          className="text-xs text-teal-600 hover:text-teal-700 font-medium flex items-center disabled:opacity-50"
                        >
                          <Gift className="h-3 w-3 mr-1" />
                          {loadingOffers ? 'Loading...' : 'Available offers'}
                        </button>
                      )}
                    </div>

                    {appliedPromo ? (
                      <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex items-start space-x-2">
                            <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                            <div>
                              <p className="text-green-800 font-semibold text-sm">{appliedPromo.code}</p>
                              <p className="text-green-600 text-xs">
                                {appliedPromo.discount_type === 'percentage'
                                  ? `${appliedPromo.discount_value}% off`
                                  : `${appliedPromo.discount_value} Dh off`}
                                {appliedPromo.minimum_order && ` on orders over ${appliedPromo.minimum_order} Dh`}
                              </p>
                              <p className="text-green-700 text-xs font-medium mt-1">
                                You saved {discountAmount.toFixed(2)} Dh
                              </p>
                            </div>
                          </div>
                          <button
                            onClick={handleRemovePromo}
                            className="text-red-600 hover:text-red-800 text-xs font-medium px-2 py-1 rounded hover:bg-red-50 transition-colors"
                          >
                            Remove
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex space-x-2">
                          <div className="flex-1 relative">
                            <input
                              type="text"
                              placeholder="Enter promo code"
                              value={promoCode}
                              onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
                              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 pr-8"
                              onKeyPress={(e) => e.key === 'Enter' && handleApplyPromo()}
                            />
                            <Percent className="absolute right-2 top-2.5 h-4 w-4 text-gray-400" />
                          </div>
                          <button
                            onClick={handleApplyPromo}
                            disabled={isValidatingPromo || !promoCode.trim()}
                            className="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-4 py-2 text-sm rounded-lg hover:from-teal-700 hover:to-teal-800 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all font-medium"
                          >
                            {isValidatingPromo ? (
                              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                            ) : (
                              'Apply'
                            )}
                          </button>
                        </div>

                        {/* Enhanced Available Offers Display */}
                        {showAllOffers && (
                          <div className="bg-gradient-to-br from-teal-50 via-white to-amber-50 border border-teal-200 rounded-lg p-4 space-y-4">
                            <div className="flex items-center justify-between">
                              <h4 className="text-sm font-semibold text-teal-800 flex items-center">
                                <Gift className="h-4 w-4 mr-2" />
                                All Available Offers
                              </h4>
                              <button
                                onClick={() => setShowAllOffers(false)}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            </div>

                            {loadingOffers ? (
                              <div className="flex items-center justify-center py-4">
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-teal-600"></div>
                                <span className="ml-2 text-sm text-gray-600">Loading offers...</span>
                              </div>
                            ) : availablePromoCodes.length > 0 ? (
                              <div className="space-y-3 max-h-64 overflow-y-auto">
                                {availablePromoCodes.map(promo => {
                                  const isEligible = !promo.minimum_order_amount || subtotal >= promo.minimum_order_amount;
                                  const isApplied = appliedPromo?.id === promo.id;

                                  return (
                                    <div
                                      key={promo.id}
                                      className={`bg-white rounded-lg p-3 border transition-all ${
                                        isApplied
                                          ? 'border-green-300 bg-green-50'
                                          : isEligible
                                            ? 'border-gray-200 hover:border-teal-300 hover:shadow-sm'
                                            : 'border-gray-100 bg-gray-50 opacity-60'
                                      }`}
                                    >
                                      <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                          <div className="flex items-center space-x-2 mb-1">
                                            <span className="text-sm font-bold text-gray-900">{promo.code}</span>
                                            {promo.is_featured && (
                                              <span className="bg-amber-100 text-amber-800 text-xs px-2 py-0.5 rounded-full font-medium">
                                                Featured
                                              </span>
                                            )}
                                            {isApplied && (
                                              <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full font-medium">
                                                Applied
                                              </span>
                                            )}
                                          </div>

                                          {promo.name && (
                                            <p className="text-sm font-medium text-gray-800 mb-1">{promo.name}</p>
                                          )}

                                          <p className="text-xs text-gray-600 mb-2">
                                            {getDiscountText(promo)}
                                            {promo.minimum_order_amount && (
                                              <span className="text-gray-500">
                                                {' '}• Min. order: {promo.minimum_order_amount} Dh
                                              </span>
                                            )}
                                          </p>

                                          {promo.description && (
                                            <p className="text-xs text-gray-500 mb-2">{promo.description}</p>
                                          )}

                                          <div className="flex items-center justify-between text-xs text-gray-500">
                                            <span>Expires: {formatExpiryDate(promo.valid_until)}</span>
                                            {promo.usage_limit_total && (
                                              <span>
                                                {promo.current_usage_count}/{promo.usage_limit_total} used
                                              </span>
                                            )}
                                          </div>
                                        </div>

                                        <div className="ml-3">
                                          {isApplied ? (
                                            <button
                                              onClick={handleRemovePromo}
                                              className="text-xs bg-red-100 text-red-700 px-3 py-1.5 rounded hover:bg-red-200 transition-colors font-medium"
                                            >
                                              Remove
                                            </button>
                                          ) : isEligible ? (
                                            <button
                                              onClick={() => handleApplySuggestedPromo(promo)}
                                              className="text-xs bg-teal-600 text-white px-3 py-1.5 rounded hover:bg-teal-700 transition-colors font-medium"
                                            >
                                              Apply
                                            </button>
                                          ) : (
                                            <div className="text-xs text-gray-400 px-3 py-1.5">
                                              Not eligible
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            ) : (
                              <div className="text-center py-4">
                                <Gift className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                                <p className="text-sm text-gray-500">No offers available at the moment</p>
                              </div>
                            )}
                          </div>
                        )}

                        {/* Quick Suggested Promo Codes (Legacy) */}
                        {showPromoSuggestions && !showAllOffers && getSuggestedPromoCodes().length > 0 && (
                          <div className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg p-3">
                            <h4 className="text-xs font-medium text-amber-800 mb-2 flex items-center">
                              <Gift className="h-3 w-3 mr-1" />
                              Quick suggestions:
                            </h4>
                            <div className="space-y-2">
                              {getSuggestedPromoCodes().slice(0, 2).map(promo => (
                                <div key={promo.id} className="flex items-center justify-between bg-white rounded p-2">
                                  <div>
                                    <p className="text-xs font-medium text-gray-900">{promo.code}</p>
                                    <p className="text-xs text-gray-600">
                                      {getDiscountText(promo)}
                                      {promo.minimum_order_amount && ` on orders over ${promo.minimum_order_amount} Dh`}
                                    </p>
                                  </div>
                                  <button
                                    onClick={() => handleApplySuggestedPromo(promo)}
                                    className="text-xs bg-teal-600 text-white px-2 py-1 rounded hover:bg-teal-700 transition-colors"
                                  >
                                    Apply
                                  </button>
                                </div>
                              ))}
                            </div>
                            <div className="mt-2 pt-2 border-t border-amber-200">
                              <button
                                onClick={handleShowAllOffers}
                                className="text-xs text-teal-600 hover:text-teal-700 font-medium"
                              >
                                View all available offers →
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Success/Error Messages */}
                    {promoSuccess && (
                      <div className="flex items-center space-x-2 text-green-600 bg-green-50 p-2 rounded-lg">
                        <CheckCircle className="h-4 w-4" />
                        <p className="text-xs font-medium">{promoSuccess}</p>
                      </div>
                    )}
                    {promoError && (
                      <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-2 rounded-lg">
                        <AlertCircle className="h-4 w-4" />
                        <p className="text-xs font-medium">{promoError}</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer with totals and checkout */}
          {items.length > 0 && (
            <div className="border-t p-6 space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>{subtotal.toFixed(2)} DH</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Delivery</span>
                  <span>{deliveryFee === 0 ? 'Free' : `${deliveryFee.toFixed(2)} DH`}</span>
                </div>
                {discountAmount > 0 && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Discount</span>
                    <span>-{discountAmount.toFixed(2)} DH</span>
                  </div>
                )}
                <div className="border-t pt-2 flex justify-between font-bold">
                  <span>Total</span>
                  <span>{total.toFixed(2)} DH</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <button
                  onClick={() => setShowCheckout(true)}
                  className="w-full bg-teal-600 text-white py-3 px-4 rounded-lg hover:bg-teal-700 transition-colors"
                >
                  Proceed to Checkout
                </button>
                <button
                  onClick={onClearCart}
                  className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors text-sm"
                >
                  Clear Cart
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      <CheckoutModal
        isOpen={showCheckout}
        onClose={() => setShowCheckout(false)}
        cartItems={items}
        subtotal={subtotal}
        deliveryFee={deliveryFee}
        discountAmount={discountAmount}
        appliedPromo={appliedPromo}
        onCheckout={handleCheckout}
      />
    </>
  );
};

export default Cart;
