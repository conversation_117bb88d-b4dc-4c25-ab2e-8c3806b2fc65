import React, { useState } from 'react';
import { Star, MessageCircle, ThumbsUp, User } from 'lucide-react';
import { reviewService } from '../../services/reviewService';

interface ReviewSystemTestProps {
  productId: string;
  customerId: string;
  customerName: string;
}

const ReviewSystemTest = ({ productId, customerId, customerName }: ReviewSystemTestProps) => {
  const [reviews, setReviews] = useState<any[]>([]);
  const [reviewStats, setReviewStats] = useState<any>({ averageRating: 0, totalReviews: 0 });
  const [newReview, setNewReview] = useState({ rating: 5, title: '', comment: '' });
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const loadReviews = async () => {
    setLoading(true);
    try {
      const [reviewsData, statsData] = await Promise.all([
        reviewService.getProductReviews(productId, 10),
        reviewService.getReviewStats(productId)
      ]);
      setReviews(reviewsData);
      setReviewStats(statsData);
    } catch (error) {
      console.error('Error loading reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitReview = async () => {
    if (!newReview.comment.trim() || newReview.comment.length < 10) {
      alert('Please enter at least 10 characters for your review.');
      return;
    }

    setSubmitting(true);
    try {
      await reviewService.createReview({
        product_id: productId,
        customer_id: customerId,
        customer_name: customerName,
        rating: newReview.rating,
        title: newReview.title.trim(),
        comment: newReview.comment.trim()
      });

      setNewReview({ rating: 5, title: '', comment: '' });
      await loadReviews();
      alert('Review submitted successfully!');
    } catch (error) {
      console.error('Error submitting review:', error);
      alert(error instanceof Error ? error.message : 'Failed to submit review');
    } finally {
      setSubmitting(false);
    }
  };

  const handleMarkHelpful = async (reviewId: string) => {
    try {
      await reviewService.markReviewHelpful(reviewId);
      await loadReviews();
    } catch (error) {
      console.error('Error marking review helpful:', error);
    }
  };

  React.useEffect(() => {
    loadReviews();
  }, [productId]);

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Review System Test</h2>
      
      {/* Product Info */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <p><strong>Product ID:</strong> {productId}</p>
        <p><strong>Customer:</strong> {customerName} ({customerId})</p>
        <p><strong>Average Rating:</strong> {reviewStats.averageRating.toFixed(1)} ({reviewStats.totalReviews} reviews)</p>
      </div>

      {/* Review Form */}
      <div className="bg-teal-50 p-6 rounded-lg mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Write a Review</h3>
        
        {/* Star Rating */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Rating</label>
          <div className="flex items-center space-x-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                onClick={() => setNewReview(prev => ({ ...prev, rating: star }))}
                className="focus:outline-none"
              >
                <Star
                  className={`h-6 w-6 ${
                    star <= newReview.rating
                      ? 'text-amber-500 fill-current'
                      : 'text-gray-300'
                  } hover:text-amber-500 transition-colors`}
                />
              </button>
            ))}
          </div>
        </div>

        {/* Title */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Title (Optional)</label>
          <input
            type="text"
            value={newReview.title}
            onChange={(e) => setNewReview(prev => ({ ...prev, title: e.target.value }))}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-teal-500"
            placeholder="Brief summary of your review"
            maxLength={100}
          />
        </div>

        {/* Comment */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Comment * ({newReview.comment.length}/1000)
          </label>
          <textarea
            value={newReview.comment}
            onChange={(e) => setNewReview(prev => ({ ...prev, comment: e.target.value }))}
            rows={4}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-teal-500"
            placeholder="Share your experience with this product... (minimum 10 characters)"
            maxLength={1000}
          />
        </div>

        {/* Submit Button */}
        <button
          onClick={handleSubmitReview}
          disabled={submitting || !newReview.comment.trim() || newReview.comment.length < 10}
          className="bg-gradient-to-r from-teal-500 to-teal-600 text-white px-6 py-2 rounded-lg hover:from-teal-600 hover:to-teal-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
        >
          {submitting && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          )}
          <span>{submitting ? 'Submitting...' : 'Submit Review'}</span>
        </button>
      </div>

      {/* Reviews List */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Reviews</h3>
          <button
            onClick={loadReviews}
            disabled={loading}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Refresh'}
          </button>
        </div>

        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600 mx-auto mb-4"></div>
            <p className="text-gray-500">Loading reviews...</p>
          </div>
        ) : reviews.length === 0 ? (
          <div className="text-center py-8">
            <MessageCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No reviews yet. Be the first to review!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {reviews.map((review) => (
              <div key={review.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      <User className="h-4 w-4 text-gray-500" />
                      <span className="font-medium text-gray-900">{review.customer_name}</span>
                      {review.is_verified && (
                        <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                          Verified
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < review.rating
                                ? 'text-amber-500 fill-current'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-sm text-gray-500">
                        {new Date(review.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>

                {review.title && (
                  <h4 className="font-medium text-gray-900 mb-2">{review.title}</h4>
                )}

                <p className="text-gray-700 mb-3">{review.comment}</p>

                <button
                  onClick={() => handleMarkHelpful(review.id)}
                  className="flex items-center space-x-1 text-sm text-gray-500 hover:text-gray-700 transition-colors"
                >
                  <ThumbsUp className="h-4 w-4" />
                  <span>Helpful ({review.helpful_count})</span>
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ReviewSystemTest;
