
import { useState, useEffect } from 'react';
import { Shield, Users, FileText, Settings, Eye, EyeOff, Lock } from 'lucide-react';
import RoleManagement from './RoleManagement';
import AuditTrailViewer from './AuditTrailViewer';
import SecuritySettings from './SecuritySettings';
import PasswordManagement from './PasswordManagement';
import { liveDataService } from '../../services/liveDataService';
import { realTimeService } from '../../services/realTimeService';
import { supabase } from '../../integrations/supabase/client';

interface SecurityDashboardProps {
  userRole?: string;
}

const SecurityDashboard = ({ userRole = 'admin' }: SecurityDashboardProps) => {
  const [activeTab, setActiveTab] = useState('password');
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [securityData, setSecurityData] = useState({
    lastLogin: null as string | null,
    loginAttempts: 0,
    sessionCount: 0,
    passwordLastChanged: null as string | null,
    twoFactorEnabled: false,
    accountLocked: false
  });
  const [loading, setLoading] = useState(true);

  // Password management state
  const [showPasswordSection, setShowPasswordSection] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });

  const getTabsForRole = () => {
    if (userRole === 'client' || userRole === 'reseller') {
      // No security tabs for client/reseller users
      return [];
    }

    // Admin and other roles get full access
    return [
      { id: 'roles', label: 'Role Management', icon: Users },
      { id: 'audit', label: 'Audit Trail', icon: FileText },
      { id: 'settings', label: 'Security Settings', icon: Settings }
    ];
  };

  const tabs = getTabsForRole();

  // Load real security data from Supabase
  const loadSecurityData = async () => {
    try {
      setLoading(true);
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        // Get user security information
        const { data: userData } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();

        // Get recent login attempts (if we have an audit table)
        const { data: auditData } = await supabase
          .from('audit_logs')
          .select('*')
          .eq('user_id', user.id)
          .eq('action', 'login')
          .order('created_at', { ascending: false })
          .limit(10);

        setSecurityData({
          lastLogin: user.last_sign_in_at,
          loginAttempts: auditData?.length || 0,
          sessionCount: 1, // Current session
          passwordLastChanged: userData?.password_changed_at || user.created_at,
          twoFactorEnabled: userData?.two_factor_enabled || false,
          accountLocked: userData?.account_locked || false
        });
      }
    } catch (error) {
      console.error('Error loading security data:', error);
      // Set default values on error
      setSecurityData({
        lastLogin: new Date().toISOString(),
        loginAttempts: 0,
        sessionCount: 1,
        passwordLastChanged: new Date().toISOString(),
        twoFactorEnabled: true,
        accountLocked: false
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle password change
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      alert('New passwords do not match');
      return;
    }

    if (passwordData.newPassword.length < 8) {
      alert('Password must be at least 8 characters long');
      return;
    }

    try {
      setPasswordLoading(true);

      // Update password using Supabase Auth
      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      });

      if (error) {
        console.error('Error updating password:', error);
        alert(`Error updating password: ${error.message}`);
        return;
      }

      // Update password change timestamp in users table
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await supabase
          .from('users')
          .update({
            password_changed_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id);
      }

      // Broadcast password change event
      realTimeService.emit('password-changed', {
        userId: user?.id,
        timestamp: new Date().toISOString()
      });

      realTimeService.emit('security-event', {
        type: 'password_changed',
        userId: user?.id,
        timestamp: new Date().toISOString()
      });

      // Reset form and reload security data
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      setShowPasswordSection(false);
      loadSecurityData();

      alert('Password updated successfully!');

    } catch (error) {
      console.error('Error in handlePasswordChange:', error);
      alert('Error updating password. Please try again.');
    } finally {
      setPasswordLoading(false);
    }
  };

  const handlePasswordInputChange = (field: string, value: string) => {
    setPasswordData(prev => ({ ...prev, [field]: value }));
  };

  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };

  // Set default tab based on available tabs
  useEffect(() => {
    if (tabs.length > 0 && !tabs.find(tab => tab.id === activeTab)) {
      setActiveTab(tabs[0].id);
    }
  }, [userRole, tabs, activeTab]);

  // Set up real-time synchronization for security data
  useEffect(() => {
    console.log('SecurityDashboard: Setting up real-time subscriptions');

    // Load initial security data
    loadSecurityData();

    // Subscribe to user changes (for role management and profile updates)
    const userSubscription = liveDataService.subscribeToUsers((payload) => {
      console.log('SecurityDashboard: Real-time user update:', payload);
      setLastUpdate(new Date());
    });

    // Subscribe to security-related events
    const securitySubscription = realTimeService.subscribe('security-event', (payload) => {
      console.log('SecurityDashboard: Security event received:', payload);
      setLastUpdate(new Date());
    });

    const passwordChangeSubscription = realTimeService.subscribe('password-changed', (payload) => {
      console.log('SecurityDashboard: Password change event:', payload);
      setLastUpdate(new Date());
    });

    const roleUpdateSubscription = realTimeService.subscribe('role-updated', (payload) => {
      console.log('SecurityDashboard: Role update event:', payload);
      setLastUpdate(new Date());
    });

    return () => {
      console.log('SecurityDashboard: Cleaning up real-time subscriptions');
      userSubscription.unsubscribe();
      securitySubscription();
      passwordChangeSubscription();
      roleUpdateSubscription();
    };
  }, []);

  // For client/reseller users, show a simplified security info section
  if (userRole === 'client' || userRole === 'reseller') {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">Security Dashboard</h2>
        </div>

        {/* Security Information Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="bg-green-100 p-2 rounded-lg">
                <Shield className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Account Security</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Your account is secure</p>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Two-Factor Authentication</span>
                <span className={`text-sm font-medium ${securityData.twoFactorEnabled ? 'text-green-600' : 'text-amber-600'}`}>
                  {loading ? 'Loading...' : (securityData.twoFactorEnabled ? 'Active' : 'Inactive')}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Account Status</span>
                <span className={`text-sm font-medium ${securityData.accountLocked ? 'text-red-600' : 'text-green-600'}`}>
                  {loading ? 'Loading...' : (securityData.accountLocked ? 'Locked' : 'Active')}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Last Login</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {loading ? 'Loading...' : (securityData.lastLogin ? new Date(securityData.lastLogin).toLocaleDateString() : 'Never')}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="bg-blue-100 p-2 rounded-lg">
                <Lock className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Privacy Settings</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Data protection status</p>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Active Sessions</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {loading ? 'Loading...' : securityData.sessionCount}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Login Attempts</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {loading ? 'Loading...' : securityData.loginAttempts}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Password Changed</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {loading ? 'Loading...' : (securityData.passwordLastChanged ? new Date(securityData.passwordLastChanged).toLocaleDateString() : 'Never')}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Password Management Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="bg-amber-100 p-2 rounded-lg">
                <Lock className="h-5 w-5 text-amber-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Password Management</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Change your account password</p>
              </div>
            </div>
            <button
              onClick={() => setShowPasswordSection(!showPasswordSection)}
              className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 transition-colors flex items-center space-x-2"
            >
              <Lock className="h-4 w-4" />
              <span>{showPasswordSection ? 'Cancel' : 'Change Password'}</span>
            </button>
          </div>

          {showPasswordSection && (
            <form onSubmit={handlePasswordChange} className="space-y-4 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
              {/* Current Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Current Password
                </label>
                <div className="relative">
                  <input
                    type={showPasswords.current ? 'text' : 'password'}
                    value={passwordData.currentPassword}
                    onChange={(e) => handlePasswordInputChange('currentPassword', e.target.value)}
                    required
                    className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Enter your current password"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('current')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    {showPasswords.current ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              {/* New Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  New Password
                </label>
                <div className="relative">
                  <input
                    type={showPasswords.new ? 'text' : 'password'}
                    value={passwordData.newPassword}
                    onChange={(e) => handlePasswordInputChange('newPassword', e.target.value)}
                    required
                    className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Enter your new password"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('new')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    {showPasswords.new ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Password must be at least 8 characters long
                </p>
              </div>

              {/* Confirm New Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Confirm New Password
                </label>
                <div className="relative">
                  <input
                    type={showPasswords.confirm ? 'text' : 'password'}
                    value={passwordData.confirmPassword}
                    onChange={(e) => handlePasswordInputChange('confirmPassword', e.target.value)}
                    required
                    className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Confirm your new password"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('confirm')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    {showPasswords.confirm ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowPasswordSection(false)}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={passwordLoading}
                  className="bg-teal-600 text-white px-6 py-2 rounded-lg hover:bg-teal-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Lock className="h-4 w-4" />
                  <span>{passwordLoading ? 'Updating...' : 'Update Password'}</span>
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white">Security Dashboard</h2>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm border border-gray-200 dark:border-gray-700">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white shadow-md'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
            }`}
          >
            <tab.icon className="h-5 w-5" />
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'password' && <PasswordManagement />}
      {activeTab === 'roles' && <RoleManagement />}
      {activeTab === 'audit' && <AuditTrailViewer />}
      {activeTab === 'settings' && <SecuritySettings />}
    </div>
  );
};

export default SecurityDashboard;
