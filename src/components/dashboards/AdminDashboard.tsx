import { useState, useEffect } from 'react';
import { User, Package, MapPin, ShoppingCart, BarChart3, Settings, FolderPlus, Users, Building2, TrendingUp, Plus, UserPlus, FileText, ClipboardList, MessageSquare, Star, Tag, RefreshCw } from 'lucide-react';
import NavigationTabs from './components/NavigationTabs';
import { liveDashboardService, DashboardStats } from '../../services/liveDashboardService';
import { getDashboardAnalytics, DashboardAnalytics } from '../../services/analyticsService';
import { realTimeService } from '../../services/realTimeService';
import TabContent from './components/TabContent';

import { useResponsive, useResponsiveSpacing } from '../../hooks/useResponsive';
import { useMobileUtils } from '../../hooks/use-mobile';
import { formatCurrency } from '../../utils/currency';
import ProfileManagement from '../profile/ProfileManagement';
import PageLayout from '../layout/PageLayout';
import UserManagement from '../admin/UserManagement';
import ProductManagement from '../inventory/ProductManagement';
import BranchManagement from '../branches/BranchManagement';
import CategoryManagement from '../inventory/CategoryManagement';
import OrderManagement from '../orders/OrderManagement';
import AnalyticsPage from '../pages/AnalyticsPage';
import AdvancedAnalyticsDashboard from '../analytics/AdvancedAnalyticsDashboard';
import PromoCodesPage from '../pages/PromoCodesPage';
import CustomReportBuilder from '../analytics/CustomReportBuilder';
import AdvancedSecurityDashboard from '../security/AdvancedSecurityDashboard';
import SystemHealthMonitor from '../system/SystemHealthMonitor';
import AuditLogsViewer from '../audit/AuditLogsViewer';
import SecurityPage from '../pages/SecurityPage';
import SystemHealthPage from '../pages/SystemHealthPage';
import NotificationsPage from '../pages/NotificationsPage';
import InvoiceManagementPage from '../pages/InvoiceManagementPage';
import AuditLogsPage from '../pages/AuditLogsPage';
import SystemSettingsPage from '../pages/SystemSettingsPage';
import ReviewsManagementPage from '../pages/ReviewsManagementPage';
import ClientManagement from '../clients/ClientManagement';

interface AdminDashboardProps {
  user: any;
  onNavigateFromHeader?: (navigateFunction: (page: string) => void) => void;
}

const AdminDashboard = ({ user: initialUser, onNavigateFromHeader }: AdminDashboardProps) => {
  const [user, setUser] = useState(initialUser);

  // Initialize state from URL parameters and localStorage
  const getInitialPage = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const urlPage = urlParams.get('page');
    const storedPage = localStorage.getItem('admin_current_page');
    const result = urlPage || storedPage || 'dashboard';

    console.log('AdminDashboard.getInitialPage() debug:');
    console.log('  - URL:', window.location.href);
    console.log('  - urlPage:', urlPage);
    console.log('  - storedPage:', storedPage);
    console.log('  - result:', result);

    return result;
  };

  const getInitialTab = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const urlTab = urlParams.get('tab');
    const storedTab = localStorage.getItem('admin_active_tab');
    return urlTab || storedTab || 'overview';
  };

  const [activeTab, setActiveTab] = useState(getInitialTab());
  const [currentPage, setCurrentPage] = useState(() => {
    const initialPage = getInitialPage();
    console.log('AdminDashboard: Initial currentPage state:', initialPage);
    return initialPage;
  });
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);
  const [analytics, setAnalytics] = useState<DashboardAnalytics | null>(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Enhanced responsive utilities
  const responsive = useResponsive();
  const { isMobile, isSmallMobile, isTouch, shouldUseTouchUI } = useMobileUtils();
  const spacing = useResponsiveSpacing();

  // Debug currentPage changes
  useEffect(() => {
    console.log('AdminDashboard: currentPage changed to:', currentPage);
    console.log('AdminDashboard: Current URL:', window.location.href);
    console.log('AdminDashboard: URL params:', new URLSearchParams(window.location.search).toString());
  }, [currentPage]);

  // Load live dashboard statistics with real-time synchronization
  useEffect(() => {
    const initializeDashboard = async () => {
      try {
        // Load dashboard stats
        setStatsLoading(true);
        const stats = await liveDashboardService.getAdminDashboardStats();
        setDashboardStats(stats);
        setLastUpdated(new Date());
        setStatsLoading(false);

        // Load analytics
        setAnalyticsLoading(true);
        const analyticsData = await getDashboardAnalytics();
        setAnalytics(analyticsData);
        setAnalyticsLoading(false);

        console.log('AdminDashboard: Successfully initialized dashboard data');
      } catch (error) {
        console.error('Error initializing dashboard:', error);
        setStatsLoading(false);
        setAnalyticsLoading(false);
      }
    };

    // Function to reload dashboard statistics
    const reloadDashboardStats = async () => {
      try {
        console.log('AdminDashboard: Reloading statistics due to real-time update');
        const stats = await liveDashboardService.getAdminDashboardStats();
        setDashboardStats(stats);
        setLastUpdated(new Date());
      } catch (error) {
        console.error('Error reloading dashboard stats:', error);
      }
    };

    initializeDashboard();

    // Subscribe to real-time updates that affect dashboard statistics
    const unsubscribeOrder = realTimeService.subscribe('order-created', reloadDashboardStats);
    const unsubscribeOrderUpdate = realTimeService.subscribe('order-updated', reloadDashboardStats);
    const unsubscribeOrderStatus = realTimeService.subscribe('order-status-changed', reloadDashboardStats);
    const unsubscribeProduct = realTimeService.subscribe('product-added', reloadDashboardStats);
    const unsubscribeProductUpdate = realTimeService.subscribe('product-updated', reloadDashboardStats);
    const unsubscribeStock = realTimeService.subscribe('stock-updated', reloadDashboardStats);
    const unsubscribeUser = realTimeService.subscribe('user-created', reloadDashboardStats);
    const unsubscribeUserUpdate = realTimeService.subscribe('user-updated', reloadDashboardStats);

    // Cleanup subscriptions
    return () => {
      unsubscribeOrder();
      unsubscribeOrderUpdate();
      unsubscribeOrderStatus();
      unsubscribeProduct();
      unsubscribeProductUpdate();
      unsubscribeStock();
      unsubscribeUser();
      unsubscribeUserUpdate();
    };
  }, []);

  const handleUserUpdate = (updatedUser: any) => {
    setUser(updatedUser);
  };

  const handleNavigate = (page: string, tab?: string) => {
    console.log('AdminDashboard.handleNavigate called with:', { page, tab });
    console.log('AdminDashboard.handleNavigate - Current page before:', currentPage);

    setCurrentPage(page);
    console.log('AdminDashboard.handleNavigate - Setting page to:', page);

    // Update URL parameters
    const url = new URL(window.location.href);
    console.log('AdminDashboard.handleNavigate - Current URL before:', url.toString());
    url.searchParams.set('page', page);

    if (page === 'dashboard') {
      const newTab = tab || 'overview';
      setActiveTab(newTab);
      url.searchParams.set('tab', newTab);
      localStorage.setItem('admin_active_tab', newTab);
    } else {
      url.searchParams.delete('tab');
    }

    // Update URL without page reload
    window.history.replaceState({}, '', url.toString());
    console.log('AdminDashboard.handleNavigate - URL after update:', url.toString());

    // Store in localStorage for persistence
    localStorage.setItem('admin_current_page', page);
    console.log('AdminDashboard.handleNavigate - Stored in localStorage:', page);
    console.log('AdminDashboard.handleNavigate - Navigation complete');
  };

  // Provide navigation function to header
  useEffect(() => {
    console.log('AdminDashboard: onNavigateFromHeader prop:', onNavigateFromHeader);
    if (onNavigateFromHeader) {
      console.log('AdminDashboard: Providing handleNavigate function to header');
      onNavigateFromHeader(handleNavigate);
    } else {
      console.warn('AdminDashboard: onNavigateFromHeader prop is not provided');
    }
  }, [onNavigateFromHeader]);

  // Render page content without layout wrapper (for mobile)
  const renderPageContent = () => {
    switch (currentPage) {
      case 'user-management':
        return <UserManagement currentUserId={user.id} />;
      case 'product-management':
        return <ProductManagement />;
      case 'inventory':
        return <InventoryManagement />;
      case 'orders':
        return <OrderManagement />;
      case 'customers':
        return <CustomerManagement />;
      case 'clients':
        return <ClientManagement />;
      case 'resellers':
        return <ResellerManagement />;
      case 'delivery':
        return <DeliveryManagement />;
      case 'branches':
        return <BranchManagement />;
      case 'categories':
        return <CategoryManagement />;
      case 'reports':
        return <ReportsManagement />;
      case 'settings':
        return <SettingsManagement />;
      default:
        return <div>Page not found</div>;
    }
  };

  // Unified responsive layout - no conditional mobile rendering

  // Render different pages based on currentPage
  const renderPage = () => {
    switch (currentPage) {
      case 'user-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="User Management"
            subtitle="Manage admin and store manager accounts"
            breadcrumbs={[{ label: 'System Management', page: 'dashboard' }]}
          >
            <UserManagement currentUserId={user.id} />
          </PageLayout>
        );
      
      case 'product-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Product Management"
            subtitle="Manage products, inventory, and pricing"
            breadcrumbs={[{ label: 'Inventory', page: 'dashboard' }]}
          >
            <ProductManagement />
          </PageLayout>
        );
      
      case 'branch-management':
      case 'branches':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Branch Management"
            subtitle="Manage store branches and locations"
            breadcrumbs={[{ label: 'Operations', page: 'dashboard' }]}
          >
            <BranchManagement currentUserId={user.id} />
          </PageLayout>
        );

      case 'category-management':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Category Management"
            subtitle="Organize products into categories"
            breadcrumbs={[{ label: 'Inventory', page: 'dashboard' }]}
          >
            <CategoryManagement currentUserId={user.id} />
          </PageLayout>
        );
      
      case 'order-management':
      case 'orders':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Order Management"
            subtitle="Process and track customer orders"
            breadcrumbs={[{ label: 'Sales', page: 'dashboard' }]}
          >
            <OrderManagement />
          </PageLayout>
        );
      
      case 'analytics':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Analytics & Reports"
            subtitle="Business insights and performance metrics"
            breadcrumbs={[{ label: 'Reports', page: 'dashboard' }]}
          >
            <AnalyticsPage />
          </PageLayout>
        );

      case 'advanced-analytics':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Advanced Analytics"
            subtitle="Comprehensive business intelligence and insights"
            breadcrumbs={[{ label: 'Analytics', page: 'dashboard' }]}
          >
            <AdvancedAnalyticsDashboard onNavigate={handleNavigate} />
          </PageLayout>
        );

      case 'custom-reports':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Custom Report Builder"
            subtitle="Create and export custom reports with advanced filtering"
            breadcrumbs={[{ label: 'Analytics', page: 'dashboard' }]}
          >
            <CustomReportBuilder onNavigate={handleNavigate} />
          </PageLayout>
        );

      case 'security':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Security Management"
            subtitle="System security and access control"
            breadcrumbs={[{ label: 'System', page: 'dashboard' }]}
          >
            <SecurityPage />
          </PageLayout>
        );

      case 'advanced-security':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Advanced Security Dashboard"
            subtitle="Comprehensive security monitoring and threat detection"
            breadcrumbs={[{ label: 'Security', page: 'dashboard' }]}
          >
            <AdvancedSecurityDashboard onNavigate={handleNavigate} />
          </PageLayout>
        );

      case 'system-health':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="System Health Monitor"
            subtitle="Real-time system performance and health monitoring"
            breadcrumbs={[{ label: 'System', page: 'dashboard' }]}
          >
            <SystemHealthMonitor onNavigate={handleNavigate} />
          </PageLayout>
        );

      case 'audit-logs':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Audit Logs"
            subtitle="Comprehensive audit trail and activity monitoring"
            breadcrumbs={[{ label: 'Security', page: 'dashboard' }]}
          >
            <AuditLogsViewer onNavigate={handleNavigate} />
          </PageLayout>
        );
      

      case 'notifications':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Notification Center"
            subtitle="Manage system notifications and alerts"
            breadcrumbs={[{ label: 'Communication', page: 'dashboard' }]}
          >
            <NotificationsPage />
          </PageLayout>
        );
      
      case 'invoices':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Invoice Management"
            subtitle="Generate and manage invoices"
            breadcrumbs={[{ label: 'Finance', page: 'dashboard' }]}
          >
            <InvoiceManagementPage />
          </PageLayout>
        );
      

      case 'settings':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="System Settings"
            subtitle="Configure system preferences"
            breadcrumbs={[{ label: 'System', page: 'dashboard' }]}
          >
            <SystemSettingsPage />
          </PageLayout>
        );

      case 'profile':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Profile Management"
            subtitle="Manage your account settings"
          >
            <ProfileManagement user={user} onUserUpdate={handleUserUpdate} />
          </PageLayout>
        );

      case 'reviews':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Reviews & Comments"
            subtitle="Manage product reviews and user feedback"
            breadcrumbs={[{ label: 'Management', page: 'dashboard' }]}
          >
            <ReviewsManagementPage />
          </PageLayout>
        );

      case 'promo-codes':
        return (
          <PageLayout
            currentPage={currentPage}
            onNavigate={handleNavigate}
            title="Promo Codes Management"
            subtitle="Create and manage promotional discount codes"
            breadcrumbs={[{ label: 'Dashboard', page: 'dashboard' }]}
          >
            <PromoCodesPage />
          </PageLayout>
        );

      default:
        // Streamlined Admin Dashboard - Core Management Functions Only
        return (
          <div className="space-y-6">
            <div className="bg-gradient-to-r from-teal-600 to-orange-500 rounded-2xl p-6 sm:p-8 text-white">
              <h2 className="text-2xl sm:text-3xl font-bold mb-2">Welcome back, {user.fullName}!</h2>
              <p className="text-base sm:text-lg opacity-90">Admin Dashboard - Core Management</p>
            </div>

            {/* Analytics Overview Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  <TrendingUp className="h-5 w-5 text-teal-600 mr-2" />
                  Live Dashboard Statistics
                </h3>
                <div className="flex items-center space-x-4">
                  {lastUpdated && (
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span>Updated: {lastUpdated.toLocaleTimeString()}</span>
                    </div>
                  )}
                  <button
                    onClick={reloadDashboardStats}
                    disabled={statsLoading}
                    className="flex items-center space-x-1 px-3 py-1 text-sm text-teal-600 hover:text-teal-700 hover:bg-teal-50 rounded-md transition-colors disabled:opacity-50"
                    title="Refresh statistics"
                  >
                    <RefreshCw className={`h-4 w-4 ${statsLoading ? 'animate-spin' : ''}`} />
                    <span>Refresh</span>
                  </button>
                </div>
              </div>

              {statsLoading ? (
                <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  {[...Array(8)].map((_, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4 animate-pulse">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-8 bg-gray-200 rounded"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  {/* Total Products */}
                  <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-blue-600">Total Products</p>
                        <p className="text-2xl font-bold text-blue-900">{dashboardStats?.totalProducts || 0}</p>
                        <p className="text-xs text-blue-600 mt-1">
                          {dashboardStats?.lowStockProducts || 0} low stock
                        </p>
                      </div>
                      <Package className="h-8 w-8 text-blue-600" />
                    </div>
                  </div>

                  {/* Total Users */}
                  <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-green-600">Total Users</p>
                        <p className="text-2xl font-bold text-green-900">{dashboardStats?.totalUsers || 0}</p>
                        <p className="text-xs text-green-600 mt-1">
                          {dashboardStats?.activeUsers || 0} active
                        </p>
                      </div>
                      <Users className="h-8 w-8 text-green-600" />
                    </div>
                  </div>

                  {/* Total Orders */}
                  <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-purple-600">Total Orders</p>
                        <p className="text-2xl font-bold text-purple-900">{dashboardStats?.totalOrders || 0}</p>
                        <p className="text-xs text-purple-600 mt-1">
                          {dashboardStats?.pendingOrders || 0} pending
                        </p>
                      </div>
                      <ShoppingCart className="h-8 w-8 text-purple-600" />
                    </div>
                  </div>

                  {/* Total Revenue */}
                  <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4 border border-orange-200 relative">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-orange-600">Total Revenue</p>
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Live data"></div>
                        </div>
                        <p className="text-2xl font-bold text-orange-900">
                          {statsLoading ? '...' : (dashboardStats?.totalRevenue ? formatCurrency(dashboardStats.totalRevenue) : formatCurrency(0))}
                        </p>
                        <p className="text-xs text-orange-600 mt-1">
                          {dashboardStats?.completedOrders || 0} completed orders
                        </p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-orange-600" />
                    </div>
                  </div>

                  {/* Today's Sales */}
                  <div className="bg-gradient-to-r from-teal-50 to-teal-100 rounded-lg p-4 border border-teal-200 relative">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-teal-600">Today's Sales</p>
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Live data"></div>
                        </div>
                        <p className="text-2xl font-bold text-teal-900">
                          {statsLoading ? '...' : (dashboardStats?.todaysSales ? formatCurrency(dashboardStats.todaysSales) : formatCurrency(0))}
                        </p>
                        <p className="text-xs text-teal-600 mt-1">Real-time today's revenue</p>
                      </div>
                      <BarChart3 className="h-8 w-8 text-teal-600" />
                    </div>
                  </div>

                  {/* This Month's Sales */}
                  <div className="bg-gradient-to-r from-indigo-50 to-indigo-100 rounded-lg p-4 border border-indigo-200 relative">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-indigo-600">This Month</p>
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Live data"></div>
                        </div>
                        <p className="text-2xl font-bold text-indigo-900">
                          {statsLoading ? '...' : (dashboardStats?.thisMonthSales ? formatCurrency(dashboardStats.thisMonthSales) : formatCurrency(0))}
                        </p>
                        <p className="text-xs text-indigo-600 mt-1">Current month revenue</p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-indigo-600" />
                    </div>
                  </div>

                  {/* Total Customers */}
                  <div className="bg-gradient-to-r from-pink-50 to-pink-100 rounded-lg p-4 border border-pink-200 relative">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-pink-600">Total Customers</p>
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Live data"></div>
                        </div>
                        <p className="text-2xl font-bold text-pink-900">
                          {statsLoading ? '...' : (dashboardStats?.totalCustomers || 0)}
                        </p>
                        <p className="text-xs text-pink-600 mt-1">Active clients & resellers</p>
                      </div>
                      <Users className="h-8 w-8 text-pink-600" />
                    </div>
                  </div>

                  {/* Total Branches */}
                  <div className="bg-gradient-to-r from-amber-50 to-amber-100 rounded-lg p-4 border border-amber-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-amber-600">Total Branches</p>
                        <p className="text-2xl font-bold text-amber-900">{dashboardStats?.totalBranches || 0}</p>
                        <p className="text-xs text-amber-600 mt-1">
                          {dashboardStats?.activeBranches || 0} active
                        </p>
                      </div>
                      <MapPin className="h-8 w-8 text-amber-600" />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Core Management Functions Grid */}
            <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* User Management */}
              <div
                onClick={() => handleNavigate('user-management')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-teal-100 rounded-lg">
                    <User className="h-6 w-6 text-teal-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">User Management</h3>
                </div>
                <p className="text-gray-600 text-sm">Manage admin, manager, client, reseller, and delivery accounts</p>
              </div>

              {/* Product Management */}
              <div
                onClick={() => handleNavigate('product-management')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-amber-100 rounded-lg">
                    <Package className="h-6 w-6 text-amber-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Product Management</h3>
                </div>
                <p className="text-gray-600 text-sm">Manage products, inventory, and pricing</p>
              </div>

              {/* Category Management */}
              <div
                onClick={() => handleNavigate('category-management')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <FolderPlus className="h-6 w-6 text-orange-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Category Management</h3>
                </div>
                <p className="text-gray-600 text-sm">Organize products into categories and subcategories</p>
              </div>

              {/* Branch Management */}
              <div
                onClick={() => handleNavigate('branches')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <MapPin className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Branch Management</h3>
                </div>
                <p className="text-gray-600 text-sm">Manage branch locations and operations</p>
              </div>

              {/* Order Management */}
              <div
                onClick={() => handleNavigate('orders')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <ShoppingCart className="h-6 w-6 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Order Management</h3>
                </div>
                <p className="text-gray-600 text-sm">Process and manage customer orders</p>
              </div>

              {/* Analytics */}
              <div
                onClick={() => handleNavigate('analytics')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <BarChart3 className="h-6 w-6 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Analytics</h3>
                </div>
                <p className="text-gray-600 text-sm">View reports and business analytics</p>
              </div>

              {/* Reviews & Comments */}
              <div
                onClick={() => handleNavigate('reviews')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-amber-100 rounded-lg">
                    <MessageSquare className="h-6 w-6 text-amber-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Reviews & Comments</h3>
                </div>
                <p className="text-gray-600 text-sm">Manage product reviews and user feedback</p>
              </div>

              {/* Profile Settings */}
              <div
                onClick={() => handleNavigate('profile')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-indigo-100 rounded-lg">
                    <User className="h-6 w-6 text-indigo-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Profile Settings</h3>
                </div>
                <p className="text-gray-600 text-sm">Manage admin profile and account settings</p>
              </div>

              {/* Promo Codes Management */}
              <div
                onClick={() => handleNavigate('promo-codes')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-amber-100 rounded-lg">
                    <Tag className="h-6 w-6 text-amber-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Promo Codes Management</h3>
                </div>
                <p className="text-gray-600 text-sm">Create and manage promotional discount codes</p>
              </div>

              {/* System Settings */}
              <div
                onClick={() => handleNavigate('settings')}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    <Settings className="h-6 w-6 text-gray-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">System Settings</h3>
                </div>
                <p className="text-gray-600 text-sm">Configure system-wide settings and preferences</p>
              </div>
            </div>

            {/* Quick Actions Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Add New Product */}
                <button
                  onClick={() => handleNavigate('product-management')}
                  className="flex items-center space-x-3 p-4 bg-gradient-to-r from-teal-500 to-teal-600 text-white rounded-lg hover:from-teal-600 hover:to-teal-700 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <Plus className="h-5 w-5" />
                  <span className="font-medium">Add New Product</span>
                </button>

                {/* Create User Account */}
                <button
                  onClick={() => handleNavigate('user-management')}
                  className="flex items-center space-x-3 p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <UserPlus className="h-5 w-5" />
                  <span className="font-medium">Create User Account</span>
                </button>

                {/* Generate Report */}
                <button
                  onClick={() => handleNavigate('analytics')}
                  className="flex items-center space-x-3 p-4 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <FileText className="h-5 w-5" />
                  <span className="font-medium">Generate Report</span>
                </button>

                {/* Check Orders */}
                <button
                  onClick={() => handleNavigate('orders')}
                  className="flex items-center space-x-3 p-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <ClipboardList className="h-5 w-5" />
                  <span className="font-medium">Check Orders</span>
                </button>
              </div>
            </div>
          </div>
        );
    }
  };

  return renderPage();
};

export default AdminDashboard;
