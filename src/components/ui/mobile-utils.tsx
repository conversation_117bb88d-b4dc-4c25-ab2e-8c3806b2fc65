/**
 * Mobile-specific utility components for YalaOffice
 * Provides consistent mobile-first design patterns and touch-friendly interfaces
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { useMobileUtils } from '../../hooks/use-mobile';
import { useResponsiveSpacing } from '../../hooks/useResponsive';

// Mobile-optimized container component
interface MobileContainerProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

export const MobileContainer: React.FC<MobileContainerProps> = ({
  children,
  className,
  padding = 'md',
  maxWidth = 'full'
}) => {
  const { isSmallMobile, isMobile } = useMobileUtils();
  
  const paddingClasses = {
    none: '',
    sm: isSmallMobile ? 'p-2' : 'p-3',
    md: isSmallMobile ? 'p-3' : isMobile ? 'p-4' : 'p-6',
    lg: isSmallMobile ? 'p-4' : isMobile ? 'p-6' : 'p-8'
  };

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    full: 'max-w-full'
  };

  return (
    <div className={cn(
      'w-full mx-auto',
      paddingClasses[padding],
      maxWidthClasses[maxWidth],
      className
    )}>
      {children}
    </div>
  );
};

// Mobile-optimized button component
interface MobileButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  touchOptimized?: boolean;
}

export const MobileButton: React.FC<MobileButtonProps> = ({
  children,
  className,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  touchOptimized = true,
  ...props
}) => {
  const { isSmallMobile, shouldUseTouchUI } = useMobileUtils();

  const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-teal-500 disabled:opacity-50 disabled:pointer-events-none';

  const variantClasses = {
    primary: 'bg-gradient-to-r from-teal-600 to-amber-500 text-white hover:shadow-lg',
    secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200',
    outline: 'border border-gray-300 bg-white text-gray-900 hover:bg-gray-50',
    ghost: 'text-gray-900 hover:bg-gray-100'
  };

  const sizeClasses = {
    sm: isSmallMobile ? 'px-3 py-2 text-xs' : 'px-4 py-2 text-sm',
    md: isSmallMobile ? 'px-4 py-3 text-sm' : 'px-6 py-3 text-base',
    lg: isSmallMobile ? 'px-6 py-4 text-base' : 'px-8 py-4 text-lg'
  };

  const touchClasses = touchOptimized && shouldUseTouchUI ? 'min-h-[44px] min-w-[44px]' : '';

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        touchClasses,
        fullWidth && 'w-full',
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
};

// Mobile-optimized input component
interface MobileInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  touchOptimized?: boolean;
}

export const MobileInput: React.FC<MobileInputProps> = ({
  label,
  error,
  icon,
  className,
  touchOptimized = true,
  ...props
}) => {
  const { isSmallMobile, shouldUseTouchUI } = useMobileUtils();

  const inputClasses = cn(
    'w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-colors',
    icon ? 'pl-10' : 'pl-4',
    'pr-4',
    touchOptimized && shouldUseTouchUI ? 'py-4 text-base' : 'py-3 text-sm',
    error && 'border-red-500 focus:ring-red-500',
    className
  );

  const labelClasses = cn(
    'block font-medium text-gray-700 mb-2',
    isSmallMobile ? 'text-xs' : 'text-sm'
  );

  return (
    <div className="w-full">
      {label && (
        <label className={labelClasses}>
          {label}
        </label>
      )}
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {icon}
          </div>
        )}
        <input
          className={inputClasses}
          {...props}
        />
      </div>
      {error && (
        <p className={cn(
          'text-red-600 mt-1',
          isSmallMobile ? 'text-xs' : 'text-sm'
        )}>
          {error}
        </p>
      )}
    </div>
  );
};

// Mobile-optimized card component
interface MobileCardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

export const MobileCard: React.FC<MobileCardProps> = ({
  children,
  className,
  padding = 'md',
  shadow = 'sm',
  rounded = 'lg'
}) => {
  const { isSmallMobile } = useMobileUtils();

  const paddingClasses = {
    none: '',
    sm: isSmallMobile ? 'p-3' : 'p-4',
    md: isSmallMobile ? 'p-4' : 'p-6',
    lg: isSmallMobile ? 'p-6' : 'p-8'
  };

  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg'
  };

  const roundedClasses = {
    none: '',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl'
  };

  return (
    <div className={cn(
      'bg-white border border-gray-200',
      paddingClasses[padding],
      shadowClasses[shadow],
      roundedClasses[rounded],
      className
    )}>
      {children}
    </div>
  );
};

// Mobile-optimized grid component
interface MobileGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: 1 | 2 | 3 | 4 | 6;
  gap?: 'sm' | 'md' | 'lg';
  responsive?: boolean;
}

export const MobileGrid: React.FC<MobileGridProps> = ({
  children,
  className,
  cols = 2,
  gap = 'md',
  responsive = true
}) => {
  const { isSmallMobile, isMobile } = useMobileUtils();

  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6'
  };

  const getGridCols = () => {
    if (!responsive) return `grid-cols-${cols}`;
    
    if (isSmallMobile) return 'grid-cols-1';
    if (isMobile) return cols > 2 ? 'grid-cols-2' : `grid-cols-${cols}`;
    return `grid-cols-${cols}`;
  };

  return (
    <div className={cn(
      'grid',
      getGridCols(),
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  );
};

// Mobile-optimized spacing component
interface MobileSpacingProps {
  children: React.ReactNode;
  className?: string;
  space?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  direction?: 'vertical' | 'horizontal';
}

export const MobileSpacing: React.FC<MobileSpacingProps> = ({
  children,
  className,
  space = 'md',
  direction = 'vertical'
}) => {
  const { isSmallMobile } = useMobileUtils();

  const spaceClasses = {
    xs: direction === 'vertical' ? 'space-y-1' : 'space-x-1',
    sm: direction === 'vertical' ? (isSmallMobile ? 'space-y-2' : 'space-y-3') : (isSmallMobile ? 'space-x-2' : 'space-x-3'),
    md: direction === 'vertical' ? (isSmallMobile ? 'space-y-3' : 'space-y-4') : (isSmallMobile ? 'space-x-3' : 'space-x-4'),
    lg: direction === 'vertical' ? (isSmallMobile ? 'space-y-4' : 'space-y-6') : (isSmallMobile ? 'space-x-4' : 'space-x-6'),
    xl: direction === 'vertical' ? (isSmallMobile ? 'space-y-6' : 'space-y-8') : (isSmallMobile ? 'space-x-6' : 'space-x-8')
  };

  return (
    <div className={cn(
      'flex',
      direction === 'vertical' ? 'flex-col' : 'flex-row',
      spaceClasses[space],
      className
    )}>
      {children}
    </div>
  );
};
