/**
 * Notification Dropdown Component for YalaOffice
 * Displays notifications with real-time updates and responsive design
 */

import React, { useState, useEffect, useRef } from 'react';
import { Bell, X, Check, CheckCheck, Clock, AlertCircle, Info, ShoppingCart, Package, User } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useMobileUtils } from '../../hooks/use-mobile';
import { getUserNotifications, markNotificationAsRead, markAllNotificationsAsRead } from '../../services/notificationService';
import { Notification } from '../../types/notification';

interface NotificationDropdownProps {
  userId: string;
  className?: string;
}

export const NotificationDropdown: React.FC<NotificationDropdownProps> = ({
  userId,
  className
}) => {
  const { isMobile, isSmallMobile, shouldUseTouchUI } = useMobileUtils();
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Load notifications
  const loadNotifications = async () => {
    try {
      setLoading(true);
      const data = await getUserNotifications(userId);
      setNotifications(data);
      setUnreadCount(data.filter(n => !n.isRead).length);
    } catch (error) {
      console.error('Failed to load notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // Initialize and set up real-time updates
  useEffect(() => {
    loadNotifications();
    
    // Set up periodic refresh for real-time updates
    const interval = setInterval(loadNotifications, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(interval);
  }, [userId]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Handle notification click
  const handleNotificationClick = async (notification: Notification) => {
    if (!notification.isRead) {
      try {
        await markNotificationAsRead(notification.id);
        setNotifications(prev => 
          prev.map(n => n.id === notification.id ? { ...n, isRead: true } : n)
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    }

    // Navigate to action URL if available
    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
    }
  };

  // Mark all as read
  const handleMarkAllAsRead = async () => {
    try {
      await markAllNotificationsAsRead(userId);
      setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
      setUnreadCount(0);
    } catch (error) {
      console.error('Failed to mark all as read:', error);
    }
  };

  // Get notification icon
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'order':
        return <ShoppingCart className="h-4 w-4 text-orange-600" />;
      case 'product':
        return <Package className="h-4 w-4 text-green-600" />;
      case 'user':
        return <User className="h-4 w-4 text-purple-600" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      case 'success':
        return <Check className="h-4 w-4 text-green-600" />;
      default:
        return <Info className="h-4 w-4 text-blue-600" />;
    }
  };

  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  return (
    <div className={cn('relative', className)} ref={dropdownRef}>
      {/* Notification Bell Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'relative rounded-lg hover:bg-gray-100 transition-colors flex items-center justify-center',
          shouldUseTouchUI ? 'p-3 min-w-[44px] min-h-[44px]' : 'p-2',
          isOpen && 'bg-gray-100'
        )}
        aria-label={`Notifications ${unreadCount > 0 ? `(${unreadCount} unread)` : ''}`}
      >
        <Bell className={cn('text-gray-600', isSmallMobile ? 'h-5 w-5' : 'h-6 w-6')} />
        
        {/* Unread count badge */}
        {unreadCount > 0 && (
          <span className={cn(
            'absolute bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium',
            isSmallMobile ? '-top-0 -right-0 h-4 w-4' : '-top-1 -right-1 h-5 w-5'
          )}>
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {/* Dropdown/Modal */}
      {isOpen && (
        <>
          {/* Mobile overlay */}
          {isMobile && (
            <div 
              className="fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setIsOpen(false)}
            />
          )}
          
          {/* Dropdown content */}
          <div className={cn(
            'bg-white rounded-lg shadow-xl border border-gray-200 z-50',
            // Mobile: Full screen modal
            isMobile ? [
              'fixed inset-x-4 top-20 bottom-20 max-h-none',
              'overflow-hidden flex flex-col'
            ] : [
              // Desktop: Positioned dropdown
              'absolute right-0 top-full mt-2 w-96 max-h-96'
            ]
          )}>
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
              <div className="flex items-center space-x-2">
                {unreadCount > 0 && (
                  <button
                    onClick={handleMarkAllAsRead}
                    className="text-sm text-teal-600 hover:text-teal-700 font-medium"
                  >
                    Mark all read
                  </button>
                )}
                {isMobile && (
                  <button
                    onClick={() => setIsOpen(false)}
                    className="p-1 rounded-lg hover:bg-gray-100"
                  >
                    <X className="h-5 w-5 text-gray-500" />
                  </button>
                )}
              </div>
            </div>

            {/* Notifications list */}
            <div className={cn(
              'overflow-y-auto',
              isMobile ? 'flex-1' : 'max-h-80'
            )}>
              {loading ? (
                <div className="p-4 text-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-teal-600 mx-auto"></div>
                  <p className="text-sm text-gray-500 mt-2">Loading notifications...</p>
                </div>
              ) : notifications.length === 0 ? (
                <div className="p-8 text-center">
                  <Bell className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No notifications yet</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-100">
                  {notifications.slice(0, 10).map((notification) => (
                    <button
                      key={notification.id}
                      onClick={() => handleNotificationClick(notification)}
                      className={cn(
                        'w-full p-4 text-left hover:bg-gray-50 transition-colors',
                        !notification.isRead && 'bg-blue-50 border-l-4 border-teal-500'
                      )}
                    >
                      <div className="flex items-start space-x-3">
                        {/* Icon */}
                        <div className="flex-shrink-0 mt-1">
                          {getNotificationIcon(notification.type)}
                        </div>
                        
                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className={cn(
                              'text-sm font-medium truncate',
                              notification.isRead ? 'text-gray-700' : 'text-gray-900'
                            )}>
                              {notification.title}
                            </p>
                            <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
                              {formatTimeAgo(notification.createdAt)}
                            </span>
                          </div>
                          <p className={cn(
                            'text-sm mt-1',
                            notification.isRead ? 'text-gray-500' : 'text-gray-700'
                          )}>
                            {notification.message}
                          </p>
                          
                          {/* Priority indicator */}
                          {notification.priority === 'high' && (
                            <div className="flex items-center mt-2">
                              <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                              <span className="text-xs text-red-600 font-medium">High Priority</span>
                            </div>
                          )}
                        </div>
                        
                        {/* Read indicator */}
                        {!notification.isRead && (
                          <div className="w-2 h-2 bg-teal-500 rounded-full flex-shrink-0 mt-2"></div>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {notifications.length > 0 && (
              <div className="p-4 border-t border-gray-200 bg-gray-50">
                <button
                  onClick={() => {
                    setIsOpen(false);
                    // Navigate to full notifications page
                    window.location.href = '/notifications';
                  }}
                  className="w-full text-center text-sm text-teal-600 hover:text-teal-700 font-medium"
                >
                  View all notifications
                </button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default NotificationDropdown;
