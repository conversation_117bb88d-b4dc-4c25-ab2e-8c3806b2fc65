/**
 * Responsive Table Component for YalaOffice
 * Automatically adapts table layout for different screen sizes
 */

import React, { useState } from 'react';
import { ChevronDown, ChevronUp, MoreVertical, Eye, Edit, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useMobileUtils } from '../../hooks/use-mobile';

export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  className?: string;
  render?: (value: any, row: any) => React.ReactNode;
  mobileHidden?: boolean; // Hide column on mobile
  mobileOnly?: boolean;   // Show only on mobile
}

export interface TableAction {
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  onClick: (row: any) => void;
  className?: string;
  variant?: 'primary' | 'secondary' | 'danger';
}

interface ResponsiveTableProps {
  columns: TableColumn[];
  data: any[];
  actions?: TableAction[];
  loading?: boolean;
  emptyMessage?: string;
  className?: string;
  onSort?: (key: string, direction: 'asc' | 'desc') => void;
  sortKey?: string;
  sortDirection?: 'asc' | 'desc';
  pagination?: {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
    pageSize: number;
    totalItems: number;
  };
}

export const ResponsiveTable: React.FC<ResponsiveTableProps> = ({
  columns,
  data,
  actions = [],
  loading = false,
  emptyMessage = 'No data available',
  className,
  onSort,
  sortKey,
  sortDirection,
  pagination
}) => {
  const { isMobile, isSmallMobile, shouldUseTouchUI } = useMobileUtils();
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  // Filter columns based on screen size
  const visibleColumns = columns.filter(col => {
    if (isMobile && col.mobileHidden) return false;
    if (!isMobile && col.mobileOnly) return false;
    return true;
  });

  // Get primary columns for mobile (first 2-3 most important)
  const primaryColumns = isMobile ? visibleColumns.slice(0, isSmallMobile ? 2 : 3) : visibleColumns;
  const secondaryColumns = isMobile ? visibleColumns.slice(isSmallMobile ? 2 : 3) : [];

  const handleSort = (key: string) => {
    if (!onSort) return;
    const direction = sortKey === key && sortDirection === 'asc' ? 'desc' : 'asc';
    onSort(key, direction);
  };

  const toggleRowExpansion = (rowId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(rowId)) {
      newExpanded.delete(rowId);
    } else {
      newExpanded.add(rowId);
    }
    setExpandedRows(newExpanded);
  };

  const renderCell = (column: TableColumn, row: any) => {
    const value = row[column.key];
    return column.render ? column.render(value, row) : value;
  };

  const renderActions = (row: any) => {
    if (actions.length === 0) return null;

    if (isMobile) {
      // Mobile: Show actions as buttons or dropdown
      return (
        <div className="flex items-center space-x-2">
          {actions.slice(0, 2).map((action, index) => {
            const Icon = action.icon;
            return (
              <button
                key={index}
                onClick={() => action.onClick(row)}
                className={cn(
                  'p-2 rounded-lg transition-colors',
                  shouldUseTouchUI ? 'min-w-[44px] min-h-[44px]' : '',
                  action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' :
                  action.variant === 'primary' ? 'text-teal-600 hover:bg-teal-50' :
                  'text-gray-600 hover:bg-gray-50',
                  action.className
                )}
                title={action.label}
              >
                {Icon && <Icon className="h-4 w-4" />}
              </button>
            );
          })}
          {actions.length > 2 && (
            <button className="p-2 rounded-lg text-gray-600 hover:bg-gray-50">
              <MoreVertical className="h-4 w-4" />
            </button>
          )}
        </div>
      );
    }

    // Desktop: Show all actions
    return (
      <div className="flex items-center space-x-2">
        {actions.map((action, index) => {
          const Icon = action.icon;
          return (
            <button
              key={index}
              onClick={() => action.onClick(row)}
              className={cn(
                'p-2 rounded-lg transition-colors',
                action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' :
                action.variant === 'primary' ? 'text-teal-600 hover:bg-teal-50' :
                'text-gray-600 hover:bg-gray-50',
                action.className
              )}
              title={action.label}
            >
              {Icon && <Icon className="h-4 w-4" />}
            </button>
          );
        })}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-8">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-4 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
        <p className="text-gray-500">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={cn('bg-white rounded-lg border border-gray-200 overflow-hidden', className)}>
      {/* Desktop Table */}
      {!isMobile && (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                {visibleColumns.map((column) => (
                  <th
                    key={column.key}
                    className={cn(
                      'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
                      column.sortable && 'cursor-pointer hover:bg-gray-100',
                      column.className
                    )}
                    style={{ width: column.width }}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center space-x-1">
                      <span>{column.label}</span>
                      {column.sortable && sortKey === column.key && (
                        sortDirection === 'asc' ? 
                          <ChevronUp className="h-4 w-4" /> : 
                          <ChevronDown className="h-4 w-4" />
                      )}
                    </div>
                  </th>
                ))}
                {actions.length > 0 && (
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.map((row, index) => (
                <tr key={row.id || index} className="hover:bg-gray-50">
                  {visibleColumns.map((column) => (
                    <td
                      key={column.key}
                      className={cn('px-6 py-4 whitespace-nowrap text-sm text-gray-900', column.className)}
                    >
                      {renderCell(column, row)}
                    </td>
                  ))}
                  {actions.length > 0 && (
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      {renderActions(row)}
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Mobile Card Layout */}
      {isMobile && (
        <div className="divide-y divide-gray-200">
          {data.map((row, index) => {
            const rowId = row.id || index.toString();
            const isExpanded = expandedRows.has(rowId);
            
            return (
              <div key={rowId} className="p-4">
                {/* Primary row content */}
                <div className="flex items-center justify-between">
                  <div className="flex-1 space-y-2">
                    {primaryColumns.map((column) => (
                      <div key={column.key} className="flex items-center justify-between">
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {column.label}
                        </span>
                        <span className="text-sm text-gray-900 font-medium">
                          {renderCell(column, row)}
                        </span>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    {renderActions(row)}
                    {secondaryColumns.length > 0 && (
                      <button
                        onClick={() => toggleRowExpansion(rowId)}
                        className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-50"
                      >
                        {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                      </button>
                    )}
                  </div>
                </div>

                {/* Expanded content */}
                {isExpanded && secondaryColumns.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-gray-100 space-y-2">
                    {secondaryColumns.map((column) => (
                      <div key={column.key} className="flex items-center justify-between">
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {column.label}
                        </span>
                        <span className="text-sm text-gray-900">
                          {renderCell(column, row)}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}

      {/* Pagination */}
      {pagination && (
        <div className="bg-gray-50 px-4 py-3 border-t border-gray-200 sm:px-6">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {((pagination.currentPage - 1) * pagination.pageSize) + 1} to{' '}
              {Math.min(pagination.currentPage * pagination.pageSize, pagination.totalItems)} of{' '}
              {pagination.totalItems} results
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => pagination.onPageChange(pagination.currentPage - 1)}
                disabled={pagination.currentPage === 1}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <span className="text-sm text-gray-700">
                Page {pagination.currentPage} of {pagination.totalPages}
              </span>
              <button
                onClick={() => pagination.onPageChange(pagination.currentPage + 1)}
                disabled={pagination.currentPage === pagination.totalPages}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResponsiveTable;
