/**
 * YalaOffice Logo Component
 * Displays the favicon with proper sizing and fallback handling
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';

interface YalaLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showText?: boolean;
  textSize?: 'xs' | 'sm' | 'base' | 'lg';
  onClick?: () => void;
}

export const YalaLogo: React.FC<YalaLogoProps> = ({
  size = 'md',
  className,
  showText = false,
  textSize = 'base',
  onClick
}) => {
  const [imageError, setImageError] = useState(false);

  // Size mappings for the logo container
  const sizeClasses = {
    sm: 'w-6 h-6',      // 24px - Small mobile
    md: 'w-8 h-8',      // 32px - Default mobile/desktop
    lg: 'w-10 h-10',    // 40px - Large desktop
    xl: 'w-12 h-12'     // 48px - Extra large
  };

  // Size mappings for the favicon image
  const imageSizeClasses = {
    sm: 'w-4 h-4',      // 16px inside 24px container
    md: 'w-6 h-6',      // 24px inside 32px container
    lg: 'w-7 h-7',      // 28px inside 40px container
    xl: 'w-8 h-8'       // 32px inside 48px container
  };

  // Text size mappings
  const textSizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    base: 'text-base',
    lg: 'text-lg'
  };

  // Fallback Y letter size
  const fallbackTextSize = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const containerClasses = cn(
    'bg-gradient-to-r from-teal-600 to-amber-500 rounded-lg flex items-center justify-center flex-shrink-0',
    sizeClasses[size],
    onClick && 'cursor-pointer hover:from-teal-700 hover:to-amber-600 transition-colors',
    className
  );

  return (
    <div className="flex items-center space-x-2">
      {/* Logo Container */}
      <div className={containerClasses} onClick={onClick}>
        {!imageError ? (
          <img
            src="/favicon.ico"
            alt="YalaOffice Logo"
            className={cn(
              'object-contain',
              imageSizeClasses[size]
            )}
            onError={handleImageError}
            onLoad={() => setImageError(false)}
          />
        ) : (
          // Fallback to Y letter if favicon fails to load
          <span className={cn(
            'text-white font-bold',
            fallbackTextSize[size]
          )}>
            Y
          </span>
        )}
      </div>

      {/* Optional Text */}
      {showText && (
        <div className="flex flex-col">
          <span className={cn(
            'font-bold text-gray-900 dark:text-white',
            textSizeClasses[textSize]
          )}>
            YalaOffice
          </span>
          {(size === 'lg' || size === 'xl') && (
            <span className="text-xs text-gray-600 dark:text-gray-300">
              Supply Management
            </span>
          )}
        </div>
      )}
    </div>
  );
};

// Preset configurations for common use cases
export const MobileLogo: React.FC<Omit<YalaLogoProps, 'size'>> = (props) => (
  <YalaLogo size="sm" {...props} />
);

export const DesktopLogo: React.FC<Omit<YalaLogoProps, 'size'>> = (props) => (
  <YalaLogo size="md" {...props} />
);

export const HeaderLogo: React.FC<Omit<YalaLogoProps, 'size' | 'showText'>> = (props) => (
  <YalaLogo size="md" showText={true} textSize="base" {...props} />
);

export const SidebarLogo: React.FC<Omit<YalaLogoProps, 'size' | 'showText'>> = (props) => (
  <YalaLogo size="lg" showText={true} textSize="lg" {...props} />
);

export default YalaLogo;
