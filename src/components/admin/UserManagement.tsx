import { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, User<PERSON>heck, UserX, Shield, Users, Eye, EyeOff, Download, FileSpreadsheet, Key, Mail, Phone, MapPin, Building, AlertCircle, RefreshCw, UserPlus, X } from 'lucide-react';
import { User, getUserStats, CreateUserData, getUsers, createUser, updateUser, deleteUser } from '../../services/userManagementService';
import { useSyncedUsers, useUserOperations } from '../../hooks/useSyncedData';
import { exportUsers } from '../../utils/exportUtils';
import { supabase } from '../../integrations/supabase/client';
import { liveDataService } from '../../services/liveDataService';
import { realTimeService } from '../../services/realTimeService';
import { MOROCCAN_CITIES } from '../../constants/cities';

interface UserManagementProps {
  currentUserId: string;
}

// Helper functions for user type display
const getUserTypeLabel = (userType: string | undefined | null): string => {
  if (!userType) {
    return 'Unknown';
  }

  const labels: Record<string, string> = {
    'admin': 'Admin',
    'manager': 'Manager',
    'client': 'Client',
    'reseller': 'Reseller',
    'delivery_person': 'Delivery Person',
    'delivery': 'Delivery Person'
  };

  return labels[userType] || (userType.charAt(0).toUpperCase() + userType.slice(1));
};

const getUserTypeColor = (userType: string | undefined | null): string => {
  if (!userType) {
    return 'bg-gray-100 text-gray-800';
  }

  const colors: Record<string, string> = {
    'admin': 'bg-red-100 text-red-800',
    'manager': 'bg-purple-100 text-purple-800',
    'client': 'bg-blue-100 text-blue-800',
    'reseller': 'bg-green-100 text-green-800',
    'delivery_person': 'bg-orange-100 text-orange-800',
    'delivery': 'bg-orange-100 text-orange-800'
  };

  return colors[userType] || 'bg-gray-100 text-gray-800';
};

const getStatusLabel = (status: string): string => {
  const labels: Record<string, string> = {
    'active': 'Active',
    'inactive': 'Inactive',
    'pending': 'Pending',
    'suspended': 'Suspended'
  };

  return labels[status] || status.charAt(0).toUpperCase() + status.slice(1);
};

const getStatusColor = (status: string): string => {
  const colors: Record<string, string> = {
    'active': 'bg-green-100 text-green-800',
    'inactive': 'bg-gray-100 text-gray-800',
    'pending': 'bg-yellow-100 text-yellow-800',
    'suspended': 'bg-red-100 text-red-800'
  };

  return colors[status] || 'bg-gray-100 text-gray-800';
};

const UserManagement = ({ currentUserId }: UserManagementProps) => {
  // Use synchronized data hooks
  const { data: users, loading, error, refetch } = useSyncedUsers();
  const { createUser: createUserSync, updateUser: updateUserSync, deleteUser: deleteUserSync, loading: operationLoading, error: operationError } = useUserOperations();

  // Debug logging
  console.log('UserManagement Debug:', {
    users,
    usersLength: users?.length,
    loading,
    error,
    currentUserId
  });

  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUserType, setSelectedUserType] = useState<'all' | 'admin' | 'manager' | 'client' | 'reseller' | 'delivery_person'>('all');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'active' | 'inactive' | 'pending' | 'suspended'>('all');
  const [selectedCity, setSelectedCity] = useState<string>('all');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUserForm, setShowUserForm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [stats, setStats] = useState({
    total: 0,
    admins: 0,
    managers: 0,
    clients: 0,
    resellers: 0,
    delivery: 0,
    active: 0,
    inactive: 0,
    recentlyCreated: 0
  });

  // Enhanced state for new features
  const [showPasswordModal, setShowPasswordModal] = useState<User | null>(null);
  const [showEmailModal, setShowEmailModal] = useState<User | null>(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(true);
  const [passwordForm, setPasswordForm] = useState({ newPassword: '', confirmPassword: '' });
  const [emailForm, setEmailForm] = useState({ newEmail: '', confirmEmail: '' });



  // User details modal state
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [selectedUserForDetails, setSelectedUserForDetails] = useState<User | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [usersPerPage] = useState(20);
  const [totalUsers, setTotalUsers] = useState(0);

  // Sorting state
  const [sortField, setSortField] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Real-time Supabase integration and statistics loading
  useEffect(() => {
    const initializeUserManagement = async () => {
      // First, ensure current user exists in users table
      await liveDataService.ensureCurrentUserInUsersTable();

      // Then load stats
      loadStats();
    };

    initializeUserManagement();

    // Set up real-time subscription for statistics updates
    const subscription = supabase
      .channel('users_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'users' }, (payload) => {
        console.log('UserManagement: Real-time user change detected:', payload);
        loadStats();
        refetch(); // Refresh users data from the hook
      })
      .subscribe();

    // Also listen to real-time service events for immediate updates
    const handleUserStatusChanged = (eventData: any) => {
      console.log('UserManagement: Real-time service user status changed:', eventData);
      refetch();
      loadStats();
    };

    const handleUserCreated = (eventData: any) => {
      console.log('UserManagement: Real-time service user created:', eventData);
      refetch();
      loadStats();
    };

    const handleUserDeleted = (eventData: any) => {
      console.log('UserManagement: Real-time service user deleted:', eventData);
      refetch();
      loadStats();
    };

    // Subscribe to real-time service events
    const unsubscribeStatusChanged = realTimeService.subscribe('user-status-changed', handleUserStatusChanged);
    const unsubscribeUserCreated = realTimeService.subscribe('user-created', handleUserCreated);
    const unsubscribeUserDeleted = realTimeService.subscribe('user-deleted', handleUserDeleted);

    return () => {
      supabase.removeChannel(subscription);
      unsubscribeStatusChanged();
      unsubscribeUserCreated();
      unsubscribeUserDeleted();
    };
  }, [refetch]);



  // Update stats when users change
  useEffect(() => {
    if (users && users.length >= 0) {
      loadStats();
    }
  }, [users]);

  // Update filtered users when users or filters change
  useEffect(() => {
    if (!users) return;

    console.log('UserManagement: Users data received:', users);
    console.log('UserManagement: Sample user data:', users?.[0]);

    let filtered = [...users];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(user =>
        (user.full_name || user.fullName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.email || '').toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply user type filter
    if (selectedUserType !== 'all') {
      filtered = filtered.filter(user =>
        (user.user_type || user.userType) === selectedUserType
      );
    }

    // Apply status filter
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(user => {
        const userStatus = user.status || (user.is_active || user.isActive ? 'active' : 'inactive');
        return userStatus === selectedStatus;
      });
    }

    // Apply city filter
    if (selectedCity !== 'all') {
      filtered = filtered.filter(user =>
        (user.city || '') === selectedCity
      );
    }

    // Apply sorting
    if (sortField) {
      filtered.sort((a, b) => {
        let aValue: any = '';
        let bValue: any = '';

        switch (sortField) {
          case 'name':
            aValue = (a.full_name || a.fullName || '').toLowerCase();
            bValue = (b.full_name || b.fullName || '').toLowerCase();
            break;
          case 'email':
            aValue = (a.email || '').toLowerCase();
            bValue = (b.email || '').toLowerCase();
            break;
          case 'userType':
            aValue = (a.user_type || a.userType || '').toLowerCase();
            bValue = (b.user_type || b.userType || '').toLowerCase();
            break;
          case 'city':
            aValue = (a.city || '').toLowerCase();
            bValue = (b.city || '').toLowerCase();
            break;
          case 'status':
            aValue = (a.status || (a.is_active || a.isActive ? 'active' : 'inactive')).toLowerCase();
            bValue = (b.status || (b.is_active || b.isActive ? 'active' : 'inactive')).toLowerCase();
            break;
          case 'createdAt':
            aValue = new Date(a.created_at || a.createdAt || 0).getTime();
            bValue = new Date(b.created_at || b.createdAt || 0).getTime();
            break;
          default:
            return 0;
        }

        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
      });
    }

    setFilteredUsers(filtered as any);
  }, [users, searchTerm, selectedUserType, selectedStatus, selectedCity, sortField, sortDirection]);

  // Update total users count when filtered users change
  useEffect(() => {
    setTotalUsers(filteredUsers.length);
  }, [filteredUsers]);

  // Calculate pagination
  const indexOfLastUser = currentPage * usersPerPage;
  const indexOfFirstUser = indexOfLastUser - usersPerPage;
  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);
  const totalPages = Math.ceil(totalUsers / usersPerPage);

  // Pagination handlers
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handleGoToPage = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  // Sorting handlers
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
    setCurrentPage(1); // Reset to first page when sorting
  };

  const getSortIcon = (field: string) => {
    if (sortField !== field) {
      return null;
    }
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  // Enhanced statistics calculation from real Supabase data
  const loadStats = async () => {
    try {
      setAnalyticsLoading(true);
      console.log('UserManagement: Loading user statistics...');

      // Use the new liveDataService method
      const stats = await liveDataService.getUserStatistics();
      console.log('UserManagement: Received statistics:', stats);

      setStats(stats);
    } catch (error) {
      console.error('Error loading enhanced user stats:', error);
      // Set default stats on error
      setStats({
        total: 0,
        admins: 0,
        managers: 0,
        clients: 0,
        resellers: 0,
        delivery: 0,
        active: 0,
        inactive: 0,
        recentlyCreated: 0
      });
    } finally {
      setAnalyticsLoading(false);
    }
  };

  // Manual refresh function for the refresh button
  const handleManualRefresh = async () => {
    try {
      console.log('UserManagement: Manual refresh triggered');
      setAnalyticsLoading(true);

      // Store current state to maintain after refresh
      const currentState = {
        page: currentPage,
        search: searchTerm,
        userType: selectedUserType,
        status: selectedStatus,
        city: selectedCity,
        sortField: sortField,
        sortDirection: sortDirection
      };

      // Refresh both users data and statistics in parallel
      await Promise.all([
        refetch(), // Refresh users data from the hook
        loadStats() // Refresh statistics
      ]);

      // Restore state after refresh (filters and sort are maintained automatically via useEffect)
      // Only reset page if it would be out of bounds
      setTimeout(() => {
        if (currentPage > Math.ceil(totalUsers / usersPerPage)) {
          setCurrentPage(1);
        }
      }, 100);

      console.log('UserManagement: Manual refresh completed, state maintained:', currentState);
    } catch (error) {
      console.error('Error during manual refresh:', error);
      alert('Error refreshing data. Please try again.');
    } finally {
      setAnalyticsLoading(false);
    }
  };

  const filterUsers = () => {
    let filtered = users;

    if (searchTerm) {
      filtered = filtered.filter(user =>
        (user.fullName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.department || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.branch || '').toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedUserType !== 'all') {
      filtered = filtered.filter(user => (user.userType || '').toLowerCase() === selectedUserType.toLowerCase());
    }

    setFilteredUsers(filtered);
  };

  // Status management functions
  const handleStatusUpdate = async (userId: string, updates: { is_active?: boolean; is_verified?: boolean; status?: string }) => {
    try {
      setStatusUpdateLoading(userId);
      console.log('UserManagement: Updating user status:', { userId, updates });

      // Use the more robust userManagementService for status updates
      if (updates.is_active !== undefined) {
        const result = await toggleUserStatus(userId, currentUserId);

        if (result) {
          // Refresh the users data
          refetch();

          // Update local state immediately for better UX
          setFilteredUsers(prev => prev.map(user =>
            user.id === userId
              ? { ...user, isActive: result.isActive }
              : user
          ));

          alert(`User ${result.isActive ? 'activated' : 'deactivated'} successfully!`);
        } else {
          alert('Failed to update user status');
        }
      } else {
        // For other status updates, use the liveDataService
        const result = await liveDataService.updateUserStatus(userId, updates);

        if (result.success) {
          // Refresh the users data
          refetch();

          // Update local state immediately for better UX
          setFilteredUsers(prev => prev.map(user =>
            user.id === userId
              ? {
                  ...user,
                  ...(updates.is_verified !== undefined && { isVerified: updates.is_verified })
                }
              : user
          ));

          alert('User status updated successfully!');
        } else {
          console.error('Status update failed:', result.error);
          alert(result.error || 'Failed to update user status');
        }
      }
    } catch (error) {
      console.error('Error updating user status:', error);
      alert('Error updating user status: ' + (error as Error).message);
    } finally {
      setStatusUpdateLoading(null);
      setShowStatusDropdown(null);
    }
  };







  const handleCreateUser = async (userData: CreateUserData) => {
    try {
      const result = await createUserSync({
        email: userData.email,
        password: userData.password || 'TempPassword2024!',
        full_name: userData.fullName,
        user_type: userData.userType,
        phone: userData.phone,
        city: userData.city || 'Tetouan',
        company_name: userData.companyName,
        company_address: userData.companyAddress
      });

      if (result.success) {
        setShowUserForm(false);
        setSelectedUser(null);

        // Immediately refresh data and statistics
        await Promise.all([
          refetch(), // Refresh users data
          loadStats() // Refresh statistics
        ]);

        alert('User created successfully!');
      } else {
        alert(result.error || 'Failed to create user');
      }
    } catch (error) {
      console.error('Error creating user:', error);
      alert('Error creating user');
    }
  };

  // Password management function
  const handleChangePassword = async (userId: string, newPassword: string) => {
    try {
      // Get user email for password reset
      const user = users.find(u => u.id === userId);
      if (!user) {
        alert('User not found');
        return;
      }

      // Send password reset email instead of direct password change
      const { error } = await supabase.auth.resetPasswordForEmail(
        user.email,
        {
          redirectTo: `${window.location.origin}/reset-password`
        }
      );

      if (error) throw error;

      alert('Password reset email sent to user. They will need to check their email to set a new password.');
      setShowPasswordModal(null);
      setPasswordForm({ newPassword: '', confirmPassword: '' });
    } catch (error) {
      console.error('Error sending password reset:', error);
      alert('Error sending password reset email: ' + (error as Error).message);
    }
  };

  // Email management function
  const handleChangeEmail = async (userId: string, newEmail: string) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ email: newEmail })
        .eq('id', userId);

      if (error) throw error;

      alert('Email updated successfully!');
      setShowEmailModal(null);
      setEmailForm({ newEmail: '', confirmEmail: '' });
      refetch(); // Refresh data
    } catch (error) {
      console.error('Error updating email:', error);
      alert('Error updating email');
    }
  };

  // Account status toggle function
  const handleToggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: !currentStatus })
        .eq('id', userId);

      if (error) throw error;

      alert(`User ${!currentStatus ? 'activated' : 'deactivated'} successfully!`);
      refetch(); // Refresh data
    } catch (error) {
      console.error('Error toggling user status:', error);
      alert('Error updating user status');
    }
  };

  // User type change function
  const handleChangeUserType = async (userId: string, newUserType: string) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ user_type: newUserType })
        .eq('id', userId);

      if (error) throw error;

      alert('User type updated successfully!');
      refetch(); // Refresh data
    } catch (error) {
      console.error('Error updating user type:', error);
      alert('Error updating user type');
    }
  };

  // Export functions
  const handleExportExcel = () => {
    if (filteredUsers.length === 0) {
      alert('No data to export');
      return;
    }
    exportUsers(filteredUsers, 'excel');
  };

  const handleExportCSV = () => {
    if (filteredUsers.length === 0) {
      alert('No data to export');
      return;
    }
    exportUsers(filteredUsers, 'csv');
  };

  const handleUpdateUser = async (userData: any) => {
    if (!selectedUser) return;

    try {
      // Prepare update data
      const updateData: any = {
        full_name: userData.fullName,
        email: userData.email,
        user_type: userData.userType,
        phone: userData.phone,
        city: userData.city,
        company_name: userData.companyName,
        company_address: userData.companyAddress,
        company_phone: userData.companyPhone,
        company_email: userData.companyEmail,
        tax_id: userData.taxId,
        legal_form: userData.legalForm,
        ice_number: userData.iceNumber,
        is_company: userData.isCompany
      };

      // Handle password update if provided
      if (userData.password && userData.password.trim() !== '') {
        try {
          // For admin password reset, we'll use a different approach
          // Since client-side admin functions are restricted, we'll send a password reset email
          const { error: resetError } = await supabase.auth.resetPasswordForEmail(
            userData.email,
            {
              redirectTo: `${window.location.origin}/reset-password`
            }
          );

          if (resetError) {
            console.error('Error sending password reset:', resetError);
            alert('Error sending password reset email: ' + resetError.message);
            return;
          }

          alert('Password reset email sent to user. They will need to check their email to set a new password.');
        } catch (authError) {
          console.error('Error with password reset:', authError);
          alert('Error with password reset: ' + (authError as Error).message);
          return;
        }
      }

      // Update user data in database
      const result = await updateUserSync(selectedUser.id, updateData);

      if (result.success) {
        // Refresh the data and statistics to show changes immediately
        await Promise.all([
          refetch(), // Refresh users data
          loadStats() // Refresh statistics
        ]);

        setShowUserForm(false);
        setSelectedUser(null);
        alert('User updated successfully!');
      } else {
        alert(result.error || 'Failed to update user');
      }
    } catch (error) {
      console.error('Error updating user:', error);
      alert('Error updating user: ' + (error as Error).message);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    try {
      const result = await deleteUserSync(userId);

      if (result.success) {
        setShowDeleteConfirm(null);

        // Immediately refresh data and statistics
        await Promise.all([
          refetch(), // Refresh users data
          loadStats() // Refresh statistics
        ]);

        alert('User deleted successfully!');
      } else {
        alert(result.error || 'Failed to delete user');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      alert('Error deleting user: ' + (error as Error).message);
    }
  };



  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
        <span className="ml-3 text-gray-600">Loading users...</span>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-500 mb-2">Error loading users</div>
          <div className="text-sm text-gray-600 mb-4">{error}</div>
          <button
            onClick={refetch}
            className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Ensure users is an array
  const safeUsers = Array.isArray(users) ? users : [];
  const safeFilteredUsers = Array.isArray(filteredUsers) ? filteredUsers : [];
  const safeCurrentUsers = Array.isArray(currentUsers) ? currentUsers : [];

  return (
    <div className="space-y-6">
      {/* Header - Mobile Responsive */}
      <div className="space-y-4">
        {/* Title and Subtitle */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900">User Management</h2>
          <p className="text-gray-600">Manage all user accounts (Admin, Manager, Client, Reseller, Delivery)</p>
        </div>

        {/* Action Buttons - Responsive Layout */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-end gap-3">
          {/* Export Buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleExportExcel}
              className="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 flex-1 sm:flex-none justify-center"
              title="Export to Excel"
            >
              <FileSpreadsheet className="h-4 w-4" />
              <span>Excel</span>
            </button>
            <button
              onClick={handleExportCSV}
              className="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 flex-1 sm:flex-none justify-center"
              title="Export to CSV"
            >
              <Download className="h-4 w-4" />
              <span>CSV</span>
            </button>
          </div>

          {/* Refresh and Add User Buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleManualRefresh}
              className="bg-gray-600 text-white px-3 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2 flex-1 sm:flex-none justify-center"
              title="Refresh Data & Statistics"
              disabled={analyticsLoading}
            >
              <RefreshCw className={`h-4 w-4 ${analyticsLoading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>

            <button
              onClick={() => {
                setSelectedUser(null);
                setShowUserForm(true);
              }}
              className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors flex items-center space-x-2 flex-1 sm:flex-none justify-center"
            >
              <Plus className="h-5 w-5" />
              <span>Add User</span>
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      {analyticsLoading ? (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          {[...Array(7)].map((_, index) => (
            <div key={index} className="bg-white rounded-lg border border-gray-200 p-4 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          {/* Total Users */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Users</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>

          {/* Admins */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-red-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Admins</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.admins}</p>
              </div>
            </div>
          </div>

          {/* Managers */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Managers</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.managers}</p>
              </div>
            </div>
          </div>

          {/* Clients */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <Building className="h-8 w-8 text-teal-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Clients</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.clients}</p>
              </div>
            </div>
          </div>

          {/* Resellers */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-amber-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Resellers</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.resellers}</p>
              </div>
            </div>
          </div>

          {/* Active Users */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <UserCheck className="h-8 w-8 text-green-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Active</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.active}</p>
              </div>
            </div>
          </div>

          {/* Recent Users */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <Plus className="h-8 w-8 text-orange-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">New (7d)</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.recentlyCreated}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <select
            value={selectedUserType}
            onChange={(e) => setSelectedUserType(e.target.value as 'all' | 'admin' | 'manager' | 'client' | 'reseller' | 'delivery_person')}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="all">All User Types</option>
            <option value="admin">Admins</option>
            <option value="manager">Managers</option>
            <option value="client">Clients</option>
            <option value="reseller">Resellers</option>
            <option value="delivery_person">Delivery Personnel</option>
          </select>

          {/* Status Filter */}
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value as 'all' | 'active' | 'inactive' | 'pending' | 'suspended')}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="pending">Pending</option>
            <option value="suspended">Suspended</option>
          </select>

          {/* City Filter */}
          <select
            value={selectedCity}
            onChange={(e) => setSelectedCity(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="all">All Cities</option>
            {MOROCCAN_CITIES.map(city => (
              <option key={city} value={city}>{city}</option>
            ))}
          </select>

          {/* Clear Filters Button */}
          {(selectedUserType !== 'all' || selectedStatus !== 'all' || selectedCity !== 'all' || searchTerm) && (
            <button
              onClick={() => {
                setSelectedUserType('all');
                setSelectedStatus('all');
                setSelectedCity('all');
                setSearchTerm('');
                setCurrentPage(1);
              }}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors flex items-center space-x-2"
            >
              <X className="h-4 w-4" />
              <span>Clear Filters</span>
            </button>
          )}
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center space-x-1">
                    <span>User</span>
                    <span className="text-gray-400">{getSortIcon('name')}</span>
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
                  onClick={() => handleSort('userType')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Type</span>
                    <span className="text-gray-400">{getSortIcon('userType')}</span>
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Phone Number
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
                  onClick={() => handleSort('status')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Status</span>
                    <span className="text-gray-400">{getSortIcon('status')}</span>
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
                  onClick={() => handleSort('city')}
                >
                  <div className="flex items-center space-x-1">
                    <span>City</span>
                    <span className="text-gray-400">{getSortIcon('city')}</span>
                  </div>
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {safeCurrentUsers.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-8 text-center text-gray-500">
                    {searchTerm || selectedUserType !== 'all' || selectedStatus !== 'all' || selectedCity !== 'all' ? 'No users match your filters' : 'No users found'}
                  </td>
                </tr>
              ) : (
                safeCurrentUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <img
                        src={user.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent((user as any).full_name || user.fullName || 'User')}&background=f97316&color=fff`}
                        alt={(user as any).full_name || user.fullName || 'User'}
                        className="h-10 w-10 rounded-full object-cover"
                      />
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{(user as any).full_name || user.fullName}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getUserTypeColor((user as any).user_type || user.userType)}`}>
                      {getUserTypeLabel((user as any).user_type || user.userType)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div>{user.phone || 'N/A'}</div>
                      {(user as any).branch && <div className="text-xs text-gray-500">{(user as any).branch}</div>}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-col space-y-1">
                      {/* User Status */}
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor((user as any).status || ((user as any).is_active || user.isActive ? 'active' : 'inactive'))}`}>
                        {getStatusLabel((user as any).status || ((user as any).is_active || user.isActive ? 'active' : 'inactive'))}
                      </span>


                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                      <span>{user.city || 'Not specified'}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      {/* View Details Button */}
                      <button
                        onClick={() => {
                          setSelectedUserForDetails(user);
                          setShowUserDetails(true);
                        }}
                        className="text-blue-600 hover:text-blue-900"
                        title="View user details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>

                      {/* Edit Button */}
                      <button
                        onClick={() => {
                          setSelectedUser(user);
                          setShowUserForm(true);
                        }}
                        className="text-orange-600 hover:text-orange-900"
                        title="Edit user"
                      >
                        <Edit className="h-4 w-4" />
                      </button>



                      {/* Delete Button */}
                      <button
                        onClick={() => setShowDeleteConfirm(user.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete user"
                        disabled={user.id === currentUserId}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination Controls */}
      {totalUsers > usersPerPage && (
        <div className="bg-white px-6 py-4 border-t border-gray-200 rounded-b-lg">
          <div className="flex items-center justify-between">
            {/* Pagination Info */}
            <div className="text-sm text-gray-700">
              Showing {indexOfFirstUser + 1} to {Math.min(indexOfLastUser, totalUsers)} of {totalUsers} users
            </div>

            {/* Pagination Controls */}
            <div className="flex items-center space-x-2">
              {/* Previous Button */}
              <button
                onClick={handlePreviousPage}
                disabled={currentPage === 1}
                className={`px-3 py-2 text-sm font-medium rounded-md ${
                  currentPage === 1
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                Previous
              </button>

              {/* Page Numbers */}
              <div className="flex space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNumber: number;
                  if (totalPages <= 5) {
                    pageNumber = i + 1;
                  } else if (currentPage <= 3) {
                    pageNumber = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNumber = totalPages - 4 + i;
                  } else {
                    pageNumber = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNumber}
                      onClick={() => handlePageChange(pageNumber)}
                      className={`px-3 py-2 text-sm font-medium rounded-md ${
                        currentPage === pageNumber
                          ? 'bg-orange-500 text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      {pageNumber}
                    </button>
                  );
                })}
              </div>

              {/* Next Button */}
              <button
                onClick={handleNextPage}
                disabled={currentPage === totalPages}
                className={`px-3 py-2 text-sm font-medium rounded-md ${
                  currentPage === totalPages
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                Next
              </button>

              {/* Go to Page Input */}
              <div className="flex items-center space-x-2 ml-4">
                <span className="text-sm text-gray-700">Go to:</span>
                <input
                  type="number"
                  min="1"
                  max={totalPages}
                  value={currentPage}
                  onChange={(e) => {
                    const page = parseInt(e.target.value);
                    if (page >= 1 && page <= totalPages) {
                      handleGoToPage(page);
                    }
                  }}
                  className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* User Form Modal */}
      {showUserForm && (
        <UserForm
          user={selectedUser}
          onSubmit={selectedUser ? handleUpdateUser : handleCreateUser}
          onCancel={() => {
            setShowUserForm(false);
            setSelectedUser(null);
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Delete User</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this user? This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => handleDeleteUser(showDeleteConfirm)}
                className="flex-1 bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600 transition-colors"
              >
                Delete
              </button>
              <button
                onClick={() => setShowDeleteConfirm(null)}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* User Details Modal */}
      {showUserDetails && selectedUserForDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">User Details</h3>
              <button
                onClick={() => {
                  setShowUserDetails(false);
                  setSelectedUserForDetails(null);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Personal Information */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  Personal Information
                </h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Full Name</label>
                    <p className="text-gray-900">{selectedUserForDetails.fullName || 'Not specified'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Email</label>
                    <p className="text-gray-900">{selectedUserForDetails.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Phone</label>
                    <p className="text-gray-900">{selectedUserForDetails.phone || 'Not specified'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">City</label>
                    <p className="text-gray-900">{selectedUserForDetails.city || 'Not specified'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">User Type</label>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getUserTypeColor(selectedUserForDetails.userType)}`}>
                      {getUserTypeLabel(selectedUserForDetails.userType)}
                    </span>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Status</label>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedUserForDetails.status || (selectedUserForDetails.isActive ? 'active' : 'inactive'))}`}>
                      {getStatusLabel(selectedUserForDetails.status || (selectedUserForDetails.isActive ? 'active' : 'inactive'))}
                    </span>
                  </div>
                </div>
              </div>

              {/* Company Information */}
              {selectedUserForDetails.isCompany && (
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                    Company Information
                  </h4>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Company Name</label>
                      <p className="text-gray-900">{selectedUserForDetails.companyName || 'Not specified'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">ICE Number</label>
                      <p className="text-gray-900">{selectedUserForDetails.iceNumber || 'Not specified'}</p>
                    </div>

                  </div>
                </div>
              )}

              {/* System Information */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  System Information
                </h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">User ID</label>
                    <p className="text-gray-900 font-mono text-sm">{selectedUserForDetails.id}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Created Date</label>
                    <p className="text-gray-900">{new Date(selectedUserForDetails.createdAt).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Last Updated</label>
                    <p className="text-gray-900">{new Date(selectedUserForDetails.updatedAt).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Last Login</label>
                    <p className="text-gray-900">
                      {selectedUserForDetails.lastLogin
                        ? new Date(selectedUserForDetails.lastLogin).toLocaleDateString()
                        : 'Never'
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-6 pt-4 border-t border-gray-200">
              <button
                onClick={() => {
                  setShowUserDetails(false);
                  setSelectedUserForDetails(null);
                }}
                className="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Enhanced User Form Component with conditional fields
const UserForm = ({ user, onSubmit, onCancel }: any) => {
  const [formData, setFormData] = useState({
    email: user?.email || '',
    fullName: user?.fullName || '',
    userType: user?.userType || 'manager',
    password: '',
    phone: user?.phone || '',
    city: user?.city || 'Tetouan',
    // Company fields (for clients/resellers)
    companyName: user?.companyName || '',
    companyAddress: user?.companyAddress || '',
    businessLicense: user?.businessLicense || '',
    // Delivery fields (for delivery personnel)
    vehicleType: user?.vehicleType || '',
    licenseNumber: user?.licenseNumber || '',
    coverageArea: user?.coverageArea || '',
    // Profile picture
    profilePicture: null as File | null,
    permissions: user?.permissions || []
  });

  const [showPassword, setShowPassword] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!user && !formData.password) {
      alert('Password is required for new users');
      return;
    }
    onSubmit(formData);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, profilePicture: file }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {user ? 'Edit User' : 'Add User'}
        </h3>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="border-b border-gray-200 pb-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Basic Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.fullName}
                  onChange={(e) => setFormData(prev => ({ ...prev, fullName: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  User Type <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.userType}
                  onChange={(e) => setFormData(prev => ({ ...prev, userType: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  required
                >
                  <option value="admin">Admin</option>
                  <option value="manager">Manager</option>
                  <option value="client">Client</option>
                  <option value="reseller">Reseller</option>
                  <option value="delivery_person">Delivery Personnel</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="+212 6XX XXX XXX"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
                <select
                  value={formData.city}
                  onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  {MOROCCAN_CITIES.map(city => (
                    <option key={city} value={city}>{city}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Password Section */}
          <div className="border-b border-gray-200 pb-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Security</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {user ? 'Reset Password (leave blank to keep current)' : 'Password'}
                  {!user && <span className="text-red-500">*</span>}
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    required={!user}
                    minLength={8}
                    placeholder={user ? "Enter any value to send password reset email" : "Minimum 8 characters"}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
                {user && (
                  <p className="text-xs text-amber-600 mt-1">
                    ⚠️ Entering any value here will send a password reset email to the user
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Profile Picture</label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">Upload a profile picture (optional)</p>
              </div>
            </div>
          </div>

          {/* Conditional Fields for Clients and Resellers */}
          {(formData.userType === 'client' || formData.userType === 'reseller') && (
            <div className="border-b border-gray-200 pb-4">
              <h4 className="text-md font-medium text-gray-900 mb-3">Company Information</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Company Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.companyName}
                    onChange={(e) => setFormData(prev => ({ ...prev, companyName: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Business License</label>
                  <input
                    type="text"
                    value={formData.businessLicense}
                    onChange={(e) => setFormData(prev => ({ ...prev, businessLicense: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="License number or registration ID"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Company Address</label>
                  <textarea
                    value={formData.companyAddress}
                    onChange={(e) => setFormData(prev => ({ ...prev, companyAddress: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    rows={3}
                    placeholder="Enter complete company address"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Conditional Fields for Delivery Personnel */}
          {formData.userType === 'delivery_person' && (
            <div className="border-b border-gray-200 pb-4">
              <h4 className="text-md font-medium text-gray-900 mb-3">Delivery Information</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Vehicle Type <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.vehicleType}
                    onChange={(e) => setFormData(prev => ({ ...prev, vehicleType: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    required
                  >
                    <option value="">Select vehicle type</option>
                    <option value="motorcycle">Motorcycle</option>
                    <option value="car">Car</option>
                    <option value="van">Van</option>
                    <option value="truck">Truck</option>
                    <option value="bicycle">Bicycle</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    License Number <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.licenseNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, licenseNumber: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="Driving license number"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Coverage Area</label>
                  <textarea
                    value={formData.coverageArea}
                    onChange={(e) => setFormData(prev => ({ ...prev, coverageArea: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    rows={2}
                    placeholder="Areas or regions covered for delivery"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors flex items-center space-x-2"
            >
              <UserPlus className="h-4 w-4" />
              <span>{user ? 'Update User' : 'Create User'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UserManagement;
