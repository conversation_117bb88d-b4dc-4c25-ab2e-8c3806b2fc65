import React, { useState } from 'react';
import { AlertTriangle, Database, Copy, Check, X } from 'lucide-react';

const DatabaseUpdateNotification = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [copied, setCopied] = useState(false);

  const sqlScript = `-- YalaOffice Reply System - Simple Fix
-- Run this script in your Supabase SQL Editor

-- 1. Create review_replies table
CREATE TABLE IF NOT EXISTS review_replies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    review_id UUID NOT NULL REFERENCES product_reviews(id) ON DELETE CASCADE,
    admin_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reply_text TEXT NOT NULL CHECK (length(reply_text) >= 10 AND length(reply_text) <= 2000),
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_review_replies_review_id ON review_replies(review_id);
CREATE INDEX IF NOT EXISTS idx_review_replies_admin_id ON review_replies(admin_id);

-- 3. Drop existing policies and disable RLS
DROP POLICY IF EXISTS "Anyone can read public replies" ON review_replies;
DROP POLICY IF EXISTS "Authenticated users can insert replies" ON review_replies;
ALTER TABLE review_replies DISABLE ROW LEVEL SECURITY;`;

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(sqlScript);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed top-4 right-4 max-w-md bg-amber-50 border border-amber-200 rounded-lg shadow-lg z-50">
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-5 w-5 text-amber-400" />
          </div>
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium text-amber-800">
              Database Update Required
            </h3>
            <div className="mt-2 text-sm text-amber-700">
              <p>
                The reply system requires creating the review_replies table and fixing RLS policies.
                If you get RLS errors, run the fix script.
              </p>
              <div className="mt-3">
                <button
                  onClick={copyToClipboard}
                  className="inline-flex items-center px-3 py-2 border border-amber-300 shadow-sm text-sm leading-4 font-medium rounded-md text-amber-700 bg-white hover:bg-amber-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
                >
                  {copied ? (
                    <>
                      <Check className="h-4 w-4 mr-2" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4 mr-2" />
                      Copy SQL Script
                    </>
                  )}
                </button>
              </div>
              <div className="mt-2 text-xs">
                <p>
                  <strong>Instructions:</strong>
                  <br />
                  1. Copy the SQL script above
                  <br />
                  2. Go to your Supabase Dashboard → SQL Editor
                  <br />
                  3. Paste and run the script
                  <br />
                  4. Refresh this page
                </p>
              </div>
            </div>
          </div>
          <div className="ml-4 flex-shrink-0">
            <button
              onClick={() => setIsVisible(false)}
              className="bg-amber-50 rounded-md inline-flex text-amber-400 hover:text-amber-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-amber-50 focus:ring-amber-600"
            >
              <span className="sr-only">Close</span>
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
      
      {/* SQL Script Preview */}
      <div className="border-t border-amber-200 bg-amber-25 px-4 py-3">
        <div className="flex items-center mb-2">
          <Database className="h-4 w-4 text-amber-600 mr-2" />
          <span className="text-xs font-medium text-amber-800">SQL Script Preview:</span>
        </div>
        <pre className="text-xs text-amber-700 bg-white p-2 rounded border overflow-x-auto max-h-32">
{sqlScript}
        </pre>
      </div>
    </div>
  );
};

export default DatabaseUpdateNotification;
