import { useState, useEffect } from 'react';
import {
  Tag,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Co<PERSON>,
  Star,
  Percent,
  DollarSign,
  TrendingUp,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  RefreshCw
} from 'lucide-react';
import CreatePromoCodeModal from '../modals/CreatePromoCodeModal';
import EditPromoCodeModal from '../modals/EditPromoCodeModal';
import DeletePromoCodeModal from '../modals/DeletePromoCodeModal';
import { promoCodesService } from '../../services/promoCodesService';
import { realTimeService } from '../../services/realTimeService';
import {
  PromoCode,
  PromoCodeFilters,
  PromoCodeSort,
  PromoCodesListResponse,
  PromoCodeStats,
  DiscountType,
  PromoCodeStatus
} from '../../types/promoCodes';

interface PromoCodesPageProps {
  initialFilters?: PromoCodeFilters;
}

const PromoCodesPage = ({ initialFilters = {} }: PromoCodesPageProps) => {
  // State management
  const [promoCodes, setPromoCodes] = useState<PromoCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<PromoCodeStats | null>(null);
  const [selectedCodes, setSelectedCodes] = useState<string[]>([]);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const recordsPerPage = 20;

  // Filter and sort state
  const [filters, setFilters] = useState<PromoCodeFilters>(initialFilters);
  const [sort, setSort] = useState<PromoCodeSort>({ field: 'created_at', direction: 'desc' });
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedPromoCode, setSelectedPromoCode] = useState<PromoCode | null>(null);

  // Load data on component mount and when filters/sort/page change
  useEffect(() => {
    loadPromoCodes();
    loadStats();
  }, [filters, sort, currentPage]);

  // Subscribe to real-time events
  useEffect(() => {
    const unsubscribeCreated = realTimeService.subscribe('promo-code-created', () => {
      loadPromoCodes();
      loadStats();
    });

    const unsubscribeUpdated = realTimeService.subscribe('promo-code-updated', () => {
      loadPromoCodes();
      loadStats();
    });

    const unsubscribeDeleted = realTimeService.subscribe('promo-code-deleted', () => {
      loadPromoCodes();
      loadStats();
    });

    const unsubscribeUsed = realTimeService.subscribe('promo-code-used', () => {
      loadPromoCodes();
      loadStats();
    });

    return () => {
      unsubscribeCreated();
      unsubscribeUpdated();
      unsubscribeDeleted();
      unsubscribeUsed();
    };
  }, []);

  // Load promo codes from service
  const loadPromoCodes = async () => {
    try {
      setLoading(true);
      console.log('PromoCodesPage: Loading promo codes', { filters, sort, currentPage });

      const response: PromoCodesListResponse = await promoCodesService.getPromoCodes(
        { ...filters, search: searchQuery },
        sort,
        currentPage,
        recordsPerPage
      );

      setPromoCodes(response.data);
      setTotalPages(response.pagination.total_pages);
      setTotalRecords(response.pagination.total);

      console.log('PromoCodesPage: Promo codes loaded successfully', {
        count: response.data.length,
        total: response.pagination.total
      });
    } catch (error) {
      console.error('PromoCodesPage: Error loading promo codes:', error);
      // TODO: Show error toast
    } finally {
      setLoading(false);
    }
  };

  // Load statistics
  const loadStats = async () => {
    try {
      const statsData = await promoCodesService.getPromoCodeStats();
      setStats(statsData);
    } catch (error) {
      console.error('PromoCodesPage: Error loading stats:', error);
    }
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page
    // Debounce search - will trigger loadPromoCodes via useEffect
    const timeoutId = setTimeout(() => {
      loadPromoCodes();
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<PromoCodeFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page
  };

  // Handle sort changes
  const handleSortChange = (field: PromoCodeSort['field']) => {
    const newDirection = sort.field === field && sort.direction === 'asc' ? 'desc' : 'asc';
    setSort({ field, direction: newDirection });
    setCurrentPage(1); // Reset to first page
  };

  // Clear all filters
  const clearAllFilters = () => {
    setFilters({});
    setSearchQuery('');
    setCurrentPage(1);
  };

  // Get active filter count
  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.status && filters.status.length > 0) count++;
    if (filters.discount_type && filters.discount_type.length > 0) count++;
    if (filters.date_range) count++;
    if (filters.usage_status && filters.usage_status.length > 0) count++;
    if (filters.is_featured !== undefined) count++;
    if (searchQuery) count++;
    return count;
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle row selection
  const handleRowSelect = (id: string) => {
    setSelectedCodes(prev => 
      prev.includes(id) 
        ? prev.filter(codeId => codeId !== id)
        : [...prev, id]
    );
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedCodes.length === promoCodes.length) {
      setSelectedCodes([]);
    } else {
      setSelectedCodes(promoCodes.map(code => code.id));
    }
  };

  // Get promo code status
  const getPromoCodeStatus = (promoCode: PromoCode): PromoCodeStatus => {
    if (!promoCode.is_active) return 'inactive';
    
    const now = new Date();
    if (promoCode.valid_until && new Date(promoCode.valid_until) < now) {
      return 'expired';
    }
    
    if (promoCode.usage_limit_total && promoCode.current_usage_count >= promoCode.usage_limit_total) {
      return 'used_up';
    }
    
    return 'active';
  };

  // Get status badge color
  const getStatusBadgeColor = (status: PromoCodeStatus) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'expired': return 'bg-red-100 text-red-800';
      case 'used_up': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Format discount value
  const formatDiscountValue = (promoCode: PromoCode) => {
    if (promoCode.discount_type === 'percentage') {
      return `${promoCode.discount_value}%`;
    } else {
      return `${promoCode.discount_value} Dh`;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading && promoCodes.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-teal-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading promo codes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header - Mobile Responsive */}
        <div className="mb-8">
          <div className="space-y-4">
            {/* Title and Subtitle */}
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <Tag className="h-8 w-8 mr-3 text-teal-600" />
                Promo Codes Management
              </h1>
              <p className="mt-2 text-gray-600">
                Create and manage promotional discount codes for your customers.
              </p>
            </div>

            {/* Action Button - Responsive Layout */}
            <div className="flex justify-end">
              <button
                onClick={() => setShowCreateModal(true)}
                className="flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors w-full sm:w-auto justify-center"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Promo Code
              </button>
            </div>
          </div>
        </div>

        {/* Statistics Cards */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-teal-100 rounded-lg">
                  <Tag className="h-6 w-6 text-teal-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Codes</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total_codes}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Codes</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.active_codes}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-amber-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-amber-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Usage</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total_usage}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <DollarSign className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Discount</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total_discount_given.toFixed(2)} Dh</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Search and Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              {/* Search */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search promo codes..."
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  />
                </div>
              </div>

              {/* Filter Controls */}
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`flex items-center px-3 py-2 rounded-lg border transition-colors ${
                    showFilters || getActiveFilterCount() > 0
                      ? 'bg-teal-50 border-teal-200 text-teal-700'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                  {getActiveFilterCount() > 0 && (
                    <span className="ml-2 px-2 py-0.5 bg-teal-100 text-teal-800 text-xs rounded-full">
                      {getActiveFilterCount()}
                    </span>
                  )}
                </button>

                {getActiveFilterCount() > 0 && (
                  <button
                    onClick={clearAllFilters}
                    className="text-sm text-gray-500 hover:text-gray-700"
                  >
                    Clear all
                  </button>
                )}

                <button
                  onClick={loadPromoCodes}
                  disabled={loading}
                  className="flex items-center px-3 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </button>
              </div>
            </div>

            {/* Filter Panel */}
            {showFilters && (
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {/* Status Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select
                      multiple
                      value={filters.status || []}
                      onChange={(e) => {
                        const values = Array.from(e.target.selectedOptions, option => option.value) as PromoCodeStatus[];
                        handleFilterChange({ status: values });
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="expired">Expired</option>
                      <option value="used_up">Used Up</option>
                    </select>
                  </div>

                  {/* Discount Type Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Discount Type</label>
                    <select
                      multiple
                      value={filters.discount_type || []}
                      onChange={(e) => {
                        const values = Array.from(e.target.selectedOptions, option => option.value) as DiscountType[];
                        handleFilterChange({ discount_type: values });
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                    >
                      <option value="percentage">Percentage</option>
                      <option value="fixed_amount">Fixed Amount</option>
                    </select>
                  </div>

                  {/* Featured Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Featured</label>
                    <select
                      value={filters.is_featured === undefined ? '' : filters.is_featured.toString()}
                      onChange={(e) => {
                        const value = e.target.value === '' ? undefined : e.target.value === 'true';
                        handleFilterChange({ is_featured: value });
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                    >
                      <option value="">All</option>
                      <option value="true">Featured</option>
                      <option value="false">Not Featured</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Data Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          {/* Table Header with Bulk Actions */}
          {selectedCodes.length > 0 && (
            <div className="px-6 py-4 border-b border-gray-200 bg-teal-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-sm font-medium text-teal-700">
                    {selectedCodes.length} promo code{selectedCodes.length !== 1 ? 's' : ''} selected
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="px-3 py-1 text-sm bg-white border border-teal-200 text-teal-700 rounded hover:bg-teal-50">
                    Activate
                  </button>
                  <button className="px-3 py-1 text-sm bg-white border border-teal-200 text-teal-700 rounded hover:bg-teal-50">
                    Deactivate
                  </button>
                  <button className="px-3 py-1 text-sm bg-white border border-red-200 text-red-700 rounded hover:bg-red-50">
                    Delete
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedCodes.length === promoCodes.length && promoCodes.length > 0}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
                    />
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSortChange('code')}
                  >
                    <div className="flex items-center">
                      Code
                      {sort.field === 'code' && (
                        <span className="ml-1">
                          {sort.direction === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSortChange('name')}
                  >
                    <div className="flex items-center">
                      Name
                      {sort.field === 'name' && (
                        <span className="ml-1">
                          {sort.direction === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSortChange('discount_value')}
                  >
                    <div className="flex items-center">
                      Discount
                      {sort.field === 'discount_value' && (
                        <span className="ml-1">
                          {sort.direction === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSortChange('current_usage_count')}
                  >
                    <div className="flex items-center">
                      Usage
                      {sort.field === 'current_usage_count' && (
                        <span className="ml-1">
                          {sort.direction === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSortChange('valid_until')}
                  >
                    <div className="flex items-center">
                      Expires
                      {sort.field === 'valid_until' && (
                        <span className="ml-1">
                          {sort.direction === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {promoCodes.map((promoCode) => {
                  const status = getPromoCodeStatus(promoCode);
                  return (
                    <tr key={promoCode.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <input
                          type="checkbox"
                          checked={selectedCodes.includes(promoCode.id)}
                          onChange={() => handleRowSelect(promoCode.id)}
                          className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="font-mono font-medium text-gray-900">
                            {promoCode.code}
                          </span>
                          {promoCode.is_featured && (
                            <Star className="h-4 w-4 ml-2 text-amber-500 fill-current" />
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {promoCode.name}
                          </div>
                          {promoCode.description && (
                            <div className="text-sm text-gray-500 truncate max-w-xs">
                              {promoCode.description}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {promoCode.discount_type === 'percentage' ? (
                            <Percent className="h-4 w-4 mr-1 text-green-600" />
                          ) : (
                            <DollarSign className="h-4 w-4 mr-1 text-blue-600" />
                          )}
                          <span className="text-sm font-medium text-gray-900">
                            {formatDiscountValue(promoCode)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(status)}`}>
                          {status.replace('_', ' ')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {promoCode.current_usage_count}
                        {promoCode.usage_limit_total && (
                          <span className="text-gray-500">
                            /{promoCode.usage_limit_total}
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {promoCode.valid_until ? formatDate(promoCode.valid_until) : 'No expiry'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => {
                              setSelectedPromoCode(promoCode);
                              setShowEditModal(true);
                            }}
                            className="text-teal-600 hover:text-teal-900"
                            title="Edit"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => navigator.clipboard.writeText(promoCode.code)}
                            className="text-gray-600 hover:text-gray-900"
                            title="Copy Code"
                          >
                            <Copy className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => {
                              setSelectedPromoCode(promoCode);
                              setShowDeleteModal(true);
                            }}
                            className="text-red-600 hover:text-red-900"
                            title="Delete"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Empty State */}
          {promoCodes.length === 0 && !loading && (
            <div className="text-center py-12">
              <Tag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No promo codes found</h3>
              <p className="text-gray-500 mb-4">
                {getActiveFilterCount() > 0
                  ? 'Try adjusting your filters or search terms.'
                  : 'Get started by setting up the promo codes database.'
                }
              </p>
              {getActiveFilterCount() === 0 && (
                <div className="space-y-4">
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 max-w-md mx-auto">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-amber-800">
                          <strong>Database Setup Required:</strong> Please run the database setup script first.
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-center space-x-3">
                    <a
                      href="/database-setup-promo-codes.html"
                      target="_blank"
                      className="inline-flex items-center px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700"
                    >
                      📋 Database Setup Guide
                    </a>
                    <button
                      onClick={() => setShowCreateModal(true)}
                      className="inline-flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create Promo Code
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Pagination */}
          {totalRecords > 0 && (
            <div className="px-6 py-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  Showing {((currentPage - 1) * recordsPerPage) + 1}-{Math.min(currentPage * recordsPerPage, totalRecords)} of {totalRecords} records
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + 1;
                      return (
                        <button
                          key={page}
                          onClick={() => handlePageChange(page)}
                          className={`px-3 py-1 text-sm rounded ${
                            currentPage === page
                              ? 'bg-teal-600 text-white'
                              : 'border border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      );
                    })}
                  </div>

                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      <CreatePromoCodeModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={() => {
          loadPromoCodes();
          loadStats();
        }}
      />

      <EditPromoCodeModal
        isOpen={showEditModal}
        promoCode={selectedPromoCode}
        onClose={() => {
          setShowEditModal(false);
          setSelectedPromoCode(null);
        }}
        onSuccess={() => {
          loadPromoCodes();
          loadStats();
        }}
      />

      <DeletePromoCodeModal
        isOpen={showDeleteModal}
        promoCode={selectedPromoCode}
        onClose={() => {
          setShowDeleteModal(false);
          setSelectedPromoCode(null);
        }}
        onSuccess={() => {
          loadPromoCodes();
          loadStats();
          setSelectedCodes([]); // Clear selection after delete
        }}
      />
    </div>
  );
};

export default PromoCodesPage;
