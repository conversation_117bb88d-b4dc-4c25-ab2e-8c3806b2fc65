import { useState, useEffect } from 'react';
import { Settings, Save, RefreshCw, Mail, Bell, Shield, Globe, Eye, EyeOff, Send, CheckCircle, AlertCircle, Tag, CreditCard, Building2, FileText, X, Loader, Database, TestTube } from 'lucide-react';
import { systemSettingsService } from '../../services/systemSettingsService';
import { realTimeService } from '../../services/realTimeService';
import {
  CompanySettings,
  SystemConfig,
  PaymentMethodConfig,
  GeneralSettings,
  SecuritySettings,
  NotificationSettings,
  EmailSettings,
  BankTransferConfig,
  CheckPaymentConfig,
  CashPaymentConfig
} from '../../types/systemSettings';

interface SystemSettingsPageProps {
  initialTab?: string;
}

const SystemSettingsPage = ({ initialTab = 'general' }: SystemSettingsPageProps) => {
  const [activeSection, setActiveSection] = useState(initialTab);
  const [loading, setLoading] = useState(true);
  const [saveLoading, setSaveLoading] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [showPassword, setShowPassword] = useState(false);
  const [testEmailLoading, setTestEmailLoading] = useState(false);
  const [testEmailStatus, setTestEmailStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // Database-aligned state management
  const [companySettings, setCompanySettings] = useState<CompanySettings>({
    id: '550e8400-e29b-41d4-a716-************',
    name: 'YalaOffice Morocco',
    address: 'Casablanca, Morocco',
    phone: '+212 5XX-XXX-XXX',
    email: '<EMAIL>',
    tax_id: 'TAX-12345678',
    ice_number: 'ICE-87654321',
    logo_url: ''
  });

  const [generalSettings, setGeneralSettings] = useState<GeneralSettings>({
    site_name: 'YalaOffice Morocco',
    site_description: 'Professional Office & School Supplies',
    timezone: 'Africa/Casablanca',
    session_timeout: 30,
    file_upload_size_limit: 10,
    maintenance_mode: false,
    backup_frequency: 'daily'
  });

  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    password_min_length: 8,
    max_login_attempts: 5,
    session_security_strict: false,
    two_factor_auth_enabled: false,
    password_expiry_days: 90
  });

  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    email_notifications: true,
    sms_notifications: false,
    push_notifications: true,
    order_notifications: true,
    inventory_notifications: true,
    user_notifications: true
  });

  const [emailSettings, setEmailSettings] = useState<EmailSettings>({
    smtp_host: '',
    smtp_port: 587,
    smtp_username: '',
    smtp_password: '',
    smtp_secure: true,
    from_email: '<EMAIL>',
    from_name: 'YalaOffice Morocco',
    test_email: ''
  });

  const [paymentMethods, setPaymentMethods] = useState<PaymentMethodConfig[]>([
    {
      id: 'bank-transfer',
      method_type: 'bank_transfer',
      is_enabled: true,
      configuration: {
        bankName: 'Banque Populaire',
        accountNumber: '**********',
        iban: 'MA64 0123 4567 8901 2345 6789 01',
        swiftCode: 'BMCEMAMC',
        accountHolder: 'YalaOffice SARL'
      } as BankTransferConfig
    },
    {
      id: 'check',
      method_type: 'check',
      is_enabled: true,
      configuration: {
        payableTo: 'YalaOffice SARL',
        mailingAddress: 'Casablanca, Morocco',
        processingTime: '3-5 business days'
      } as CheckPaymentConfig
    },
    {
      id: 'cash',
      method_type: 'cash',
      is_enabled: true,
      configuration: {
        enabled: true
      } as CashPaymentConfig
    }
  ]);

  // Initialize data from database
  useEffect(() => {
    const initializeData = async () => {
      try {
        setLoading(true);
        console.log('SystemSettings: Initializing data from database...');

        // Load all settings from database using systemSettingsService
        const [
          companyData,
          configsByCategory,
          paymentMethodsData
        ] = await Promise.allSettled([
          systemSettingsService.getCompanySettings(),
          systemSettingsService.getSystemConfigsByCategory(),
          systemSettingsService.getPaymentMethods()
        ]).then(results => [
          results[0].status === 'fulfilled' ? results[0].value : null,
          results[1].status === 'fulfilled' ? results[1].value : { general: {}, security: {}, notifications: {}, email: {} },
          results[2].status === 'fulfilled' ? results[2].value : []
        ]);

        // Update state with database data, ensuring no undefined values
        if (companyData) {
          setCompanySettings({
            ...companySettings,
            ...companyData,
            // Ensure all string fields have default values
            name: companyData.name || companySettings.name,
            address: companyData.address || companySettings.address || '',
            phone: companyData.phone || companySettings.phone || '',
            email: companyData.email || companySettings.email || '',
            tax_id: companyData.tax_id || companySettings.tax_id || '',
            ice_number: companyData.ice_number || companySettings.ice_number || '',
            logo_url: companyData.logo_url || companySettings.logo_url || ''
          });
        }

        setGeneralSettings({
          ...generalSettings,
          ...configsByCategory.general,
          // Ensure all fields have default values
          site_name: configsByCategory.general.site_name || generalSettings.site_name,
          site_description: configsByCategory.general.site_description || generalSettings.site_description,
          timezone: configsByCategory.general.timezone || generalSettings.timezone,
          session_timeout: configsByCategory.general.session_timeout || generalSettings.session_timeout,
          file_upload_size_limit: configsByCategory.general.file_upload_size_limit || generalSettings.file_upload_size_limit,
          maintenance_mode: configsByCategory.general.maintenance_mode !== undefined ? configsByCategory.general.maintenance_mode : generalSettings.maintenance_mode,
          backup_frequency: configsByCategory.general.backup_frequency || generalSettings.backup_frequency
        });

        setSecuritySettings({
          ...securitySettings,
          ...configsByCategory.security,
          // Ensure all fields have default values
          password_min_length: configsByCategory.security.password_min_length || securitySettings.password_min_length,
          max_login_attempts: configsByCategory.security.max_login_attempts || securitySettings.max_login_attempts,
          session_security_strict: configsByCategory.security.session_security_strict !== undefined ? configsByCategory.security.session_security_strict : securitySettings.session_security_strict,
          two_factor_auth_enabled: configsByCategory.security.two_factor_auth_enabled !== undefined ? configsByCategory.security.two_factor_auth_enabled : securitySettings.two_factor_auth_enabled,
          password_expiry_days: configsByCategory.security.password_expiry_days || securitySettings.password_expiry_days
        });

        setNotificationSettings({
          ...notificationSettings,
          ...configsByCategory.notifications,
          // Ensure all boolean fields have default values
          email_notifications: configsByCategory.notifications.email_notifications !== undefined ? configsByCategory.notifications.email_notifications : notificationSettings.email_notifications,
          sms_notifications: configsByCategory.notifications.sms_notifications !== undefined ? configsByCategory.notifications.sms_notifications : notificationSettings.sms_notifications,
          push_notifications: configsByCategory.notifications.push_notifications !== undefined ? configsByCategory.notifications.push_notifications : notificationSettings.push_notifications,
          order_notifications: configsByCategory.notifications.order_notifications !== undefined ? configsByCategory.notifications.order_notifications : notificationSettings.order_notifications,
          inventory_notifications: configsByCategory.notifications.inventory_notifications !== undefined ? configsByCategory.notifications.inventory_notifications : notificationSettings.inventory_notifications,
          user_notifications: configsByCategory.notifications.user_notifications !== undefined ? configsByCategory.notifications.user_notifications : notificationSettings.user_notifications
        });

        setEmailSettings({
          ...emailSettings,
          ...configsByCategory.email,
          // Ensure all fields have default values
          smtp_host: configsByCategory.email.smtp_host || emailSettings.smtp_host || '',
          smtp_port: configsByCategory.email.smtp_port || emailSettings.smtp_port,
          smtp_username: configsByCategory.email.smtp_username || emailSettings.smtp_username || '',
          smtp_password: configsByCategory.email.smtp_password || emailSettings.smtp_password || '',
          smtp_secure: configsByCategory.email.smtp_secure !== undefined ? configsByCategory.email.smtp_secure : emailSettings.smtp_secure,
          from_email: configsByCategory.email.from_email || emailSettings.from_email || '',
          from_name: configsByCategory.email.from_name || emailSettings.from_name || '',
          test_email: configsByCategory.email.test_email || emailSettings.test_email || ''
        });

        // Ensure paymentMethods is always an array
        if (Array.isArray(paymentMethodsData) && paymentMethodsData.length > 0) {
          setPaymentMethods(paymentMethodsData);
        } else {
          console.warn('SystemSettings: Payment methods data is not valid, keeping defaults');
          // Keep the default payment methods if database data is invalid
        }

        console.log('SystemSettings: Data loaded successfully from database');
      } catch (error) {
        console.error('Error initializing system settings data:', error);
        // Keep default values if database load fails
      } finally {
        setLoading(false);
      }
    };

    // Subscribe to real-time system settings events
    const unsubscribeCompany = realTimeService.subscribe('company-settings-updated', (data) => {
      console.log('SystemSettings: Company settings updated event received', data);
      setCompanySettings(prev => ({
        ...prev,
        ...data.data,
        // Ensure no undefined values
        name: data.data.name || prev.name,
        address: data.data.address || prev.address || '',
        phone: data.data.phone || prev.phone || '',
        email: data.data.email || prev.email || '',
        tax_id: data.data.tax_id || prev.tax_id || '',
        ice_number: data.data.ice_number || prev.ice_number || '',
        logo_url: data.data.logo_url || prev.logo_url || ''
      }));
    });

    const unsubscribeConfigs = realTimeService.subscribe('system-configs-updated', (data) => {
      console.log('SystemSettings: System configs updated event received', data);
      // Re-organize configs by category
      const configs = data.data;
      const organized = {
        general: {} as GeneralSettings,
        security: {} as SecuritySettings,
        notifications: {} as NotificationSettings,
        email: {} as EmailSettings
      };

      configs.forEach((config: SystemConfig) => {
        const value = parseConfigValue(config.value, config.data_type);
        switch (config.category) {
          case 'general':
            (organized.general as any)[config.key] = value;
            break;
          case 'security':
            (organized.security as any)[config.key] = value;
            break;
          case 'notifications':
            (organized.notifications as any)[config.key] = value;
            break;
          case 'email':
            (organized.email as any)[config.key] = value;
            break;
        }
      });

      setGeneralSettings(organized.general);
      setSecuritySettings(organized.security);
      setNotificationSettings(organized.notifications);
      setEmailSettings(organized.email);
    });

    const unsubscribePayments = realTimeService.subscribe('payment-methods-updated', (data) => {
      console.log('SystemSettings: Payment methods updated event received', data);
      if (Array.isArray(data.data)) {
        setPaymentMethods(data.data);
      } else {
        console.warn('SystemSettings: Invalid payment methods data received in real-time event');
      }
    });

    initializeData();

    return () => {
      unsubscribeCompany();
      unsubscribeConfigs();
      unsubscribePayments();
    };
  }, []);

  // Helper function to parse config values
  const parseConfigValue = (value: string, dataType: string): any => {
    try {
      switch (dataType) {
        case 'boolean':
          return value === 'true' || value === true;
        case 'number':
          return parseFloat(value);
        case 'json':
          return JSON.parse(value);
        case 'string':
        default:
          return value.startsWith('"') && value.endsWith('"') 
            ? value.slice(1, -1) 
            : value;
      }
    } catch (error) {
      console.error('Error parsing config value:', { value, dataType, error });
      return value;
    }
  };

  // Helper function to get config descriptions
  const getConfigDescription = (category: string, key: string): string => {
    const descriptions: Record<string, Record<string, string>> = {
      general: {
        site_name: 'Site name',
        site_description: 'Site description',
        timezone: 'System timezone',
        session_timeout: 'Session timeout in minutes',
        file_upload_size_limit: 'File upload size limit in MB',
        maintenance_mode: 'Enable maintenance mode',
        backup_frequency: 'Backup frequency'
      },
      security: {
        password_min_length: 'Minimum password length',
        max_login_attempts: 'Maximum login attempts',
        session_security_strict: 'Enable strict session security',
        two_factor_auth_enabled: 'Enable two-factor authentication',
        password_expiry_days: 'Password expiry in days'
      },
      notifications: {
        email_notifications: 'Enable email notifications',
        sms_notifications: 'Enable SMS notifications',
        push_notifications: 'Enable push notifications',
        order_notifications: 'Enable order notifications',
        inventory_notifications: 'Enable inventory notifications',
        user_notifications: 'Enable user notifications'
      },
      email: {
        smtp_host: 'SMTP host',
        smtp_port: 'SMTP port',
        smtp_username: 'SMTP username',
        smtp_password: 'SMTP password',
        smtp_secure: 'Use secure SMTP',
        from_email: 'From email address',
        from_name: 'From name',
        test_email: 'Test email address'
      }
    };
    
    return descriptions[category]?.[key] || '';
  };

  // Main save function
  const handleSave = async () => {
    setSaveLoading(true);
    setSaveStatus('idle');

    try {
      console.log('SystemSettings: Saving ALL settings to database...');

      // Prepare system configs from current state
      const systemConfigs: SystemConfig[] = [
        // General settings
        ...Object.entries(generalSettings).map(([key, value]) => ({
          category: 'general' as const,
          key,
          value: String(value),
          data_type: typeof value === 'boolean' ? 'boolean' as const :
                    typeof value === 'number' ? 'number' as const : 'string' as const,
          description: getConfigDescription('general', key)
        })),
        // Security settings
        ...Object.entries(securitySettings).map(([key, value]) => ({
          category: 'security' as const,
          key,
          value: String(value),
          data_type: typeof value === 'boolean' ? 'boolean' as const :
                    typeof value === 'number' ? 'number' as const : 'string' as const,
          description: getConfigDescription('security', key)
        })),
        // Notification settings
        ...Object.entries(notificationSettings).map(([key, value]) => ({
          category: 'notifications' as const,
          key,
          value: String(value),
          data_type: typeof value === 'boolean' ? 'boolean' as const :
                    typeof value === 'number' ? 'number' as const : 'string' as const,
          description: getConfigDescription('notifications', key)
        })),
        // Email settings
        ...Object.entries(emailSettings).map(([key, value]) => ({
          category: 'email' as const,
          key,
          value: String(value),
          data_type: typeof value === 'boolean' ? 'boolean' as const :
                    typeof value === 'number' ? 'number' as const : 'string' as const,
          description: getConfigDescription('email', key)
        }))
      ];

      // Validate critical settings
      if (generalSettings.session_timeout < 5 || generalSettings.session_timeout > 480) {
        alert('Session timeout must be between 5 and 480 minutes.');
        return;
      }

      if (securitySettings.password_min_length < 6 || securitySettings.password_min_length > 32) {
        alert('Password minimum length must be between 6 and 32 characters.');
        return;
      }

      if (securitySettings.max_login_attempts < 3 || securitySettings.max_login_attempts > 10) {
        alert('Max login attempts must be between 3 and 10.');
        return;
      }

      // Save all settings using systemSettingsService
      const [
        savedCompany,
        savedConfigs,
        savedPayments
      ] = await Promise.all([
        systemSettingsService.saveCompanySettings(companySettings),
        systemSettingsService.saveSystemConfigs(systemConfigs),
        systemSettingsService.savePaymentMethods(paymentMethods)
      ]);

      console.log('SystemSettings: All settings saved successfully', {
        company: savedCompany,
        configs: savedConfigs.length,
        payments: savedPayments.length
      });

      // Clear cache to ensure fresh data
      systemSettingsService.clearAllCache();

      // Show success message
      setSaveStatus('success');
      setTimeout(() => setSaveStatus('idle'), 3000);

    } catch (error) {
      console.error('SystemSettings: Error saving settings:', error);
      setSaveStatus('error');
      alert(`Error saving settings: ${error instanceof Error ? error.message : 'Please try again.'}`);
    } finally {
      setSaveLoading(false);
    }
  };

  // Test email function
  const handleTestEmail = async () => {
    setTestEmailLoading(true);
    setTestEmailStatus('idle');

    try {
      console.log('SystemSettings: Testing SMTP configuration...');

      // Validate required fields
      if (!emailSettings.smtp_host || !emailSettings.smtp_username || !emailSettings.smtp_password || !emailSettings.from_email || !emailSettings.test_email) {
        alert('Please fill in all required SMTP fields before testing.');
        setTestEmailStatus('error');
        return;
      }

      // Simulate email test (in real implementation, this would call an API)
      await new Promise(resolve => setTimeout(resolve, 2000));

      setTestEmailStatus('success');
      alert('Test email sent successfully!');
    } catch (error) {
      console.error('SystemSettings: SMTP test error:', error);
      setTestEmailStatus('error');
      alert(`SMTP test failed: ${error instanceof Error ? error.message : 'Please check your SMTP configuration and try again.'}`);
    } finally {
      setTestEmailLoading(false);
    }
  };

  // Sections configuration
  const sections = [
    { id: 'general', label: 'General', icon: Settings },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'email', label: 'Email Settings', icon: Mail },
    { id: 'company', label: 'Company Info', icon: Building2 },
    { id: 'payment', label: 'Payment Methods', icon: CreditCard }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader className="h-8 w-8 animate-spin text-teal-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading system settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Settings className="h-8 w-8 mr-3 text-teal-600" />
            System Settings
          </h1>
          <p className="mt-2 text-gray-600">
            Configure system-wide settings, company information, and payment methods.
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <nav className="space-y-2">
                {sections.map((section) => {
                  const Icon = section.icon;
                  return (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                        activeSection === section.id
                          ? 'bg-teal-50 text-teal-700 border border-teal-200'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <Icon className="h-4 w-4 mr-3" />
                      {section.label}
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              {/* Content will be added in the next section */}
              <div className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  {sections.find(s => s.id === activeSection)?.label}
                </h2>

                {/* Form Content Sections */}
                {activeSection === 'general' && (
                  <div className="space-y-6">
                    {/* Site Information */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Site Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Site Name *
                          </label>
                          <input
                            type="text"
                            value={generalSettings.site_name}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, site_name: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="YalaOffice Morocco"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Site Description
                          </label>
                          <input
                            type="text"
                            value={generalSettings.site_description}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, site_description: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="Professional Office & School Supplies"
                          />
                        </div>
                      </div>
                    </div>

                    {/* System Configuration */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">System Configuration</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Timezone
                          </label>
                          <select
                            value={generalSettings.timezone}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, timezone: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                          >
                            <option value="Africa/Casablanca">Africa/Casablanca (GMT+1)</option>
                            <option value="Europe/London">Europe/London (GMT+0)</option>
                            <option value="Europe/Paris">Europe/Paris (GMT+1)</option>
                            <option value="America/New_York">America/New_York (GMT-5)</option>
                            <option value="Asia/Dubai">Asia/Dubai (GMT+4)</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Session Timeout (minutes)
                          </label>
                          <input
                            type="number"
                            min="5"
                            max="480"
                            value={generalSettings.session_timeout}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, session_timeout: parseInt(e.target.value) || 30 }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="30"
                          />
                          <p className="text-xs text-gray-500 mt-1">Between 5 and 480 minutes</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            File Upload Size Limit (MB)
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="100"
                            value={generalSettings.file_upload_size_limit}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, file_upload_size_limit: parseInt(e.target.value) || 10 }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="10"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Backup Frequency
                          </label>
                          <select
                            value={generalSettings.backup_frequency}
                            onChange={(e) => setGeneralSettings(prev => ({ ...prev, backup_frequency: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                          >
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                          </select>
                        </div>
                      </div>
                    </div>

                    {/* System Maintenance */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">System Maintenance</h3>
                      <div className="flex items-center justify-between">
                        <div>
                          <label className="text-sm font-medium text-gray-700">
                            Maintenance Mode
                          </label>
                          <p className="text-xs text-gray-500">
                            Enable to put the system in maintenance mode for updates
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={() => setGeneralSettings(prev => ({ ...prev, maintenance_mode: !prev.maintenance_mode }))}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            generalSettings.maintenance_mode ? 'bg-teal-600' : 'bg-gray-200'
                          }`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              generalSettings.maintenance_mode ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                      {generalSettings.maintenance_mode && (
                        <div className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                          <div className="flex items-center">
                            <AlertCircle className="h-4 w-4 text-amber-600 mr-2" />
                            <span className="text-sm text-amber-800">
                              Maintenance mode is enabled. Users will see a maintenance page.
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {activeSection === 'security' && (
                  <div className="space-y-6">
                    {/* Password Policy */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Password Policy</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Minimum Password Length
                          </label>
                          <input
                            type="number"
                            min="6"
                            max="32"
                            value={securitySettings.password_min_length}
                            onChange={(e) => setSecuritySettings(prev => ({ ...prev, password_min_length: parseInt(e.target.value) || 8 }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="8"
                          />
                          <p className="text-xs text-gray-500 mt-1">Between 6 and 32 characters</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Password Expiry (days)
                          </label>
                          <input
                            type="number"
                            min="30"
                            max="365"
                            value={securitySettings.password_expiry_days}
                            onChange={(e) => setSecuritySettings(prev => ({ ...prev, password_expiry_days: parseInt(e.target.value) || 90 }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="90"
                          />
                          <p className="text-xs text-gray-500 mt-1">Days before password expires</p>
                        </div>
                      </div>
                    </div>

                    {/* Login Security */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Login Security</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Maximum Login Attempts
                          </label>
                          <input
                            type="number"
                            min="3"
                            max="10"
                            value={securitySettings.max_login_attempts}
                            onChange={(e) => setSecuritySettings(prev => ({ ...prev, max_login_attempts: parseInt(e.target.value) || 5 }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="5"
                          />
                          <p className="text-xs text-gray-500 mt-1">Between 3 and 10 attempts</p>
                        </div>
                      </div>
                    </div>

                    {/* Advanced Security */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Advanced Security</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Strict Session Security
                            </label>
                            <p className="text-xs text-gray-500">
                              Enable strict session validation and IP checking
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => setSecuritySettings(prev => ({ ...prev, session_security_strict: !prev.session_security_strict }))}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              securitySettings.session_security_strict ? 'bg-teal-600' : 'bg-gray-200'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                securitySettings.session_security_strict ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Two-Factor Authentication
                            </label>
                            <p className="text-xs text-gray-500">
                              Require 2FA for all user accounts
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => setSecuritySettings(prev => ({ ...prev, two_factor_auth_enabled: !prev.two_factor_auth_enabled }))}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              securitySettings.two_factor_auth_enabled ? 'bg-teal-600' : 'bg-gray-200'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                securitySettings.two_factor_auth_enabled ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>
                      </div>

                      {(securitySettings.session_security_strict || securitySettings.two_factor_auth_enabled) && (
                        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <div className="flex items-center">
                            <Shield className="h-4 w-4 text-blue-600 mr-2" />
                            <span className="text-sm text-blue-800">
                              Enhanced security features are enabled. Users may need to re-authenticate.
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {activeSection === 'notifications' && (
                  <div className="space-y-6">
                    {/* General Notifications */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">General Notifications</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Email Notifications
                            </label>
                            <p className="text-xs text-gray-500">
                              Send notifications via email
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => setNotificationSettings(prev => ({ ...prev, email_notifications: !prev.email_notifications }))}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              notificationSettings.email_notifications ? 'bg-teal-600' : 'bg-gray-200'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                notificationSettings.email_notifications ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              SMS Notifications
                            </label>
                            <p className="text-xs text-gray-500">
                              Send notifications via SMS
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => setNotificationSettings(prev => ({ ...prev, sms_notifications: !prev.sms_notifications }))}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              notificationSettings.sms_notifications ? 'bg-teal-600' : 'bg-gray-200'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                notificationSettings.sms_notifications ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Push Notifications
                            </label>
                            <p className="text-xs text-gray-500">
                              Send browser push notifications
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => setNotificationSettings(prev => ({ ...prev, push_notifications: !prev.push_notifications }))}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              notificationSettings.push_notifications ? 'bg-teal-600' : 'bg-gray-200'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                notificationSettings.push_notifications ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Business Notifications */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Business Notifications</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Order Notifications
                            </label>
                            <p className="text-xs text-gray-500">
                              Notify about new orders, status changes, and updates
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => setNotificationSettings(prev => ({ ...prev, order_notifications: !prev.order_notifications }))}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              notificationSettings.order_notifications ? 'bg-teal-600' : 'bg-gray-200'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                notificationSettings.order_notifications ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Inventory Notifications
                            </label>
                            <p className="text-xs text-gray-500">
                              Notify about low stock, inventory updates, and alerts
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => setNotificationSettings(prev => ({ ...prev, inventory_notifications: !prev.inventory_notifications }))}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              notificationSettings.inventory_notifications ? 'bg-teal-600' : 'bg-gray-200'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                notificationSettings.inventory_notifications ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              User Notifications
                            </label>
                            <p className="text-xs text-gray-500">
                              Notify about user account changes and activities
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => setNotificationSettings(prev => ({ ...prev, user_notifications: !prev.user_notifications }))}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              notificationSettings.user_notifications ? 'bg-teal-600' : 'bg-gray-200'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                notificationSettings.user_notifications ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Notification Summary */}
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                      <div className="flex items-center mb-2">
                        <Bell className="h-4 w-4 text-blue-600 mr-2" />
                        <span className="text-sm font-medium text-blue-900">Notification Summary</span>
                      </div>
                      <div className="text-xs text-blue-800">
                        {Object.values(notificationSettings).filter(Boolean).length} of {Object.keys(notificationSettings).length} notification types enabled
                      </div>
                    </div>
                  </div>
                )}

                {activeSection === 'email' && (
                  <div className="space-y-6">
                    {/* SMTP Configuration */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">SMTP Configuration</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            SMTP Host *
                          </label>
                          <input
                            type="text"
                            value={emailSettings.smtp_host}
                            onChange={(e) => setEmailSettings(prev => ({ ...prev, smtp_host: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="smtp.gmail.com"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            SMTP Port *
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="65535"
                            value={emailSettings.smtp_port}
                            onChange={(e) => setEmailSettings(prev => ({ ...prev, smtp_port: parseInt(e.target.value) || 587 }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="587"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            SMTP Username *
                          </label>
                          <input
                            type="text"
                            value={emailSettings.smtp_username}
                            onChange={(e) => setEmailSettings(prev => ({ ...prev, smtp_username: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="<EMAIL>"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            SMTP Password *
                          </label>
                          <div className="relative">
                            <input
                              type={showPassword ? 'text' : 'password'}
                              value={emailSettings.smtp_password}
                              onChange={(e) => setEmailSettings(prev => ({ ...prev, smtp_password: e.target.value }))}
                              className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                              placeholder="••••••••"
                              required
                            />
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            >
                              {showPassword ? (
                                <EyeOff className="h-4 w-4 text-gray-400" />
                              ) : (
                                <Eye className="h-4 w-4 text-gray-400" />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>

                      <div className="mt-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Use Secure Connection (TLS/SSL)
                            </label>
                            <p className="text-xs text-gray-500">
                              Enable secure SMTP connection
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => setEmailSettings(prev => ({ ...prev, smtp_secure: !prev.smtp_secure }))}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              emailSettings.smtp_secure ? 'bg-teal-600' : 'bg-gray-200'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                emailSettings.smtp_secure ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Email Settings */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Email Settings</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            From Email Address *
                          </label>
                          <input
                            type="email"
                            value={emailSettings.from_email}
                            onChange={(e) => setEmailSettings(prev => ({ ...prev, from_email: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="<EMAIL>"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            From Name
                          </label>
                          <input
                            type="text"
                            value={emailSettings.from_name}
                            onChange={(e) => setEmailSettings(prev => ({ ...prev, from_name: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="YalaOffice Morocco"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Test Email */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Test Email Configuration</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Test Email Address
                          </label>
                          <input
                            type="email"
                            value={emailSettings.test_email}
                            onChange={(e) => setEmailSettings(prev => ({ ...prev, test_email: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="<EMAIL>"
                          />
                        </div>
                        <div className="flex items-end">
                          <button
                            type="button"
                            onClick={handleTestEmail}
                            disabled={testEmailLoading || !emailSettings.smtp_host || !emailSettings.smtp_username || !emailSettings.smtp_password || !emailSettings.from_email || !emailSettings.test_email}
                            className={`w-full flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-colors ${
                              testEmailLoading || !emailSettings.smtp_host || !emailSettings.smtp_username || !emailSettings.smtp_password || !emailSettings.from_email || !emailSettings.test_email
                                ? 'bg-gray-400 text-white cursor-not-allowed'
                                : 'bg-amber-500 text-white hover:bg-amber-600'
                            }`}
                          >
                            {testEmailLoading ? (
                              <>
                                <Loader className="h-4 w-4 mr-2 animate-spin" />
                                Testing...
                              </>
                            ) : (
                              <>
                                <TestTube className="h-4 w-4 mr-2" />
                                Send Test Email
                              </>
                            )}
                          </button>
                        </div>
                      </div>

                      {testEmailStatus === 'success' && (
                        <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                          <div className="flex items-center">
                            <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                            <span className="text-sm text-green-800">
                              Test email sent successfully!
                            </span>
                          </div>
                        </div>
                      )}

                      {testEmailStatus === 'error' && (
                        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                          <div className="flex items-center">
                            <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
                            <span className="text-sm text-red-800">
                              Test email failed. Please check your SMTP configuration.
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {activeSection === 'company' && (
                  <div className="space-y-6">
                    {/* Company Information */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Company Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Company Name *
                          </label>
                          <input
                            type="text"
                            value={companySettings.name}
                            onChange={(e) => setCompanySettings(prev => ({ ...prev, name: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="YalaOffice Morocco"
                            required
                            maxLength={255}
                          />
                        </div>
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Address
                          </label>
                          <textarea
                            value={companySettings.address || ''}
                            onChange={(e) => setCompanySettings(prev => ({ ...prev, address: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="Company address"
                            rows={3}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number
                          </label>
                          <input
                            type="tel"
                            value={companySettings.phone || ''}
                            onChange={(e) => setCompanySettings(prev => ({ ...prev, phone: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="+212 5XX-XXX-XXX"
                            maxLength={50}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                          </label>
                          <input
                            type="email"
                            value={companySettings.email || ''}
                            onChange={(e) => setCompanySettings(prev => ({ ...prev, email: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="<EMAIL>"
                            maxLength={255}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Legal Information */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Legal Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Tax ID
                          </label>
                          <input
                            type="text"
                            value={companySettings.tax_id || ''}
                            onChange={(e) => setCompanySettings(prev => ({ ...prev, tax_id: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="TAX-12345678"
                            maxLength={100}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            ICE Number
                          </label>
                          <input
                            type="text"
                            value={companySettings.ice_number || ''}
                            onChange={(e) => setCompanySettings(prev => ({ ...prev, ice_number: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="ICE-87654321"
                            maxLength={100}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Company Logo */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Company Logo</h3>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Logo URL
                          </label>
                          <input
                            type="url"
                            value={companySettings.logo_url || ''}
                            onChange={(e) => setCompanySettings(prev => ({ ...prev, logo_url: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                            placeholder="https://example.com/logo.png"
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            Enter a URL to your company logo image
                          </p>
                        </div>

                        {companySettings.logo_url && (
                          <div className="mt-4">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Logo Preview
                            </label>
                            <div className="border border-gray-200 rounded-lg p-4 bg-white">
                              <img
                                src={companySettings.logo_url}
                                alt="Company Logo"
                                className="max-h-20 max-w-40 object-contain"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                }}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Company Summary */}
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                      <div className="flex items-center mb-2">
                        <Building2 className="h-4 w-4 text-blue-600 mr-2" />
                        <span className="text-sm font-medium text-blue-900">Company Profile</span>
                      </div>
                      <div className="text-xs text-blue-800">
                        This information will appear on invoices, reports, and official documents.
                      </div>
                    </div>
                  </div>
                )}

                {activeSection === 'payment' && (
                  <div className="space-y-6">
                    {/* Safety check for paymentMethods array */}
                    {!Array.isArray(paymentMethods) && (
                      <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                        <div className="flex items-center">
                          <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
                          <span className="text-sm text-red-800">
                            Error loading payment methods. Please refresh the page.
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Bank Transfer */}
                    {Array.isArray(paymentMethods) && (() => {
                      const bankTransfer = paymentMethods.find(pm => pm.method_type === 'bank_transfer');
                      const bankConfig = bankTransfer?.configuration as BankTransferConfig;
                      return (
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium text-gray-900">Bank Transfer</h3>
                            <button
                              type="button"
                              onClick={() => setPaymentMethods(prev => prev.map(pm =>
                                pm.method_type === 'bank_transfer'
                                  ? { ...pm, is_enabled: !pm.is_enabled }
                                  : pm
                              ))}
                              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                                bankTransfer?.is_enabled ? 'bg-teal-600' : 'bg-gray-200'
                              }`}
                            >
                              <span
                                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                  bankTransfer?.is_enabled ? 'translate-x-6' : 'translate-x-1'
                                }`}
                              />
                            </button>
                          </div>

                          {bankTransfer?.is_enabled && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                  Bank Name
                                </label>
                                <input
                                  type="text"
                                  value={bankConfig?.bankName || ''}
                                  onChange={(e) => setPaymentMethods(prev => prev.map(pm =>
                                    pm.method_type === 'bank_transfer'
                                      ? { ...pm, configuration: { ...bankConfig, bankName: e.target.value } }
                                      : pm
                                  ))}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                                  placeholder="Banque Populaire"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                  Account Number
                                </label>
                                <input
                                  type="text"
                                  value={bankConfig?.accountNumber || ''}
                                  onChange={(e) => setPaymentMethods(prev => prev.map(pm =>
                                    pm.method_type === 'bank_transfer'
                                      ? { ...pm, configuration: { ...bankConfig, accountNumber: e.target.value } }
                                      : pm
                                  ))}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                                  placeholder="**********"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                  IBAN
                                </label>
                                <input
                                  type="text"
                                  value={bankConfig?.iban || ''}
                                  onChange={(e) => setPaymentMethods(prev => prev.map(pm =>
                                    pm.method_type === 'bank_transfer'
                                      ? { ...pm, configuration: { ...bankConfig, iban: e.target.value } }
                                      : pm
                                  ))}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                                  placeholder="MA64 0123 4567 8901 2345 6789 01"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                  SWIFT Code
                                </label>
                                <input
                                  type="text"
                                  value={bankConfig?.swiftCode || ''}
                                  onChange={(e) => setPaymentMethods(prev => prev.map(pm =>
                                    pm.method_type === 'bank_transfer'
                                      ? { ...pm, configuration: { ...bankConfig, swiftCode: e.target.value } }
                                      : pm
                                  ))}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                                  placeholder="BMCEMAMC"
                                />
                              </div>
                              <div className="md:col-span-2">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                  Account Holder
                                </label>
                                <input
                                  type="text"
                                  value={bankConfig?.accountHolder || ''}
                                  onChange={(e) => setPaymentMethods(prev => prev.map(pm =>
                                    pm.method_type === 'bank_transfer'
                                      ? { ...pm, configuration: { ...bankConfig, accountHolder: e.target.value } }
                                      : pm
                                  ))}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                                  placeholder="YalaOffice SARL"
                                />
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })()}

                    {/* Check Payment */}
                    {Array.isArray(paymentMethods) && (() => {
                      const checkPayment = paymentMethods.find(pm => pm.method_type === 'check');
                      const checkConfig = checkPayment?.configuration as CheckPaymentConfig;
                      return (
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium text-gray-900">Check Payment</h3>
                            <button
                              type="button"
                              onClick={() => setPaymentMethods(prev => prev.map(pm =>
                                pm.method_type === 'check'
                                  ? { ...pm, is_enabled: !pm.is_enabled }
                                  : pm
                              ))}
                              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                                checkPayment?.is_enabled ? 'bg-teal-600' : 'bg-gray-200'
                              }`}
                            >
                              <span
                                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                  checkPayment?.is_enabled ? 'translate-x-6' : 'translate-x-1'
                                }`}
                              />
                            </button>
                          </div>

                          {checkPayment?.is_enabled && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                  Payable To
                                </label>
                                <input
                                  type="text"
                                  value={checkConfig?.payableTo || ''}
                                  onChange={(e) => setPaymentMethods(prev => prev.map(pm =>
                                    pm.method_type === 'check'
                                      ? { ...pm, configuration: { ...checkConfig, payableTo: e.target.value } }
                                      : pm
                                  ))}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                                  placeholder="YalaOffice SARL"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                  Processing Time
                                </label>
                                <input
                                  type="text"
                                  value={checkConfig?.processingTime || ''}
                                  onChange={(e) => setPaymentMethods(prev => prev.map(pm =>
                                    pm.method_type === 'check'
                                      ? { ...pm, configuration: { ...checkConfig, processingTime: e.target.value } }
                                      : pm
                                  ))}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                                  placeholder="3-5 business days"
                                />
                              </div>
                              <div className="md:col-span-2">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                  Mailing Address
                                </label>
                                <textarea
                                  value={checkConfig?.mailingAddress || ''}
                                  onChange={(e) => setPaymentMethods(prev => prev.map(pm =>
                                    pm.method_type === 'check'
                                      ? { ...pm, configuration: { ...checkConfig, mailingAddress: e.target.value } }
                                      : pm
                                  ))}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                                  placeholder="Mailing address for check payments"
                                  rows={2}
                                />
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })()}

                    {/* Cash on Delivery */}
                    {Array.isArray(paymentMethods) && (() => {
                      const cashPayment = paymentMethods.find(pm => pm.method_type === 'cash');
                      return (
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium text-gray-900">Cash on Delivery (COD)</h3>
                            <button
                              type="button"
                              onClick={() => setPaymentMethods(prev => prev.map(pm =>
                                pm.method_type === 'cash'
                                  ? { ...pm, is_enabled: !pm.is_enabled }
                                  : pm
                              ))}
                              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                                cashPayment?.is_enabled ? 'bg-teal-600' : 'bg-gray-200'
                              }`}
                            >
                              <span
                                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                  cashPayment?.is_enabled ? 'translate-x-6' : 'translate-x-1'
                                }`}
                              />
                            </button>
                          </div>

                          {cashPayment?.is_enabled && (
                            <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                              <div className="flex items-center">
                                <CreditCard className="h-4 w-4 text-amber-600 mr-2" />
                                <span className="text-sm text-amber-800">
                                  Cash on Delivery is enabled. Customers can pay upon delivery.
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })()}

                    {/* Payment Methods Summary */}
                    {Array.isArray(paymentMethods) && (
                      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <div className="flex items-center mb-2">
                          <CreditCard className="h-4 w-4 text-blue-600 mr-2" />
                          <span className="text-sm font-medium text-blue-900">Payment Methods Summary</span>
                        </div>
                        <div className="text-xs text-blue-800">
                          {paymentMethods.filter(pm => pm.is_enabled).length} of {paymentMethods.length} payment methods enabled
                        </div>
                        <div className="mt-2 text-xs text-blue-700">
                          Enabled methods: {paymentMethods.filter(pm => pm.is_enabled).map(pm => {
                            switch (pm.method_type) {
                              case 'bank_transfer': return 'Bank Transfer';
                              case 'check': return 'Check Payment';
                              case 'cash': return 'Cash on Delivery';
                              default: return pm.method_type;
                            }
                          }).join(', ') || 'None'}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Save Button */}
              <div className="border-t border-gray-200 px-6 py-4 bg-gray-50 rounded-b-xl">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {saveStatus === 'success' && (
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        <span className="text-sm">Settings saved successfully!</span>
                      </div>
                    )}
                    {saveStatus === 'error' && (
                      <div className="flex items-center text-red-600">
                        <AlertCircle className="h-4 w-4 mr-2" />
                        <span className="text-sm">Error saving settings</span>
                      </div>
                    )}
                  </div>

                  <button
                    onClick={handleSave}
                    disabled={saveLoading}
                    className={`flex items-center px-6 py-2 rounded-lg font-medium transition-colors ${
                      saveLoading
                        ? 'bg-gray-400 text-white cursor-not-allowed'
                        : 'bg-teal-600 text-white hover:bg-teal-700'
                    }`}
                  >
                    {saveLoading ? (
                      <>
                        <Loader className="h-4 w-4 mr-2 animate-spin" />
                        Saving All Settings...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save All Settings
                      </>
                    )}
                  </button>
                </div>

                <div className="mt-3 text-xs text-gray-500">
                  <strong>Note:</strong> This will save all settings across all tabs (General, Security, Notifications, Email, Company Info, and Payment Methods) to the database with real-time synchronization.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemSettingsPage;
