import { useState, useEffect } from 'react';
import { Star, MessageSquare, User, Package, Calendar, CheckCircle, XCircle, Trash2, Reply, Filter, Search, Eye, MoreHorizontal, X } from 'lucide-react';
import { supabase } from '../../integrations/supabase/client';
import { liveDataService } from '../../services/liveDataService';
import { realTimeService } from '../../services/realTimeService';
import { reviewService } from '../../services/reviewService';
import { replyService } from '../../services/replyService';


import YalaPagination from '../ui/YalaPagination';

interface Review {
  id: string;
  product_id: string;
  customer_id: string;
  rating: number;
  title?: string;
  comment: string;
  is_verified: boolean;
  helpful_count: number;
  status?: 'pending' | 'approved' | 'rejected'; // We'll add this field
  created_at: string;
  updated_at: string;
  products?: {
    id: string;
    title: string;
    featured_image?: string;
  };
  users?: {
    id: string;
    full_name: string;
    email: string;
  };
}

interface ReviewStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  averageRating: number;
}

const ReviewsManagementPage = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [stats, setStats] = useState<ReviewStats>({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    averageRating: 0
  });
  const [loading, setLoading] = useState(true);
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [showReplyModal, setShowReplyModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [replyText, setReplyText] = useState('');
  const [replyLoading, setReplyLoading] = useState(false);
  const [replyError, setReplyError] = useState('');
  const [detailsReplyText, setDetailsReplyText] = useState('');
  const [detailsReplyLoading, setDetailsReplyLoading] = useState(false);
  const [detailsReplyError, setDetailsReplyError] = useState('');
  const [reviewReplies, setReviewReplies] = useState<any[]>([]);
  const [replyCounts, setReplyCounts] = useState<{[key: string]: number}>({});
  const [filters, setFilters] = useState({
    status: 'all',
    rating: 'all',
    search: ''
  });
  const [currentPage, setCurrentPage] = useState(1);
  const recordsPerPage = 20;

  useEffect(() => {
    loadReviews();
    setupRealTimeSync();
  }, []);

  const setupRealTimeSync = () => {
    console.log('ReviewsManagement: Setting up real-time synchronization');

    // Subscribe to all review changes using reviewService
    const reviewSubscription = reviewService.subscribeToAllReviews((payload) => {
      console.log('ReviewsManagement: Real-time review update:', payload);
      loadReviews(); // Reload reviews when changes occur
    });

    // Subscribe to review-related events
    const reviewEventSubscription = realTimeService.subscribe('review-updated', (payload) => {
      console.log('ReviewsManagement: Review event received:', payload);
      loadReviews();
    });

    const statusEventSubscription = realTimeService.subscribe('review-status-updated', (payload) => {
      console.log('ReviewsManagement: Review status event received:', payload);
      loadReviews();
    });

    const deleteEventSubscription = realTimeService.subscribe('review-deleted', (payload) => {
      console.log('ReviewsManagement: Review delete event received:', payload);
      loadReviews();
    });

    // Subscribe to reply events
    const replyCreatedSubscription = realTimeService.subscribe('reply-created', (payload) => {
      console.log('ReviewsManagement: Reply created event received:', payload);
      loadReviews(); // Reload to update reply counts
      if (selectedReview && payload.reviewId === selectedReview.id) {
        loadRepliesForReview(selectedReview.id); // Reload replies for details modal
      }
    });

    const replyUpdatedSubscription = realTimeService.subscribe('reply-updated', (payload) => {
      console.log('ReviewsManagement: Reply updated event received:', payload);
      if (selectedReview) {
        loadRepliesForReview(selectedReview.id);
      }
    });

    const replyDeletedSubscription = realTimeService.subscribe('reply-deleted', (payload) => {
      console.log('ReviewsManagement: Reply deleted event received:', payload);
      loadReviews(); // Reload to update reply counts
      if (selectedReview) {
        loadRepliesForReview(selectedReview.id);
      }
    });

    return () => {
      reviewSubscription.unsubscribe();
      reviewEventSubscription();
      statusEventSubscription();
      deleteEventSubscription();
      replyCreatedSubscription();
      replyUpdatedSubscription();
      replyDeletedSubscription();
    };
  };

  const loadReviews = async () => {
    try {
      setLoading(true);
      console.log('ReviewsManagement: Loading reviews from database');

      const reviewsData = await reviewService.getAllReviews();
      setReviews(reviewsData || []);
      calculateStats(reviewsData || []);

      // Load reply counts for each review
      await loadReplyCounts(reviewsData || []);

      console.log('ReviewsManagement: Reviews loaded successfully');
    } catch (error) {
      console.error('ReviewsManagement: Error loading reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadReplyCounts = async (reviewsData: any[]) => {
    try {
      const counts: {[key: string]: number} = {};

      // Load reply counts for each review
      for (const review of reviewsData) {
        const replies = await replyService.getReviewReplies(review.id);
        counts[review.id] = replies.length;
      }

      setReplyCounts(counts);
    } catch (error) {
      console.error('Error loading reply counts:', error);
    }
  };

  const calculateStats = (reviewsData: Review[]) => {
    const stats = {
      total: reviewsData.length,
      pending: reviewsData.filter(r => r.status === 'pending' || !r.status).length, // Default to pending if no status
      approved: reviewsData.filter(r => r.status === 'approved').length,
      rejected: reviewsData.filter(r => r.status === 'rejected').length,
      averageRating: reviewsData.length > 0
        ? reviewsData.reduce((sum, r) => sum + r.rating, 0) / reviewsData.length
        : 0
    };
    setStats(stats);
  };

  const handleStatusUpdate = async (reviewId: string, newStatus: 'approved' | 'rejected') => {
    try {
      console.log(`ReviewsManagement: Updating review ${reviewId} status to ${newStatus}`);

      await reviewService.updateReviewStatus(reviewId, newStatus);
      await loadReviews(); // Reload to show updated status

      console.log('ReviewsManagement: Review status updated successfully');
    } catch (error) {
      console.error('ReviewsManagement: Error updating review status:', error);
      alert('Error updating review status. Please try again.');
    }
  };

  const handleDeleteReview = async (reviewId: string) => {
    if (!confirm('Are you sure you want to delete this review? This action cannot be undone.')) {
      return;
    }

    try {
      console.log(`ReviewsManagement: Deleting review ${reviewId}`);

      await reviewService.deleteReview(reviewId);
      await loadReviews(); // Reload to remove deleted review

      console.log('ReviewsManagement: Review deleted successfully');
    } catch (error) {
      console.error('ReviewsManagement: Error deleting review:', error);
      alert('Error deleting review. Please try again.');
    }
  };

  const loadRepliesForReview = async (reviewId: string) => {
    try {
      const replies = await replyService.getReviewReplies(reviewId);
      setReviewReplies(replies);
    } catch (error) {
      console.error('Error loading replies:', error);
    }
  };

  const handleDetailsReplySubmit = async () => {
    if (!selectedReview) return;

    try {
      setDetailsReplyLoading(true);
      setDetailsReplyError('');

      // Validate reply text
      const validation = replyService.validateReplyText(detailsReplyText);
      if (!validation.isValid) {
        setDetailsReplyError(validation.error || 'Invalid reply text');
        return;
      }

      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      console.log('Current user:', user);
      console.log('Auth error:', authError);

      if (!user) {
        setDetailsReplyError('You must be logged in to reply');
        return;
      }

      // Check permissions (with development mode bypass)
      console.log('Checking permissions for user:', user.id);
      const hasPermission = await replyService.checkReplyPermission(user.id);
      console.log('Permission check result:', hasPermission);

      // Development mode: Allow replies for testing (remove this in production)
      const isDevelopment = import.meta.env.DEV;
      if (!hasPermission && !isDevelopment) {
        setDetailsReplyError('You do not have permission to reply to reviews');
        return;
      }

      if (isDevelopment && !hasPermission) {
        console.warn('Development mode: Bypassing permission check for testing');
      }

      // Create the reply
      await replyService.createReply({
        review_id: selectedReview.id,
        admin_id: user.id,
        reply_text: detailsReplyText.trim(),
        is_public: true
      });

      // Reset form and reload replies
      setDetailsReplyText('');
      await loadRepliesForReview(selectedReview.id);

      console.log('ReviewsManagement: Reply submitted successfully from details modal');
    } catch (error) {
      console.error('ReviewsManagement: Error submitting reply from details modal:', error);
      setDetailsReplyError('Error submitting reply. Please try again.');
    } finally {
      setDetailsReplyLoading(false);
    }
  };

  const handleReplySubmit = async () => {
    if (!selectedReview) return;

    try {
      setReplyLoading(true);
      setReplyError('');

      // Validate reply text
      const validation = replyService.validateReplyText(replyText);
      if (!validation.isValid) {
        setReplyError(validation.error || 'Invalid reply text');
        return;
      }

      // Get current user (in a real app, this would come from auth context)
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      console.log('Current user:', user);
      console.log('Auth error:', authError);

      if (!user) {
        setReplyError('You must be logged in to reply');
        return;
      }

      // Check permissions (with development mode bypass)
      console.log('Checking permissions for user:', user.id);
      const hasPermission = await replyService.checkReplyPermission(user.id);
      console.log('Permission check result:', hasPermission);

      // Development mode: Allow replies for testing (remove this in production)
      const isDevelopment = import.meta.env.DEV;
      if (!hasPermission && !isDevelopment) {
        setReplyError('You do not have permission to reply to reviews');
        return;
      }

      if (isDevelopment && !hasPermission) {
        console.warn('Development mode: Bypassing permission check for testing');
      }

      // Create the reply
      await replyService.createReply({
        review_id: selectedReview.id,
        admin_id: user.id,
        reply_text: replyText.trim(),
        is_public: true
      });

      // Reset form and close modal
      setReplyText('');
      setShowReplyModal(false);
      setSelectedReview(null);

      // Reload reviews to show updated data
      await loadReviews();

      console.log('ReviewsManagement: Reply submitted successfully');
    } catch (error) {
      console.error('ReviewsManagement: Error submitting reply:', error);
      setReplyError('Error submitting reply. Please try again.');
    } finally {
      setReplyLoading(false);
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="text-sm text-gray-600 ml-1">({rating})</span>
      </div>
    );
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Pending' },
      approved: { bg: 'bg-green-100', text: 'text-green-800', label: 'Approved' },
      rejected: { bg: 'bg-red-100', text: 'text-red-800', label: 'Rejected' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const filteredReviews = reviews.filter(review => {
    const reviewStatus = review.status || 'pending'; // Default to pending if no status
    if (filters.status !== 'all' && reviewStatus !== filters.status) return false;
    if (filters.rating !== 'all' && review.rating !== parseInt(filters.rating)) return false;
    if (filters.search && !review.products?.title.toLowerCase().includes(filters.search.toLowerCase()) &&
        !review.users?.full_name.toLowerCase().includes(filters.search.toLowerCase()) &&
        !review.comment?.toLowerCase().includes(filters.search.toLowerCase())) return false;
    return true;
  });

  // Pagination calculations
  const totalPages = Math.ceil(filteredReviews.length / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const paginatedReviews = filteredReviews.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters.status, filters.rating, filters.search]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">


      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MessageSquare className="h-5 w-5 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Total Reviews</p>
              <p className="text-lg font-semibold text-gray-900">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Star className="h-5 w-5 text-yellow-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Avg Rating</p>
              <p className="text-lg font-semibold text-gray-900">{stats.averageRating.toFixed(1)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Calendar className="h-5 w-5 text-yellow-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Pending</p>
              <p className="text-lg font-semibold text-gray-900">{stats.pending}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Approved</p>
              <p className="text-lg font-semibold text-gray-900">{stats.approved}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <XCircle className="h-5 w-5 text-red-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Rejected</p>
              <p className="text-lg font-semibold text-gray-900">{stats.rejected}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Filters:</span>
            </div>
            
            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>

            <select
              value={filters.rating}
              onChange={(e) => setFilters(prev => ({ ...prev, rating: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            >
              <option value="all">All Ratings</option>
              <option value="5">5 Stars</option>
              <option value="4">4 Stars</option>
              <option value="3">3 Stars</option>
              <option value="2">2 Stars</option>
              <option value="1">1 Star</option>
            </select>
          </div>

          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search reviews..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Reviews Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Reviews ({filteredReviews.length})
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rating
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Comment
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Helpful
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Replies
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedReviews.map((review) => (
                <tr key={review.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {review.products?.featured_image ? (
                        <img
                          className="h-10 w-10 rounded-lg object-cover mr-3"
                          src={review.products.featured_image}
                          alt={review.products.title}
                        />
                      ) : (
                        <div className="h-10 w-10 bg-gray-200 rounded-lg flex items-center justify-center mr-3">
                          <Package className="h-5 w-5 text-gray-400" />
                        </div>
                      )}
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {review.products?.title || 'Unknown Product'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-8 w-8 bg-teal-100 rounded-full flex items-center justify-center mr-3">
                        <User className="h-4 w-4 text-teal-600" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {review.users?.full_name || 'Anonymous'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {review.users?.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {renderStars(review.rating)}
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 max-w-xs truncate">
                      {review.comment}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(review.status || 'pending')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <span>{review.helpful_count || 0}</span>
                      <span className="text-gray-400">votes</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Reply className="h-4 w-4 text-gray-400" />
                      <span>{replyCounts[review.id] || 0}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(review.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      {(review.status === 'pending' || !review.status) && (
                        <>
                          <button
                            onClick={() => handleStatusUpdate(review.id, 'approved')}
                            className="text-green-600 hover:text-green-900 p-1 rounded"
                            title="Approve"
                          >
                            <CheckCircle className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleStatusUpdate(review.id, 'rejected')}
                            className="text-red-600 hover:text-red-900 p-1 rounded"
                            title="Reject"
                          >
                            <XCircle className="h-4 w-4" />
                          </button>
                        </>
                      )}
                      <button
                        onClick={async () => {
                          setSelectedReview(review);
                          setShowDetailsModal(true);
                          await loadRepliesForReview(review.id);
                        }}
                        className="text-teal-600 hover:text-teal-900 p-1 rounded"
                        title="View Details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedReview(review);
                          setShowReplyModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="Reply"
                      >
                        <Reply className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteReview(review.id)}
                        className="text-red-600 hover:text-red-900 p-1 rounded"
                        title="Delete"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredReviews.length === 0 && (
            <div className="text-center py-12">
              <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No reviews found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {filters.search || filters.status !== 'all' || filters.rating !== 'all'
                  ? 'Try adjusting your filters'
                  : 'Reviews will appear here when customers leave feedback'}
              </p>
            </div>
          )}
        </div>

        {/* Pagination */}
        {filteredReviews.length > 0 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <YalaPagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalRecords={filteredReviews.length}
              recordsPerPage={recordsPerPage}
              onPageChange={setCurrentPage}
            />
          </div>
        )}
      </div>

      {/* View Details Modal */}
      {showDetailsModal && selectedReview && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              {/* Modal Header */}
              <div className="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Review Details</h3>
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              {/* Modal Content */}
              <div className="py-6 space-y-6">
                {/* Product Information */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <Package className="h-5 w-5 mr-2 text-teal-600" />
                    Product Information
                  </h4>
                  <div className="flex items-center space-x-4">
                    {selectedReview.products?.featured_image ? (
                      <img
                        className="h-16 w-16 rounded-lg object-cover"
                        src={selectedReview.products.featured_image}
                        alt={selectedReview.products.title}
                      />
                    ) : (
                      <div className="h-16 w-16 bg-gray-200 rounded-lg flex items-center justify-center">
                        <Package className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                    <div>
                      <p className="font-medium text-gray-900">{selectedReview.products?.title}</p>
                      <p className="text-sm text-gray-500">Product ID: {selectedReview.product_id}</p>
                    </div>
                  </div>
                </div>

                {/* Customer Information */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <User className="h-5 w-5 mr-2 text-teal-600" />
                    Customer Information
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Name</p>
                      <p className="font-medium text-gray-900">{selectedReview.users?.full_name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium text-gray-900">{selectedReview.users?.email}</p>
                    </div>
                  </div>
                </div>

                {/* Review Content */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <MessageSquare className="h-5 w-5 mr-2 text-teal-600" />
                    Review Content
                  </h4>

                  {/* Rating */}
                  <div className="mb-4">
                    <p className="text-sm text-gray-500 mb-1">Rating</p>
                    <div className="flex items-center space-x-2">
                      {renderStars(selectedReview.rating)}
                      <span className="text-sm text-gray-600">({selectedReview.rating}/5)</span>
                    </div>
                  </div>

                  {/* Title */}
                  {selectedReview.title && (
                    <div className="mb-4">
                      <p className="text-sm text-gray-500 mb-1">Title</p>
                      <p className="font-medium text-gray-900">{selectedReview.title}</p>
                    </div>
                  )}

                  {/* Comment */}
                  <div className="mb-4">
                    <p className="text-sm text-gray-500 mb-1">Comment</p>
                    <div className="bg-white rounded-lg p-3 border border-gray-200">
                      <p className="text-gray-900 whitespace-pre-wrap">{selectedReview.comment}</p>
                    </div>
                  </div>

                  {/* Status and Metadata */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Status</p>
                      {getStatusBadge(selectedReview.status || 'pending')}
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Helpful Votes</p>
                      <p className="font-medium text-gray-900">{selectedReview.helpful_count || 0}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Verified Purchase</p>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        selectedReview.is_verified
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {selectedReview.is_verified ? 'Verified' : 'Not Verified'}
                      </span>
                    </div>
                  </div>

                  {/* Dates */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Created</p>
                      <p className="text-sm text-gray-900">{new Date(selectedReview.created_at).toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Last Updated</p>
                      <p className="text-sm text-gray-900">{new Date(selectedReview.updated_at).toLocaleString()}</p>
                    </div>
                  </div>
                </div>

                {/* Admin Replies Section */}
                <div className="bg-gray-50 rounded-lg p-4 mt-6">
                  <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <Reply className="h-5 w-5 mr-2 text-teal-600" />
                    Admin Replies
                  </h4>

                  {/* Existing Replies */}
                  {reviewReplies.length > 0 ? (
                    <div className="space-y-3 mb-4">
                      {reviewReplies.map((reply) => (
                        <div key={reply.id} className="bg-white rounded-lg p-3 border border-gray-200">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <span className="font-medium text-gray-900">{reply.admin?.full_name}</span>
                                <span className="text-xs text-gray-500">
                                  {new Date(reply.created_at).toLocaleString()}
                                </span>
                                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-teal-100 text-teal-800">
                                  {reply.admin?.role === 'admin' ? 'Admin' : reply.admin?.role === 'store_manager' ? 'Store Manager' : 'Staff'}
                                </span>
                              </div>
                              <p className="text-gray-700 whitespace-pre-wrap">{reply.reply_text}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm mb-4">No replies yet.</p>
                  )}

                  {/* Reply Form */}
                  <div className="border-t border-gray-200 pt-4">
                    <label htmlFor="detailsReplyText" className="block text-sm font-medium text-gray-700 mb-2">
                      Add Reply
                    </label>
                    <textarea
                      id="detailsReplyText"
                      value={detailsReplyText}
                      onChange={(e) => {
                        setDetailsReplyText(e.target.value);
                        setDetailsReplyError('');
                      }}
                      placeholder="Write your reply to this customer review..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                      rows={3}
                      maxLength={2000}
                    />
                    <div className="flex justify-between items-center mt-2">
                      <span className="text-sm text-gray-500">
                        {detailsReplyText.length}/2000 characters
                      </span>
                      {detailsReplyText.length < 10 && detailsReplyText.length > 0 && (
                        <span className="text-sm text-amber-600">
                          Minimum 10 characters required
                        </span>
                      )}
                    </div>

                    {detailsReplyError && (
                      <p className="mt-2 text-sm text-red-600">{detailsReplyError}</p>
                    )}

                    <button
                      onClick={handleDetailsReplySubmit}
                      disabled={detailsReplyLoading || detailsReplyText.trim().length < 10}
                      className="mt-3 bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center space-x-2"
                    >
                      {detailsReplyLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          <span>Submitting...</span>
                        </>
                      ) : (
                        <>
                          <Reply className="h-4 w-4" />
                          <span>Submit Reply</span>
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>

              {/* Modal Actions */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                {(selectedReview.status === 'pending' || !selectedReview.status) && (
                  <>
                    <button
                      onClick={() => {
                        handleStatusUpdate(selectedReview.id, 'approved');
                        setShowDetailsModal(false);
                      }}
                      className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
                    >
                      <CheckCircle className="h-4 w-4" />
                      <span>Approve</span>
                    </button>
                    <button
                      onClick={() => {
                        handleStatusUpdate(selectedReview.id, 'rejected');
                        setShowDetailsModal(false);
                      }}
                      className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
                    >
                      <XCircle className="h-4 w-4" />
                      <span>Reject</span>
                    </button>
                  </>
                )}
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reply Modal */}
      {showReplyModal && selectedReview && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              {/* Modal Header */}
              <div className="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Reply to Review</h3>
                <button
                  onClick={() => {
                    setShowReplyModal(false);
                    setReplyText('');
                    setReplyError('');
                    setSelectedReview(null);
                  }}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              {/* Review Context */}
              <div className="py-4 bg-gray-50 rounded-lg mt-4">
                <div className="flex items-start space-x-4">
                  {selectedReview.products?.featured_image ? (
                    <img
                      className="h-12 w-12 rounded-lg object-cover"
                      src={selectedReview.products.featured_image}
                      alt={selectedReview.products.title}
                    />
                  ) : (
                    <div className="h-12 w-12 bg-gray-200 rounded-lg flex items-center justify-center">
                      <Package className="h-6 w-6 text-gray-400" />
                    </div>
                  )}
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{selectedReview.products?.title}</p>
                    <p className="text-sm text-gray-600">by {selectedReview.users?.full_name}</p>
                    <div className="flex items-center mt-1">
                      {renderStars(selectedReview.rating)}
                      <span className="ml-2 text-sm text-gray-500">
                        {new Date(selectedReview.created_at).toLocaleDateString()}
                      </span>
                    </div>
                    <p className="text-sm text-gray-700 mt-2">{selectedReview.comment}</p>
                  </div>
                </div>
              </div>

              {/* Reply Form */}
              <div className="mt-6">
                <label htmlFor="replyText" className="block text-sm font-medium text-gray-700 mb-2">
                  Your Reply
                </label>
                <textarea
                  id="replyText"
                  value={replyText}
                  onChange={(e) => {
                    setReplyText(e.target.value);
                    setReplyError('');
                  }}
                  placeholder="Write your reply to this customer review..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                  rows={4}
                  maxLength={2000}
                />
                <div className="flex justify-between items-center mt-2">
                  <span className="text-sm text-gray-500">
                    {replyText.length}/2000 characters
                  </span>
                  {replyText.length < 10 && replyText.length > 0 && (
                    <span className="text-sm text-amber-600">
                      Minimum 10 characters required
                    </span>
                  )}
                </div>

                {replyError && (
                  <p className="mt-2 text-sm text-red-600">{replyError}</p>
                )}
              </div>

              {/* Modal Actions */}
              <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
                <button
                  onClick={() => {
                    setShowReplyModal(false);
                    setReplyText('');
                    setReplyError('');
                    setSelectedReview(null);
                  }}
                  className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
                  disabled={replyLoading}
                >
                  Cancel
                </button>
                <button
                  onClick={handleReplySubmit}
                  disabled={replyLoading || replyText.trim().length < 10}
                  className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {replyLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Submitting...</span>
                    </>
                  ) : (
                    <>
                      <Reply className="h-4 w-4" />
                      <span>Submit Reply</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReviewsManagementPage;
