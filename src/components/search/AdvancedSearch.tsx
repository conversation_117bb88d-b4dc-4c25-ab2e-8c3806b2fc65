
import { useState, useEffect } from 'react';
import { Search, Filter, X, Star, DollarSign, Package, Calendar } from 'lucide-react';
import { getActiveCategories } from '../../services/liveCategoryService';
import { liveDataService } from '../../services/liveDataService';

export interface SearchFilters {
  searchTerm: string;
  category: string;
  priceRange: [number, number];
  minRating: number;
  inStock: boolean;
  isNew: boolean;
  brand: string;
  sortBy: 'relevance' | 'price-low' | 'price-high' | 'rating' | 'newest' | 'popularity';
  availability: 'all' | 'in-stock' | 'low-stock' | 'out-of-stock';
  discount: boolean;
  freeShipping: boolean;
}

interface AdvancedSearchProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  categories: string[];
  brands: string[];
  priceRange: [number, number];
  isOpen: boolean;
  onClose: () => void;
}

const AdvancedSearch = ({ 
  filters, 
  onFiltersChange, 
  categories, 
  brands, 
  priceRange,
  isOpen,
  onClose 
}: AdvancedSearchProps) => {
  const [localFilters, setLocalFilters] = useState<SearchFilters>(filters);

  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
    onClose();
  };

  const handleResetFilters = () => {
    const resetFilters: SearchFilters = {
      searchTerm: '',
      category: '',
      priceRange: priceRange,
      minRating: 0,
      inStock: false,
      isNew: false,
      brand: '',
      sortBy: 'relevance',
      availability: 'all',
      discount: false,
      freeShipping: false
    };
    setLocalFilters(resetFilters);
    onFiltersChange(resetFilters);
  };

  const updateFilter = (key: keyof SearchFilters, value: any) => {
    setLocalFilters(prev => ({ ...prev, [key]: value }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center space-x-2">
              <Filter className="h-6 w-6 text-teal-600" />
              <h2 className="text-xl font-bold text-gray-900">Advanced Search & Filters</h2>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Search Term */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Search Keywords
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="text"
                    value={localFilters.searchTerm}
                    onChange={(e) => updateFilter('searchTerm', e.target.value)}
                    placeholder="Search products, brands, descriptions..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Category and Brand */}
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    value={localFilters.category}
                    onChange={(e) => updateFilter('category', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  >
                    <option value="">All Categories</option>
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Brand
                  </label>
                  <select
                    value={localFilters.brand}
                    onChange={(e) => updateFilter('brand', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  >
                    <option value="">All Brands</option>
                    {brands.map(brand => (
                      <option key={brand} value={brand}>{brand}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Price Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <DollarSign className="inline h-4 w-4 mr-1" />
                  Price Range (Dh)
                </label>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <input
                      type="number"
                      value={localFilters.priceRange[0]}
                      onChange={(e) => updateFilter('priceRange', [Number(e.target.value), localFilters.priceRange[1]])}
                      placeholder="Min price"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <input
                      type="number"
                      value={localFilters.priceRange[1]}
                      onChange={(e) => updateFilter('priceRange', [localFilters.priceRange[0], Number(e.target.value)])}
                      placeholder="Max price"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    />
                  </div>
                </div>
                <div className="mt-2 text-sm text-gray-500">
                  Range: {localFilters.priceRange[0]} Dh - {localFilters.priceRange[1]} Dh
                </div>
              </div>

              {/* Rating */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Rating
                </label>
                <div className="flex items-center space-x-2">
                  {[1, 2, 3, 4, 5].map(rating => (
                    <button
                      key={rating}
                      onClick={() => updateFilter('minRating', rating)}
                      className={`p-1 rounded ${
                        localFilters.minRating >= rating ? 'text-yellow-400' : 'text-gray-300'
                      }`}
                    >
                      <Star className="h-6 w-6 fill-current" />
                    </button>
                  ))}
                  <span className="text-sm text-gray-600 ml-2">
                    {localFilters.minRating > 0 ? `${localFilters.minRating}+ stars` : 'Any rating'}
                  </span>
                </div>
              </div>

              {/* Availability */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Package className="inline h-4 w-4 mr-1" />
                  Availability
                </label>
                <select
                  value={localFilters.availability}
                  onChange={(e) => updateFilter('availability', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="all">All Products</option>
                  <option value="in-stock">In Stock</option>
                  <option value="low-stock">Low Stock</option>
                  <option value="out-of-stock">Out of Stock</option>
                </select>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Filter Options */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Filter Options
                </label>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="inStock"
                      checked={localFilters.inStock}
                      onChange={(e) => updateFilter('inStock', e.target.checked)}
                      className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                    />
                    <label htmlFor="inStock" className="ml-2 text-sm text-gray-700">
                      In stock only
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isNew"
                      checked={localFilters.isNew}
                      onChange={(e) => updateFilter('isNew', e.target.checked)}
                      className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isNew" className="ml-2 text-sm text-gray-700">
                      <Calendar className="inline h-4 w-4 mr-1" />
                      New products only
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="discount"
                      checked={localFilters.discount}
                      onChange={(e) => updateFilter('discount', e.target.checked)}
                      className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                    />
                    <label htmlFor="discount" className="ml-2 text-sm text-gray-700">
                      On sale / Discounted
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="freeShipping"
                      checked={localFilters.freeShipping}
                      onChange={(e) => updateFilter('freeShipping', e.target.checked)}
                      className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                    />
                    <label htmlFor="freeShipping" className="ml-2 text-sm text-gray-700">
                      Free shipping
                    </label>
                  </div>
                </div>
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sort By
                </label>
                <select
                  value={localFilters.sortBy}
                  onChange={(e) => updateFilter('sortBy', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="relevance">Relevance</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="rating">Highest Rated</option>
                  <option value="newest">Newest First</option>
                  <option value="popularity">Most Popular</option>
                </select>
              </div>

              {/* Quick Filters */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Quick Filters
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    { label: 'Under 50 Dh', action: () => updateFilter('priceRange', [0, 50]) },
                    { label: '50-200 Dh', action: () => updateFilter('priceRange', [50, 200]) },
                    { label: '200-500 Dh', action: () => updateFilter('priceRange', [200, 500]) },
                    { label: 'Over 500 Dh', action: () => updateFilter('priceRange', [500, 9999]) },
                    { label: '4+ Stars', action: () => updateFilter('minRating', 4) },
                    { label: '5 Stars', action: () => updateFilter('minRating', 5) }
                  ].map((filter, index) => (
                    <button
                      key={index}
                      onClick={filter.action}
                      className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      {filter.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-6 border-t border-gray-200 mt-8">
            <button
              onClick={handleResetFilters}
              className="flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Reset All Filters
            </button>
            <button
              onClick={handleApplyFilters}
              className="flex-1 py-3 px-4 bg-gradient-to-r from-teal-600 to-teal-700 text-white rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedSearch;
