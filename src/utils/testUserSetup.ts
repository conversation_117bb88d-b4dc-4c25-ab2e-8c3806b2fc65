import { supabase } from '@/integrations/supabase/client';

// Development utility to create a test admin user
export const createTestAdminUser = async () => {
  try {
    console.log('Creating test admin user...');
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('No authenticated user found:', authError);
      return false;
    }

    console.log('Current user ID:', user.id);
    console.log('Current user email:', user.email);

    // Check if user exists in users table
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('id, email, full_name')
      .eq('id', user.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking user:', checkError);
      // Try to create user record anyway
    }

    if (existingUser) {
      console.log('User already exists:', existingUser);
    } else {
      // Create new user record (without role column for now)
      const { error: insertError } = await supabase
        .from('users')
        .insert({
          id: user.id,
          email: user.email,
          full_name: user.email?.split('@')[0] || 'Test User'
        });

      if (insertError) {
        console.error('Error creating user record:', insertError);
        console.log('This is normal if the user record already exists or if the users table has different columns');
      } else {
        console.log('Created new user record');
      }
    }

    return true;
  } catch (error) {
    console.error('Error in createTestAdminUser:', error);
    return false;
  }
};

// Development utility to check current user permissions
export const checkCurrentUserPermissions = async () => {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('No authenticated user');
      return null;
    }

    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, full_name')
      .eq('id', user.id)
      .single();

    if (userError) {
      console.error('Error fetching user data:', userError);
      console.log('This is normal if the users table has different columns or the user record doesn\'t exist');
    }

    console.log('Current user permissions:', {
      id: user.id,
      email: user.email,
      full_name: userData?.full_name,
      can_reply: true, // In development mode, all users can reply
      note: 'Role-based permissions disabled for development'
    });

    return userData;
  } catch (error) {
    console.error('Error checking permissions:', error);
    return null;
  }
};
