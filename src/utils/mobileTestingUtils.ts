/**
 * Mobile Testing and Performance Utilities for YalaOffice
 * Provides tools for testing mobile responsiveness, touch interactions, and performance
 */

// Mobile device detection and testing utilities
export const mobileTestingUtils = {
  // Device detection
  detectDevice: () => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const userAgent = navigator.userAgent;
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const pixelRatio = window.devicePixelRatio || 1;

    return {
      width,
      height,
      orientation: height > width ? 'portrait' : 'landscape',
      isTouch,
      pixelRatio,
      userAgent,
      deviceType: width < 480 ? 'small-mobile' : 
                  width < 768 ? 'mobile' : 
                  width < 1024 ? 'tablet' : 'desktop',
      isHighDPI: pixelRatio > 1.5
    };
  },

  // Touch target validation
  validateTouchTargets: () => {
    const minTouchSize = 44; // WCAG AA requirement
    const elements = document.querySelectorAll('button, [role="button"], input[type="submit"], input[type="button"], a');
    const violations: Array<{element: Element, size: {width: number, height: number}}> = [];

    elements.forEach(element => {
      const rect = element.getBoundingClientRect();
      if (rect.width < minTouchSize || rect.height < minTouchSize) {
        violations.push({
          element,
          size: { width: rect.width, height: rect.height }
        });
      }
    });

    return {
      passed: violations.length === 0,
      violations,
      totalElements: elements.length,
      message: violations.length === 0 
        ? `✅ All ${elements.length} interactive elements meet 44px minimum touch target requirement`
        : `❌ ${violations.length} elements below 44px minimum touch target`
    };
  },

  // Performance monitoring
  measurePerformance: () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');
    
    const metrics = {
      // Core Web Vitals
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      
      // Paint metrics
      firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
      firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
      
      // Network metrics
      dnsLookup: navigation.domainLookupEnd - navigation.domainLookupStart,
      tcpConnect: navigation.connectEnd - navigation.connectStart,
      serverResponse: navigation.responseEnd - navigation.requestStart,
      
      // Resource loading
      totalLoadTime: navigation.loadEventEnd - navigation.navigationStart
    };

    return {
      metrics,
      assessment: {
        loadTime: metrics.totalLoadTime < 3000 ? '✅ Good' : metrics.totalLoadTime < 5000 ? '⚠️ Needs Improvement' : '❌ Poor',
        firstPaint: metrics.firstPaint < 1000 ? '✅ Good' : metrics.firstPaint < 2000 ? '⚠️ Needs Improvement' : '❌ Poor',
        domReady: metrics.domContentLoaded < 1500 ? '✅ Good' : metrics.domContentLoaded < 3000 ? '⚠️ Needs Improvement' : '❌ Poor'
      }
    };
  },

  // Responsive breakpoint testing
  testBreakpoints: () => {
    const breakpoints = {
      'xs': 0,
      'sm': 480,
      'md': 768,
      'lg': 1024,
      'xl': 1280,
      '2xl': 1536
    };

    const currentWidth = window.innerWidth;
    const currentBreakpoint = Object.entries(breakpoints)
      .reverse()
      .find(([_, width]) => currentWidth >= width)?.[0] || 'xs';

    return {
      currentWidth,
      currentBreakpoint,
      breakpoints,
      isResponsive: true // Could add more sophisticated testing here
    };
  },

  // Real-time sync testing
  testRealTimeSync: async () => {
    const startTime = performance.now();
    let syncWorking = false;
    
    try {
      // Test if we can connect to Supabase real-time
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        import.meta.env.VITE_SUPABASE_URL || '',
        import.meta.env.VITE_SUPABASE_ANON_KEY || ''
      );
      
      // Test connection
      const { data, error } = await supabase.from('products').select('count').limit(1);
      syncWorking = !error;
      
    } catch (error) {
      console.error('Real-time sync test failed:', error);
    }

    const endTime = performance.now();
    const responseTime = endTime - startTime;

    return {
      working: syncWorking,
      responseTime,
      assessment: syncWorking ? '✅ Real-time sync working' : '❌ Real-time sync failed',
      performance: responseTime < 500 ? '✅ Fast' : responseTime < 1000 ? '⚠️ Moderate' : '❌ Slow'
    };
  },

  // Accessibility testing
  testAccessibility: () => {
    const issues: string[] = [];
    
    // Check for proper viewport meta tag
    const viewport = document.querySelector('meta[name="viewport"]');
    if (!viewport || !viewport.getAttribute('content')?.includes('width=device-width')) {
      issues.push('Missing or incorrect viewport meta tag');
    }

    // Check for proper heading hierarchy
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let lastLevel = 0;
    headings.forEach(heading => {
      const level = parseInt(heading.tagName.charAt(1));
      if (level > lastLevel + 1) {
        issues.push(`Heading hierarchy skip detected: ${heading.tagName} after h${lastLevel}`);
      }
      lastLevel = level;
    });

    // Check for alt text on images
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (!img.getAttribute('alt')) {
        issues.push('Image missing alt text');
      }
    });

    // Check for proper form labels
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
      const id = input.getAttribute('id');
      const label = id ? document.querySelector(`label[for="${id}"]`) : null;
      const ariaLabel = input.getAttribute('aria-label');
      
      if (!label && !ariaLabel) {
        issues.push('Form input missing label or aria-label');
      }
    });

    return {
      passed: issues.length === 0,
      issues,
      message: issues.length === 0 ? '✅ No accessibility issues found' : `❌ ${issues.length} accessibility issues found`
    };
  },

  // Comprehensive mobile test suite
  runFullMobileTest: async () => {
    console.log('🧪 Running YalaOffice Mobile Test Suite...\n');
    
    const device = mobileTestingUtils.detectDevice();
    console.log('📱 Device Info:', device);
    
    const touchTargets = mobileTestingUtils.validateTouchTargets();
    console.log('👆 Touch Targets:', touchTargets.message);
    
    const performance = mobileTestingUtils.measurePerformance();
    console.log('⚡ Performance:', performance.assessment);
    
    const breakpoints = mobileTestingUtils.testBreakpoints();
    console.log('📐 Responsive:', `Current: ${breakpoints.currentBreakpoint} (${breakpoints.currentWidth}px)`);
    
    const realTimeSync = await mobileTestingUtils.testRealTimeSync();
    console.log('🔄 Real-time Sync:', realTimeSync.assessment);
    
    const accessibility = mobileTestingUtils.testAccessibility();
    console.log('♿ Accessibility:', accessibility.message);
    
    const overallScore = [
      touchTargets.passed,
      performance.assessment.loadTime.includes('✅'),
      realTimeSync.working,
      accessibility.passed
    ].filter(Boolean).length;
    
    console.log(`\n🎯 Overall Score: ${overallScore}/4 tests passed`);
    
    return {
      device,
      touchTargets,
      performance,
      breakpoints,
      realTimeSync,
      accessibility,
      overallScore,
      passed: overallScore >= 3
    };
  }
};

// Export individual testing functions
export const {
  detectDevice,
  validateTouchTargets,
  measurePerformance,
  testBreakpoints,
  testRealTimeSync,
  testAccessibility,
  runFullMobileTest
} = mobileTestingUtils;

// Global testing function for browser console
if (typeof window !== 'undefined') {
  (window as any).testYalaMobile = runFullMobileTest;
}

export default mobileTestingUtils;
