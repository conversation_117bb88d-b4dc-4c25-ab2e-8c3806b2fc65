-- YalaOffice: Fix Multiple Reviews Issue
-- Run this script in your Supabase SQL Editor to allow multiple reviews per user per product

-- Step 1: Remove the unique constraint that prevents multiple reviews
DO $$ 
BEGIN
    -- Drop the unique constraint if it exists
    IF EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints 
        WHERE constraint_name = 'product_reviews_product_id_customer_id_key' 
        AND table_name = 'product_reviews'
    ) THEN
        ALTER TABLE product_reviews DROP CONSTRAINT product_reviews_product_id_customer_id_key;
        RAISE NOTICE 'SUCCESS: Unique constraint removed - customers can now submit multiple reviews per product';
    ELSE
        RAISE NOTICE 'INFO: Unique constraint does not exist - no action needed';
    END IF;
END $$;

-- Step 2: Create performance indexes to replace the unique constraint
CREATE INDEX IF NOT EXISTS idx_product_reviews_product_customer ON product_reviews(product_id, customer_id);
CREATE INDEX IF NOT EXISTS idx_product_reviews_customer_created ON product_reviews(customer_id, created_at DESC);

-- Step 3: Add documentation comments
COMMENT ON TABLE product_reviews IS 'Product reviews table - allows multiple reviews per user per product for comprehensive feedback';
COMMENT ON INDEX idx_product_reviews_product_customer IS 'Index for efficient querying of reviews by product and customer';
COMMENT ON INDEX idx_product_reviews_customer_created IS 'Index for efficient querying of customer reviews by date';

-- Step 4: Verify the change
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 
            FROM information_schema.table_constraints 
            WHERE constraint_name = 'product_reviews_product_id_customer_id_key' 
            AND table_name = 'product_reviews'
        ) 
        THEN 'ERROR: Unique constraint still exists'
        ELSE 'SUCCESS: Multiple reviews per user are now allowed'
    END as status;

-- Display current table structure for verification
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'product_reviews' 
ORDER BY ordinal_position;
